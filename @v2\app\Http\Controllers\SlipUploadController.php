<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use TomatoPHP\FilamentPayments\Models\Payment;
use App\Services\PaymentDrivers\BankTransferDriver;
use Illuminate\Support\Facades\Storage;

class SlipUploadController extends Controller
{
    public function upload(Request $request, $paymentUuid)
    {
        $request->validate([
            'slip_image' => 'required|image|mimes:jpeg,png,jpg|max:5120', // 5MB max
        ]);

        $payment = Payment::where('uuid', $paymentUuid)->firstOrFail();

        // Check if payment is still pending
        if ($payment->status !== 'pending') {
            return redirect()->back()->with('error', 'This payment has already been processed.');
        }

        try {
            // Store the uploaded slip
            $slipPath = $request->file('slip_image')->store('payment-slips', 'public');
            $fullPath = Storage::disk('public')->path($slipPath);

            // Verify slip using SlipOK API
            $driver = new BankTransferDriver();
            $result = $driver->verifySlip($payment, $fullPath);

            if ($result['success']) {
                // Store slip path in payment
                $payment->update([
                    'slip_image_path' => $slipPath,
                ]);

                return redirect()->route('payment.bank-transfer.success', $payment->uuid)
                    ->with('success', 'Payment verified successfully! Your subscription has been activated.');
            } else {
                // Store slip for manual review
                $payment->update([
                    'slip_image_path' => $slipPath,
                    'verification_notes' => $result['message'],
                ]);

                return redirect()->back()->with('error', 'Slip verification failed: ' . $result['message'] . '. Your slip has been submitted for manual review.');
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error processing slip: ' . $e->getMessage());
        }
    }

    public function success($paymentUuid)
    {
        $payment = Payment::where('uuid', $paymentUuid)->firstOrFail();
        
        return view('payment.bank-transfer-success', compact('payment'));
    }
}
