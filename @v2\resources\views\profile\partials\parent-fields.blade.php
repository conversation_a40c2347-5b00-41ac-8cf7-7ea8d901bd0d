<!-- Parent-specific Fields -->

<!-- Professional Information -->
<div class="form-section">
    <h2 class="section-title">Professional Information</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="occupation" class="form-label required">Occupation</label>
            <input type="text" id="occupation" name="occupation" class="form-input" 
                   value="{{ old('occupation', $profile->occupation) }}" 
                   placeholder="Your job title or profession" required>
            @error('occupation')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="workplace" class="form-label">Workplace</label>
            <input type="text" id="workplace" name="workplace" class="form-input" 
                   value="{{ old('workplace', $profile->workplace) }}" 
                   placeholder="Company or organization name">
            @error('workplace')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<!-- Children Information -->
<div class="form-section">
    <h2 class="section-title">Children Information</h2>
    <div class="form-grid">
        <div class="form-group" style="grid-column: 1 / -1;">
            <label for="children_ids" class="form-label">Children's Student IDs</label>
            <div class="children-container">
                @php
                    $childrenIds = old('children_ids', $profile->children_ids ?? []);
                @endphp
                
                @if(is_array($childrenIds) && count($childrenIds) > 0)
                    @foreach($childrenIds as $index => $childId)
                        <div class="child-input-group mb-2">
                            <input type="text" name="children_ids[]" class="form-input" 
                                   value="{{ $childId }}" 
                                   placeholder="Enter student ID">
                            <button type="button" class="btn btn-secondary remove-child" onclick="removeChild(this)">Remove</button>
                        </div>
                    @endforeach
                @else
                    <div class="child-input-group mb-2">
                        <input type="text" name="children_ids[]" class="form-input" 
                               placeholder="Enter student ID">
                        <button type="button" class="btn btn-secondary remove-child" onclick="removeChild(this)">Remove</button>
                    </div>
                @endif
                
                <button type="button" class="btn btn-outline" onclick="addChild()">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Another Child
                </button>
            </div>
            @error('children_ids')
                <div class="error-message">{{ $message }}</div>
            @enderror
            @error('children_ids.*')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<script>
function addChild() {
    const container = document.querySelector('.children-container');
    const addButton = container.querySelector('.btn-outline');
    
    const childGroup = document.createElement('div');
    childGroup.className = 'child-input-group mb-2';
    childGroup.innerHTML = `
        <input type="text" name="children_ids[]" class="form-input" placeholder="Enter student ID">
        <button type="button" class="btn btn-secondary remove-child" onclick="removeChild(this)">Remove</button>
    `;
    
    container.insertBefore(childGroup, addButton);
}

function removeChild(button) {
    const container = document.querySelector('.children-container');
    const childGroups = container.querySelectorAll('.child-input-group');
    
    // Keep at least one input
    if (childGroups.length > 1) {
        button.parentElement.remove();
    } else {
        // Clear the input instead of removing it
        const input = button.parentElement.querySelector('input');
        input.value = '';
    }
}
</script>

<style>
.child-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.child-input-group .form-input {
    flex: 1;
    margin-bottom: 0;
}

.child-input-group .btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.children-container .btn-outline {
    margin-top: 1rem;
}
</style>
