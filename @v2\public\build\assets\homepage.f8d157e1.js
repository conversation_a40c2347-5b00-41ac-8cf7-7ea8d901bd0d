document.addEventListener("DOMContentLoaded",function(){const c=document.querySelectorAll(".faq-item");c.forEach(t=>{const e=t.querySelector(".faq-question"),n=t.querySelector(".faq-answer"),o=t.querySelector(".faq-icon");e&&n&&o&&e.addEventListener("click",function(){const s=n.classList.contains("active");c.forEach(r=>{const f=r.querySelector(".faq-answer"),h=r.querySelector(".faq-icon");f&&h&&(f.classList.remove("active"),h.classList.remove("active"))}),s||(n.classList.add("active"),o.classList.add("active"))})}),document.querySelectorAll('a[href^="#"]').forEach(t=>{t.addEventListener("click",function(e){e.preventDefault();const n=this.getAttribute("href").substring(1),o=document.getElementById(n);o&&(window.LayoutUtils&&window.LayoutUtils.hideLoading(),o.scrollIntoView({behavior:"smooth",block:"start"}),window.LayoutUtils&&window.LayoutUtils.closeMobileMenu())})});const m=document.querySelectorAll(".counter-value"),g=t=>{const e=parseInt(t.getAttribute("data-target")),o=e/(2e3/16);let s=0;const r=()=>{s+=o,s<e?(t.textContent=Math.floor(s).toLocaleString(),requestAnimationFrame(r)):t.textContent=e.toLocaleString()};r()},i=new IntersectionObserver(t=>{t.forEach(e=>{e.isIntersecting&&(g(e.target),i.unobserve(e.target))})},{threshold:.5});m.forEach(t=>{i.observe(t)});const a=new IntersectionObserver(t=>{t.forEach(e=>{e.isIntersecting&&(e.target.classList.add("fade-in"),a.unobserve(e.target))})},{threshold:.1});document.querySelectorAll(".fade-on-scroll").forEach(t=>{a.observe(t)});const l=document.getElementById("newsletter-form");l&&l.addEventListener("submit",function(t){t.preventDefault(),this.querySelector('input[type="email"]').value;const e=this.querySelector('button[type="submit"]'),n=e.textContent;e.textContent="\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E48\u0E07...",e.disabled=!0,setTimeout(()=>{alert("\u0E02\u0E2D\u0E1A\u0E04\u0E38\u0E13\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E01\u0E32\u0E23\u0E2A\u0E21\u0E31\u0E04\u0E23\u0E23\u0E31\u0E1A\u0E02\u0E48\u0E32\u0E27\u0E2A\u0E32\u0E23!"),this.reset(),e.textContent=n,e.disabled=!1},1e3)});const d=document.getElementById("pricing-toggle");d&&d.addEventListener("change",function(){const t=document.querySelectorAll(".monthly-price"),e=document.querySelectorAll(".yearly-price");this.checked?(t.forEach(n=>n.classList.add("hidden")),e.forEach(n=>n.classList.remove("hidden"))):(t.forEach(n=>n.classList.remove("hidden")),e.forEach(n=>n.classList.add("hidden")))});const v=document.querySelectorAll("img[data-src]"),u=new IntersectionObserver(t=>{t.forEach(e=>{if(e.isIntersecting){const n=e.target;n.src=n.dataset.src,n.classList.remove("opacity-0"),n.classList.add("opacity-100"),u.unobserve(n)}})});v.forEach(t=>{u.observe(t)})});
