<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-cursor-arrow-rays class="w-5 h-5 text-primary-600 mr-2" />
                🎯 แทบฝึกประจำวัน
            </div>
        </x-slot>

        @php
            $progress = $this->getCurrentProgress();
        @endphp

        <!-- Progress Overview -->
        <div class="mb-6">
            <div class="flex justify-between items-center mb-3">
                <div>
                    <span class="text-sm text-gray-500 dark:text-gray-400">ความก้าวหน้า</span>
                    <div class="text-xl font-bold text-primary-600 dark:text-primary-400">{{ $progress['current'] }}/{{ $progress['total'] }} ข้อ</div>
                </div>
                <div class="bg-primary-100 dark:bg-primary-900/50 text-primary-600 dark:text-primary-300 px-3 py-1 rounded-full text-sm font-medium">
                    วิชา: {{ $progress['subject'] }}
                </div>
            </div>
            
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
                <div class="bg-primary-500 h-3 rounded-full transition-all duration-500" style="width: {{ $progress['percentage'] }}%"></div>
            </div>
        </div>

        <!-- Practice Items -->
        <div class="space-y-3 mb-6">
            @foreach($this->getPracticeItems() as $item)
                <div class="p-3 rounded-lg flex justify-between items-center transition-all duration-200 hover:scale-[1.02]
                    @if($item['status'] === 'completed') bg-green-100 dark:bg-green-900/20
                    @elseif($item['status'] === 'in_progress') bg-primary-50 dark:bg-primary-900/20
                    @else bg-gray-50 dark:bg-gray-800
                    @endif
                    @if($item['locked']) opacity-70 relative
                    @endif">
                    
                    <div class="flex items-center flex-1">
                        <div class="p-2 rounded-lg mr-3
                            @if($item['status'] === 'completed') bg-green-200 dark:bg-green-900/50
                            @elseif($item['status'] === 'in_progress') bg-primary-100 dark:bg-primary-900/50
                            @else bg-gray-200 dark:bg-gray-700
                            @endif">
                            @svg($item['icon'], 'w-5 h-5 ' . 
                                ($item['status'] === 'completed' ? 'text-green-600 dark:text-green-400' : 
                                ($item['status'] === 'in_progress' ? 'text-primary-600 dark:text-primary-400' : 
                                'text-gray-500 dark:text-gray-400')))
                        </div>
                        <div>
                            <div class="font-medium
                                @if($item['status'] === 'completed') text-green-800 dark:text-green-200
                                @elseif($item['status'] === 'in_progress') text-primary-800 dark:text-primary-200
                                @else text-gray-700 dark:text-gray-300
                                @endif">
                                {{ $item['title'] }}
                            </div>
                            <div class="text-sm
                                @if($item['status'] === 'completed') text-green-600 dark:text-green-400
                                @elseif($item['status'] === 'in_progress') text-primary-600 dark:text-primary-400
                                @else text-gray-500 dark:text-gray-400
                                @endif">
                                {{ $item['status_label'] }}
                            </div>
                        </div>
                    </div>
                    
                    @if($item['status'] === 'in_progress')
                        <button class="px-3 py-1 bg-primary-600 hover:bg-primary-700 text-white text-sm rounded-full transition-colors">
                            ทำต่อ
                        </button>
                    @else
                        <div class="text-{{ $item['status_color'] }}-600 dark:text-{{ $item['status_color'] }}-400 font-medium">
                            {{ $item['progress'] }}%
                        </div>
                    @endif
                    
                    @if($item['locked'])
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-2xl">🔒</span>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        <!-- Recommended Resources -->
        <div>
            <h4 class="font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                <x-heroicon-o-book-open class="w-4 h-4 text-primary-600 mr-2" />
                📚 สื่อแนะนำสำหรับคุณ
            </h4>
            
            <div class="space-y-2">
                @foreach($this->getRecommendedResources() as $resource)
                    <div class="flex items-start p-3 rounded-lg cursor-pointer transition-colors
                        @if($resource['color'] === 'blue') bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30
                        @elseif($resource['color'] === 'green') bg-green-50 hover:bg-green-100 dark:bg-green-900/20 dark:hover:bg-green-900/30
                        @elseif($resource['color'] === 'purple') bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:hover:bg-purple-900/30
                        @elseif($resource['color'] === 'yellow') bg-yellow-50 hover:bg-yellow-100 dark:bg-yellow-900/20 dark:hover:bg-yellow-900/30
                        @endif">
                        
                        <div class="p-2 rounded-lg mr-3
                            @if($resource['color'] === 'blue') bg-blue-100 dark:bg-blue-900/50
                            @elseif($resource['color'] === 'green') bg-green-100 dark:bg-green-900/50
                            @elseif($resource['color'] === 'purple') bg-purple-100 dark:bg-purple-900/50
                            @elseif($resource['color'] === 'yellow') bg-yellow-100 dark:bg-yellow-900/50
                            @endif">
                            @if($resource['icon'] === 'heroicon-o-video-camera')
                                <x-heroicon-o-video-camera class="w-5 h-5 text-{{ $resource['color'] }}-600 dark:text-{{ $resource['color'] }}-400" />
                            @elseif($resource['icon'] === 'heroicon-o-document-text')
                                <x-heroicon-o-document-text class="w-5 h-5 text-{{ $resource['color'] }}-600 dark:text-{{ $resource['color'] }}-400" />
                            @elseif($resource['icon'] === 'heroicon-o-puzzle-piece')
                                <x-heroicon-o-puzzle-piece class="w-5 h-5 text-{{ $resource['color'] }}-600 dark:text-{{ $resource['color'] }}-400" />
                            @elseif($resource['icon'] === 'heroicon-o-light-bulb')
                                <x-heroicon-o-light-bulb class="w-5 h-5 text-{{ $resource['color'] }}-600 dark:text-{{ $resource['color'] }}-400" />
                            @else
                                <x-heroicon-o-document class="w-5 h-5 text-{{ $resource['color'] }}-600 dark:text-{{ $resource['color'] }}-400" />
                            @endif
                        </div>
                        
                        <div>
                            <h5 class="font-medium text-{{ $resource['color'] }}-800 dark:text-{{ $resource['color'] }}-200">
                                {{ $resource['title'] }}
                            </h5>
                            <p class="text-sm text-{{ $resource['color'] }}-600 dark:text-{{ $resource['color'] }}-400">
                                {{ $resource['description'] }}
                            </p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
