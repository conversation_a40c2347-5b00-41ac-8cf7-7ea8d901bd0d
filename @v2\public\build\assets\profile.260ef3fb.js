document.addEventListener("DOMContentLoaded",function(){p(),v(),h(),b(),g(),y();function p(){const t=document.getElementById("profileCard");t&&setTimeout(()=>{t.classList.add("loaded")},100)}function v(){const t=document.getElementById("profileForm");if(!t)return;t.querySelectorAll(".form-input, .form-select, .form-textarea").forEach(e=>{e.addEventListener("blur",r),e.addEventListener("input",l)});function r(e){const n=e.target,i=n.value.trim(),w=n.name,m=n.hasAttribute("required")||n.classList.contains("required");if(u(n),m&&!i){o(n,"This field is required");return}switch(w){case"email":i&&!f(i)?o(n,"Please enter a valid email address"):i&&a(n);break;case"phone_number":case"emergency_contact_phone":case"school_phone":i&&!c(i)?o(n,"Please enter a valid phone number"):i&&a(n);break;case"date_of_birth":i&&!d(i)?o(n,"Please enter a valid date"):i&&a(n);break;case"years_of_experience":i&&(isNaN(i)||i<0||i>50)?o(n,"Please enter a valid number of years (0-50)"):i&&a(n);break;case"total_students":case"total_teachers":i&&(isNaN(i)||i<0)?o(n,"Please enter a valid number"):i&&a(n);break;default:i&&m&&a(n);break}}function l(e){const n=e.target;n.classList.contains("error")&&u(n)}function o(e,n){e.classList.add("error"),e.classList.remove("success");const i=document.createElement("div");i.className="error-message",i.innerHTML=`
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${n}
            `,e.parentNode.appendChild(i)}function a(e){e.classList.add("success"),e.classList.remove("error")}function u(e){e.classList.remove("error","success");const n=e.parentNode.querySelector(".error-message");n&&n.remove()}function f(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function c(e){return/^[\d\s\-\+\(\)]{10,}$/.test(e)}function d(e){const n=new Date(e);return n instanceof Date&&!isNaN(n)&&n<new Date}}function h(){const t=document.getElementById("profile_image"),s=document.querySelector(".file-upload-label"),r=document.querySelector(".profile-avatar");!t||!s||t.addEventListener("change",function(l){const o=l.target.files[0];if(o){if(!o.type.startsWith("image/")){window.LayoutUtils&&window.LayoutUtils.showNotification("Please select an image file","error");return}if(o.size>2*1024*1024){window.LayoutUtils&&window.LayoutUtils.showNotification("Image size must be less than 2MB","error");return}if(s.innerHTML=`
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    ${o.name}
                `,r){const a=new FileReader;a.onload=function(u){r.src=u.target.result},a.readAsDataURL(o)}}})}function b(){const t=document.querySelector(".subjects-container"),s=document.querySelector(".subject-input");if(!t||!s)return;let r=[];document.querySelectorAll(".subject-tag").forEach(c=>{const d=c.textContent.trim().replace("\xD7","");d&&r.push(d)}),s.addEventListener("keydown",function(c){(c.key==="Enter"||c.key===",")&&(c.preventDefault(),o())}),s.addEventListener("blur",o);function o(){const c=s.value.trim();c&&!r.includes(c)&&(r.push(c),u(),s.value="",f())}function a(c){r=r.filter(d=>d!==c),u(),f()}function u(){const c=r.map(e=>`
                <span class="subject-tag">
                    ${e}
                    <button type="button" class="subject-remove" onclick="removeSubject('${e}')">\xD7</button>
                </span>
            `).join("");t.innerHTML=c+`
                <input type="text" class="subject-input" placeholder="Add subject and press Enter">
            `;const d=t.querySelector(".subject-input");d.addEventListener("keydown",function(e){(e.key==="Enter"||e.key===",")&&(e.preventDefault(),o())}),d.addEventListener("blur",o)}function f(){document.querySelectorAll('input[name="subjects[]"]').forEach(d=>d.remove()),r.forEach(d=>{const e=document.createElement("input");e.type="hidden",e.name="subjects[]",e.value=d,t.appendChild(e)})}window.removeSubject=a}function g(){const t=document.getElementById("profileForm"),s=document.getElementById("submitBtn");if(!t||!s)return;t.addEventListener("submit",function(l){if(!r())return l.preventDefault(),!1;E(s,"Saving Profile...")});function r(){const l=t.querySelectorAll(".form-input[required], .form-select[required], .form-textarea[required]");let o=!0;return l.forEach(a=>{const u=new Event("blur");a.dispatchEvent(u),(a.classList.contains("error")||!a.value.trim())&&(o=!1)}),o}}function y(){const t=document.querySelector(".progress-fill"),s=document.querySelector(".progress-percentage");if(!t||!s)return;const r=document.getElementById("profileForm");if(!r)return;const l=r.querySelectorAll(".form-input, .form-select, .form-textarea"),o=r.querySelectorAll(".form-input[required], .form-select[required], .form-textarea[required]");function a(){let u=0;o.forEach(c=>{c.value.trim()&&u++});const f=Math.round(u/o.length*100);t.style.width=f+"%",s.textContent=f+"%"}l.forEach(u=>{u.addEventListener("input",a)}),a()}function E(t,s){t.disabled=!0,t.innerHTML=`
            <div class="loading">
                <div class="loading-spinner"></div>
                ${s}
            </div>
        `}window.skipProfile=function(){if(confirm("Are you sure you want to skip profile completion? You can complete it later from your dashboard.")){const t=document.createElement("form");t.method="POST",t.action="/profile/skip";const s=document.querySelector('meta[name="csrf-token"]');if(s){const r=document.createElement("input");r.type="hidden",r.name="_token",r.value=s.getAttribute("content"),t.appendChild(r)}document.body.appendChild(t),t.submit()}},window.disconnectSocialAccount=function(t,s){if(confirm(`Are you sure you want to disconnect your ${s} account? You will no longer be able to login using ${s}.`)){const r=document.createElement("form");r.method="POST",r.action=`/profile/disconnect/${t}`;const l=document.querySelector('meta[name="csrf-token"]');if(l){const o=document.createElement("input");o.type="hidden",o.name="_token",o.value=l.getAttribute("content"),r.appendChild(o)}document.body.appendChild(r),r.submit()}}});
