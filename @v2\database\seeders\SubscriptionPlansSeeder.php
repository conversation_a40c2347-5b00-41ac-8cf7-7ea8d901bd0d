<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use TomatoPHP\FilamentSubscriptions\Models\Plan;

class SubscriptionPlansSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            // Individual Student Plan
            [
                'name' => ['en' => 'Individual Student'],
                'slug' => 'individual-student',
                'description' => ['en' => 'Perfect for individual students who want access to all courses and features'],
                'price' => 29.99,
                'currency' => 'USD',
                'trial_period' => 14,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 1,
                'is_active' => true,
                'features' => [
                    'Access to all courses',
                    'Unlimited live classes',
                    'Exercise and exam access',
                    'Progress tracking',
                    '1 parent account included',
                    'Priority support',
                ],
            ],

            // Individual Teacher Plan
            [
                'name' => ['en' => 'Individual Teacher'],
                'slug' => 'individual-teacher',
                'description' => ['en' => 'For teachers who want to create and manage their own courses'],
                'price' => 49.99,
                'currency' => 'USD',
                'trial_period' => 14,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 2,
                'is_active' => true,
                'features' => [
                    'Create unlimited courses',
                    'Host live classes',
                    'Create exercises and exams',
                    'Student progress analytics',
                    'Up to 50 students',
                    'Priority support',
                ],
            ],

            // School Basic Plan
            [
                'name' => ['en' => 'School Basic'],
                'slug' => 'school-basic',
                'description' => ['en' => 'Perfect for small schools with up to 100 students'],
                'price' => 199.99,
                'currency' => 'USD',
                'trial_period' => 30,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 3,
                'is_active' => true,
                'features' => [
                    'Up to 100 students',
                    '100 parent accounts (1 per student)',
                    'Unlimited teachers',
                    'All course features',
                    'School management dashboard',
                    'Advanced analytics',
                    'Priority support',
                ],
            ],

            // School Premium Plan (for schools with more than 100 students)
            [
                'name' => ['en' => 'School Premium'],
                'slug' => 'school-premium',
                'description' => ['en' => 'For larger schools that need more than 100 students'],
                'price' => 399.99,
                'currency' => 'USD',
                'trial_period' => 30,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 4,
                'is_active' => true,
                'features' => [
                    'Up to 500 students',
                    '500 parent accounts (1 per student)',
                    'Unlimited teachers',
                    'All course features',
                    'School management dashboard',
                    'Advanced analytics',
                    'Custom branding',
                    'API access',
                    'Dedicated support manager',
                ],
            ],

            // Additional User Packs for Schools
            [
                'name' => ['en' => 'Additional Students Pack (50 users)'],
                'slug' => 'additional-students-50',
                'description' => ['en' => 'Add 50 more students to your school plan (includes 50 parent accounts)'],
                'price' => 99.99,
                'currency' => 'USD',
                'trial_period' => 0,
                'trial_interval' => 'day',
                'invoice_period' => 1,
                'invoice_interval' => 'month',
                'sort_order' => 5,
                'is_active' => true,
                'features' => [
                    '50 additional student accounts',
                    '50 additional parent accounts',
                    'Same features as base school plan',
                ],
            ],
            // Annual Plans (with discounts)
            [
                'name' => ['en' => 'Individual Student Annual'],
                'slug' => 'individual-student-annual',
                'description' => ['en' => 'Individual student plan with annual billing (2 months free)'],
                'price' => 299.99,
                'currency' => 'USD',
                'trial_period' => 7,
                'trial_interval' => 'day',
                'invoice_period' => 12,
                'invoice_interval' => 'month',
                'sort_order' => 6,
                'is_active' => true,
                'features' => [
                    'All Individual Student features',
                    '2 months free (save $59.98)',
                    'Annual billing',
                ],
            ],
            [
                'name' => ['en' => 'Individual Teacher Annual'],
                'slug' => 'individual-teacher-annual',
                'description' => ['en' => 'Individual teacher plan with annual billing (2 months free)'],
                'price' => 499.99,
                'currency' => 'USD',
                'trial_period' => 14,
                'trial_interval' => 'day',
                'invoice_period' => 12,
                'invoice_interval' => 'month',
                'sort_order' => 7,
                'is_active' => true,
                'features' => [
                    'All Individual Teacher features',
                    '2 months free (save $99.98)',
                    'Annual billing',
                ],
            ],
            [
                'name' => ['en' => 'School Basic Annual'],
                'slug' => 'school-basic-annual',
                'description' => ['en' => 'School basic plan with annual billing (2 months free)'],
                'price' => 1999.99,
                'currency' => 'USD',
                'trial_period' => 30,
                'trial_interval' => 'day',
                'invoice_period' => 12,
                'invoice_interval' => 'month',
                'sort_order' => 8,
                'is_active' => true,
                'features' => [
                    'All School Basic features',
                    '2 months free (save $399.98)',
                    'Annual billing',
                ],
            ],
        ];

        foreach ($plans as $planData) {
            $features = $planData['features'];
            unset($planData['features']);

            $plan = Plan::updateOrCreate(
                ['slug' => $planData['slug']],
                $planData
            );

            // Store features as JSON in description or a separate field
            $currentDescription = $planData['description']['en'];
            $plan->update([
                'description' => ['en' => $currentDescription . "\n\nFeatures:\n• " . implode("\n• ", $features)]
            ]);
        }
    }
}
