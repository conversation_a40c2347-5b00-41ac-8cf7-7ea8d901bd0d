<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lessons', function (Blueprint $table) {
            $table->id();
            // $table->foreignId('book_id')->constrained('books')->cascadeOnDelete();
            $table->string('title');
            $table->string('chapter')->nullable(); // e.g., "Chapter 1", "Unit 2", "Section A"
            $table->longText('content'); // Main lesson content
            $table->text('objectives')->nullable(); // Learning objectives
            $table->text('summary')->nullable(); // Lesson summary
            $table->integer('duration_minutes')->nullable(); // Expected lesson duration
            $table->integer('sort_order')->default(0); // Order within the book
            $table->boolean('is_published')->default(false);
            $table->timestamps();

            // Add polymorphic columns
            $table->morphs('lessonable'); // adds lessonable_id and lessonable_type
            $table->index(['lessonable_type', 'lessonable_id', 'sort_order']);
            
            // Indexes
            // $table->index(['book_id', 'sort_order']);
            // $table->index(['book_id', 'is_published']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            $table->dropMorphs('lessonable');
        });
        Schema::dropIfExists('lessons');
    }
};
