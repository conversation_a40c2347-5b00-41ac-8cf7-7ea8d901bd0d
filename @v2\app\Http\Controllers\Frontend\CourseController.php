<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Course;

class CourseController extends Controller
{
    public function index()
    {
        // Frontend shows courses from all teams (no tenant scoping)
        $courses = Course::withoutGlobalScopes()
            ->where('is_active', true)
            ->with(['subject', 'team'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('frontend.courses.index', compact('courses'));
    }

    public function show($courseId)
    {
        // Find course without global scopes
        $course = Course::withoutGlobalScopes()->findOrFail($courseId);

        // Check if course is active
        if (!$course->is_active) {
            abort(404);
        }

        // Load relationships without tenant scoping
        $course->loadMissing(['subject', 'team', 'lessons' => function ($query) {
            $query->withoutGlobalScopes()->where('is_active', true)->orderBy('sort_order');
        }]);

        return view('frontend.courses.show', compact('course'));
    }
}
