@extends('layouts.frontend')

@section('title', 'Test Header - EduNest')

@section('content')

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-sm p-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">Header Test Page</h1>

            <!-- Gradient Text Test Section -->
            <div class="p-4 bg-purple-50 rounded-lg mb-6">
                <h2 class="text-lg font-semibold text-purple-900 mb-4">Gradient Text Tests</h2>
                <div class="space-y-3">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Standard animated gradient:</p>
                        <h3 class="text-2xl font-bold gradient-text">EduNest Learning Platform</h3>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Static gradient (no animation):</p>
                        <h3 class="text-2xl font-bold gradient-text-static">Static Gradient Text</h3>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Gradient with glow effect:</p>
                        <h3 class="text-2xl font-bold gradient-text-glow">Glowing Gradient Text</h3>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Fast animation:</p>
                        <h3 class="text-2xl font-bold gradient-text-fast">Fast Gradient Animation</h3>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Slow animation:</p>
                        <h3 class="text-2xl font-bold gradient-text-slow">Slow Gradient Animation</h3>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <div class="p-4 bg-blue-50 rounded-lg">
                    <h2 class="text-lg font-semibold text-blue-900 mb-2">User Information</h2>
                    <p><strong>Name:</strong> {{ auth()->user()->name }}</p>
                    <p><strong>Email:</strong> {{ auth()->user()->email }}</p>
                    <p><strong>Role:</strong> {{ ucfirst(str_replace('_', ' ', auth()->user()->getPrimaryRole())) }}</p>
                </div>
                
                <div class="p-4 bg-green-50 rounded-lg">
                    <h2 class="text-lg font-semibold text-green-900 mb-2">Header Features</h2>
                    <ul class="list-disc list-inside space-y-1 text-green-800">
                        <li>User avatar with dropdown menu</li>
                        <li>Profile view and edit links</li>
                        <li>Dashboard navigation</li>
                        @if(auth()->user()->hasRole('super_admin') || auth()->user()->hasRole('school'))
                            <li>Admin panel access</li>
                        @endif
                        <li>Logout functionality</li>
                    </ul>
                </div>
                
                <div class="p-4 bg-yellow-50 rounded-lg">
                    <h2 class="text-lg font-semibold text-yellow-900 mb-2">Test Instructions</h2>
                    <ol class="list-decimal list-inside space-y-1 text-yellow-800">
                        <li>Click on your avatar in the header to open the dropdown menu</li>
                        <li>Test each menu item to ensure proper navigation</li>
                        <li>Check mobile responsiveness by resizing the window</li>
                        <li>Verify logout functionality works correctly</li>
                    </ol>
                </div>
                
                <div class="flex space-x-4">
                    <a href="{{ route('profile.show') }}" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        View Profile
                    </a>
                    <a href="{{ route('profile.edit') }}" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        Edit Profile
                    </a>
                    <a href="{{ auth()->user()->getDashboardRoute() }}" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                        Go to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
