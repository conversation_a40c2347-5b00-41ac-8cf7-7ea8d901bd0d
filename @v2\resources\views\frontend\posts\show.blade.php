@extends('layouts.frontend')

@section('title', $post->title)

@section('content')
<div class="bg-white">
    <div class="max-w-4xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <article>
            <header class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <span class="text-sm text-gray-500">{{ $post->published_at->format('F d, Y') }}</span>
                    @if($post->team)
                        <span class="text-sm text-gray-500">{{ $post->team->name }}</span>
                    @endif
                </div>
                
                <h1 class="text-4xl font-bold text-gray-900 mb-4">{{ $post->title }}</h1>
                
                @if($post->user)
                    <div class="flex items-center">
                        <span class="text-gray-600">by {{ $post->user->name }}</span>
                    </div>
                @endif
            </header>

            <div class="prose max-w-none">
                {!! $post->content !!}
            </div>
        </article>
    </div>
</div>
@endsection
