<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('blog_categories', function (Blueprint $table) {
            $table->string('type', 50)->default('blog')->after('team_id');
            $table->index('type');
        });

        // Update existing categories to have 'blog' type
        \Illuminate\Support\Facades\DB::table('blog_categories')
            ->whereNull('type')
            ->orWhere('type', '')
            ->update(['type' => 'blog']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('blog_categories', function (Blueprint $table) {
            $table->dropIndex(['type']);
            $table->dropColumn('type');
        });
    }
};
