
<!-- Alpine.js Popover -->
<div
    x-show="showPopover"
    @click.outside="showPopover = false"
    x-transition:enter="transition ease-out duration-200"
    x-transition:enter-start="opacity-0 translate-y-1"
    x-transition:enter-end="opacity-100 translate-y-0"
    x-transition:leave="transition ease-in duration-150"
    x-transition:leave-start="opacity-100 translate-y-0"
    x-transition:leave-end="opacity-0 translate-y-1"
    class="absolute z-20 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg p-4 text-gray-700"
    style="min-width: 300px"
>
    <div class="space-y-3">
        <div class="flex items-center justify-between">
            <h4 class="font-medium text-gray-900 dark:text-gray-100 text-sm" x-text="taskTitle"></h4>
            <span
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                :class="taskType === 'task' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'"
                x-text="taskType === 'task' ? 'Task' : 'Class'"
            ></span>
        </div>

        <div class="space-y-2 text-sm">
            <!-- Time -->
            <div class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span x-text="new Date(taskData.start_time).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: false}) + (taskData.end_time ? ' - ' + new Date(taskData.end_time).toLocaleTimeString('en-US', {hour: '2-digit', minute: '2-digit', hour12: false}) : '')"></span>
            </div>

            <!-- Location -->
            <div x-show="taskLoc" class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span x-text="taskLoc"></span>
            </div>

            <!-- Priority -->
            <div x-show="taskData.priority" class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
                </svg>
                <span x-text="'Priority: ' + (taskData.priority ? taskData.priority.charAt(0).toUpperCase() + taskData.priority.slice(1) : '')"></span>
            </div>

            <!-- Status -->
            <div x-show="taskData.status" class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span x-text="'Status: ' + (taskData.status ? taskData.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : '')"></span>
            </div>
 
            <!-- Assigned To -->
            <div x-show="taskData.assigned_user && taskData.assigned_user.name" class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span x-text="'Assigned to: ' + (taskData.assigned_user ? taskData.assigned_user.name : '')"></span>
            </div>

            <!-- Assigned By -->
            <div x-show="taskData.user && taskData.user.name && taskData.assigned_to && taskData.user_id !== taskData.assigned_to" class="flex items-center text-gray-600 dark:text-gray-400">
                <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                <span x-text="'Assigned by: ' + (taskData.user ? taskData.user.name : '')"></span>
            </div>

            <!-- Description -->
            <div x-show="taskData.description || taskData.notes" class="text-gray-600 dark:text-gray-400 text-xs mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                <div class="font-medium mb-1">Description:</div>
                <div x-text="taskData.description || taskData.notes"></div>
            </div>
        </div>
    </div>
    <div  class="flex items-center gap-2 mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
        <!-- Status Update Button -->
        <button 
            @click="window.quickComplete(taskID,taskType)"
            {{-- @click="quickCompleteFunc()" --}}
            class="action-button quickComplete p-2 rounded-lg bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 transition-colors"
            title="Update status">
            <x-heroicon-o-arrow-path class="w-4 h-4" />
        </button>

        <!-- Edit Button -->
        <button
            @click="window.quickEdit(taskID,taskType)"
            {{-- @click="quickEditFunc()" --}}
            class="action-button quickEdit p-2 rounded-lg bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-600 dark:text-blue-400 transition-colors"
            title="Edit">
            <x-heroicon-o-pencil class="w-4 h-4" />
        </button>

        <!-- Delete Button -->
        <button
            @click="window.quickDelete(taskID,taskType)"
            {{-- @click="quickDeleteFunc()" --}}
            class="action-button quickDelete p-2 rounded-lg bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-600 dark:text-red-400 transition-colors"
            title="Delete">
            <x-heroicon-o-trash class="w-4 h-4" />
        </button>
    </div>
</div> 