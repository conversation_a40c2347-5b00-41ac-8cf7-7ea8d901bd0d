@extends('frontend.layouts.app')

@section('title', 'Live Classes')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Live Classes</h1>
                    <p class="mt-2 text-gray-600">Join live lessons and watch recordings</p>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('frontend.live-classes.live') }}" 
                       class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-circle mr-2 animate-pulse"></i>
                        Live Now
                    </a>
                    <a href="{{ route('frontend.live-classes.upcoming') }}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-clock mr-2"></i>
                        Upcoming
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Filter Tabs -->
        <div class="mb-8">
            <nav class="flex space-x-8" aria-label="Tabs">
                <a href="{{ route('frontend.live-classes.index', ['status' => 'all']) }}" 
                   class="@if($status === 'all') border-blue-500 text-blue-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    All Classes
                </a>
                <a href="{{ route('frontend.live-classes.index', ['status' => 'live']) }}" 
                   class="@if($status === 'live') border-red-500 text-red-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <i class="fas fa-circle mr-1 text-xs"></i>
                    Live Now
                </a>
                <a href="{{ route('frontend.live-classes.index', ['status' => 'scheduled']) }}" 
                   class="@if($status === 'scheduled') border-yellow-500 text-yellow-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <i class="fas fa-clock mr-1 text-xs"></i>
                    Scheduled
                </a>
                <a href="{{ route('frontend.live-classes.index', ['status' => 'ended']) }}" 
                   class="@if($status === 'ended') border-gray-500 text-gray-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    <i class="fas fa-stop mr-1 text-xs"></i>
                    Recordings
                </a>
            </nav>
        </div>

        <!-- Live Classes Grid -->
        @if($liveVideos->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($liveVideos as $liveVideo)
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                        <!-- Thumbnail/Status -->
                        <div class="relative h-48 bg-gray-900">
                            @if($liveVideo->status === 'live')
                                <div class="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-circle mr-1 animate-pulse"></i>
                                    LIVE
                                </div>
                            @elseif($liveVideo->status === 'scheduled')
                                <div class="absolute top-4 left-4 bg-yellow-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-clock mr-1"></i>
                                    SCHEDULED
                                </div>
                            @elseif($liveVideo->status === 'ended')
                                <div class="absolute top-4 left-4 bg-gray-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-stop mr-1"></i>
                                    ENDED
                                </div>
                            @endif

                            <!-- Placeholder thumbnail -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <i class="fas fa-video text-6xl text-gray-500 opacity-50"></i>
                            </div>

                            <!-- Duration overlay for ended videos -->
                            @if($liveVideo->status === 'ended' && $liveVideo->duration)
                                <div class="absolute bottom-4 right-4 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                                    {{ $liveVideo->duration }} min
                                </div>
                            @endif
                        </div>

                        <!-- Content -->
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $liveVideo->title }}</h3>
                            
                            <div class="text-sm text-gray-600 mb-4">
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-user mr-2"></i>
                                    {{ $liveVideo->teacher->name }}
                                </div>
                                <div class="flex items-center mb-1">
                                    <i class="fas fa-calendar mr-2"></i>
                                    {{ $liveVideo->scheduled_start_time->format('M j, Y g:i A') }}
                                </div>
                                @if($liveVideo->liveable)
                                    <div class="flex items-center">
                                        <i class="fas fa-book mr-2"></i>
                                        {{ class_basename($liveVideo->liveable_type) }}: {{ $liveVideo->liveable->title }}
                                    </div>
                                @endif
                            </div>

                            @if($liveVideo->description)
                                <p class="text-gray-600 text-sm mb-4 line-clamp-2">{{ $liveVideo->description }}</p>
                            @endif

                            <!-- Action Buttons -->
                            <div class="flex space-x-2">
                                @if($liveVideo->status === 'live')
                                    <a href="{{ route('frontend.live-classes.join', $liveVideo) }}" 
                                       class="flex-1 bg-red-600 hover:bg-red-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-play mr-2"></i>
                                        Join Live
                                    </a>
                                @elseif($liveVideo->status === 'scheduled')
                                    <a href="{{ route('frontend.live-classes.show', $liveVideo) }}" 
                                       class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        View Details
                                    </a>
                                @elseif($liveVideo->status === 'ended')
                                    @if($liveVideo->recorded_media_id)
                                        <a href="{{ route('live-videos.watch', $liveVideo) }}" 
                                           class="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors">
                                            <i class="fas fa-play mr-2"></i>
                                            Watch Recording
                                        </a>
                                    @else
                                        <span class="flex-1 bg-gray-400 text-white text-center py-2 px-4 rounded-lg font-medium cursor-not-allowed">
                                            <i class="fas fa-exclamation-triangle mr-2"></i>
                                            No Recording
                                        </span>
                                    @endif
                                @endif
                                
                                <a href="{{ route('frontend.live-classes.show', $liveVideo) }}" 
                                   class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-info"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $liveVideos->appends(request()->query())->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <i class="fas fa-video text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Live Classes Found</h3>
                <p class="text-gray-600 mb-6">
                    @if($status === 'live')
                        There are no live classes at the moment.
                    @elseif($status === 'scheduled')
                        There are no scheduled classes.
                    @elseif($status === 'ended')
                        There are no recorded classes available.
                    @else
                        There are no live classes available.
                    @endif
                </p>
                <a href="{{ route('frontend.live-classes.index') }}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    View All Classes
                </a>
            </div>
        @endif
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection

@push('scripts')
<script>
// Auto-refresh live status every 30 seconds
setInterval(function() {
    if (window.location.search.includes('status=live') || window.location.search === '') {
        // Only refresh if we're viewing live or all classes
        window.location.reload();
    }
}, 30000);
</script>
@endpush
