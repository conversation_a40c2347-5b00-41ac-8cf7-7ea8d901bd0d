<?php

namespace App\Filament\Resources\AssignmentResource\RelationManagers;

use App\Models\AssignmentSubmission;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SubmissionsRelationManager extends RelationManager
{
    protected static string $relationship = 'submissions';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Student Information')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Student')
                            ->relationship('student', 'name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->whereHas('roles', function (Builder $query) {
                                    $query->where('name', 'student');
                                });
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->columnSpan(2),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Submission Details')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->options([
                                AssignmentSubmission::STATUS_DRAFT => 'Draft',
                                AssignmentSubmission::STATUS_SUBMITTED => 'Submitted',
                                AssignmentSubmission::STATUS_GRADED => 'Graded',
                                AssignmentSubmission::STATUS_RETURNED => 'Returned',
                            ])
                            ->default(AssignmentSubmission::STATUS_SUBMITTED)
                            ->required()
                            ->columnSpan(1),

                        Forms\Components\DateTimePicker::make('submitted_at')
                            ->label('Submitted At')
                            ->native(false)
                            ->displayFormat('M j, Y g:i A')
                            ->default(now())
                            ->columnSpan(1),

                        Forms\Components\Textarea::make('content')
                            ->label('Submission Content')
                            ->rows(4)
                            ->maxLength(5000)
                            ->placeholder('Student submission content')
                            ->columnSpan(2),

                        Forms\Components\FileUpload::make('file_path')
                            ->label('Submission File')
                            ->directory('assignment-submissions')
                            ->acceptedFileTypes(['pdf', 'doc', 'docx', 'txt', 'jpg', 'png'])
                            ->maxSize(10240) // 10MB
                            ->columnSpan(2),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Grading')
                    ->schema([
                        Forms\Components\TextInput::make('score')
                            ->label('Score')
                            ->numeric()
                            ->minValue(0)
                            ->step(0.01)
                            ->placeholder('Enter score')
                            ->columnSpan(1),

                        Forms\Components\DateTimePicker::make('graded_at')
                            ->label('Graded At')
                            ->native(false)
                            ->displayFormat('M j, Y g:i A')
                            ->columnSpan(1),

                        Forms\Components\RichEditor::make('feedback')
                            ->label('Teacher Feedback')
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'bulletList',
                                'orderedList',
                                'undo',
                                'redo',
                            ])
                            ->placeholder('Provide feedback to the student')
                            ->columnSpan(2),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('student.name')
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['student', 'assignment.exams.attempts']))
            ->columns([
                Tables\Columns\TextColumn::make('student.name')
                    ->label('Student')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        AssignmentSubmission::STATUS_DRAFT => 'gray',
                        AssignmentSubmission::STATUS_SUBMITTED => 'warning',
                        AssignmentSubmission::STATUS_GRADED => 'success',
                        AssignmentSubmission::STATUS_RETURNED => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('submitted_at')
                    ->label('Submitted')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->placeholder('Not submitted'),

                Tables\Columns\TextColumn::make('score')
                    ->label('Score')
                    ->numeric()
                    ->sortable()
                    ->placeholder('Not graded')
                    ->formatStateUsing(function ($record, $state) {
                        if (!$state || !$record->assignment->total_score) {
                            return $state ?? 'Not graded';
                        }
                        $percentage = ($state / $record->assignment->total_score) * 100;
                        return "{$state}/{$record->assignment->total_score} ({$percentage}%)";
                    }),

                Tables\Columns\TextColumn::make('exam_completion')
                    ->label('Exam Progress')
                    ->getStateUsing(function ($record) {
                        $assignment = $record->assignment;
                        $student = $record->student;

                        if ($assignment->exams->count() === 0) {
                            return 'No exams';
                        }

                        $completedExams = 0;
                        foreach ($assignment->exams as $exam) {
                            $hasCompletedAttempt = $exam->attempts()
                                ->where('user_id', $student->id)
                                ->where('status', 'submitted')
                                ->exists();

                            if ($hasCompletedAttempt) {
                                $completedExams++;
                            }
                        }

                        return "{$completedExams}/{$assignment->exams->count()}";
                    })
                    ->badge()
                    ->color(function ($record) {
                        $assignment = $record->assignment;
                        $student = $record->student;

                        if ($assignment->exams->count() === 0) {
                            return 'gray';
                        }

                        $completedExams = 0;
                        foreach ($assignment->exams as $exam) {
                            $hasCompletedAttempt = $exam->attempts()
                                ->where('user_id', $student->id)
                                ->where('status', 'submitted')
                                ->exists();

                            if ($hasCompletedAttempt) {
                                $completedExams++;
                            }
                        }

                        $percentage = ($completedExams / $assignment->exams->count()) * 100;

                        if ($percentage === 100) return 'success';
                        if ($percentage >= 50) return 'warning';
                        return 'danger';
                    }),

                Tables\Columns\TextColumn::make('grade_letter')
                    ->label('Grade')
                    ->badge()
                    ->color(fn (?string $state): string => match ($state) {
                        'A' => 'success',
                        'B' => 'info',
                        'C' => 'warning',
                        'D' => 'danger',
                        'F' => 'danger',
                        default => 'gray',
                    })
                    ->placeholder('Not graded'),

                Tables\Columns\IconColumn::make('is_late')
                    ->label('Late')
                    ->boolean()
                    ->trueIcon('heroicon-o-exclamation-triangle')
                    ->falseIcon('heroicon-o-check-circle')
                    ->trueColor('danger')
                    ->falseColor('success'),

                Tables\Columns\TextColumn::make('graded_at')
                    ->label('Graded')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->placeholder('Not graded')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        AssignmentSubmission::STATUS_DRAFT => 'Draft',
                        AssignmentSubmission::STATUS_SUBMITTED => 'Submitted',
                        AssignmentSubmission::STATUS_GRADED => 'Graded',
                        AssignmentSubmission::STATUS_RETURNED => 'Returned',
                    ]),

                Tables\Filters\Filter::make('ungraded')
                    ->label('Ungraded')
                    ->query(fn (Builder $query): Builder => $query->ungraded()),

                Tables\Filters\Filter::make('late_submissions')
                    ->label('Late Submissions')
                    ->query(fn (Builder $query): Builder => $query->whereHas('assignment', function (Builder $query) {
                        $query->whereColumn('assignment_submissions.submitted_at', '>', 'assignments.due_date');
                    })),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Auto-set graded_by when score is provided
                        if (isset($data['score']) && $data['score'] !== null) {
                            $data['graded_by'] = auth()->id();
                            $data['graded_at'] = now();
                            $data['status'] = AssignmentSubmission::STATUS_GRADED;
                        }
                        return $data;
                    })
                    ->successNotificationTitle('Submission created successfully'),
            ])
            ->actions([
                Tables\Actions\Action::make('quick_grade')
                    ->label('Quick Grade')
                    ->icon('heroicon-o-academic-cap')
                    ->color('success')
                    ->form([
                        Forms\Components\TextInput::make('score')
                            ->label('Score')
                            ->numeric()
                            ->minValue(0)
                            ->required()
                            ->placeholder('Enter score'),

                        Forms\Components\Textarea::make('feedback')
                            ->label('Feedback (Optional)')
                            ->rows(3)
                            ->placeholder('Provide feedback to the student'),
                    ])
                    ->action(function (array $data, $record): void {
                        $record->update([
                            'score' => $data['score'],
                            'feedback' => $data['feedback'] ?? null,
                            'status' => AssignmentSubmission::STATUS_GRADED,
                            'graded_at' => now(),
                            'graded_by' => auth()->id(),
                        ]);
                    })
                    ->successNotificationTitle('Submission graded successfully')
                    ->visible(fn ($record) => !$record->is_graded),

                Tables\Actions\Action::make('view_exam_progress')
                    ->label('Exam Progress')
                    ->icon('heroicon-o-clipboard-document-check')
                    ->color('info')
                    ->modalHeading(fn ($record) => "Exam Progress: {$record->student->name}")
                    ->modalContent(function ($record) {
                        $assignment = $record->assignment;
                        $student = $record->student;

                        if ($assignment->exams->count() === 0) {
                            return view('filament.components.no-exams-message');
                        }

                        $examData = [];
                        foreach ($assignment->exams as $exam) {
                            $attempts = $exam->attempts()
                                ->where('user_id', $student->id)
                                ->where('status', 'submitted')
                                ->orderBy('created_at', 'desc')
                                ->get();

                            $examData[] = [
                                'exam' => $exam,
                                'attempts' => $attempts,
                                'completed' => $attempts->count() > 0,
                                'best_score' => $attempts->max('total_score'),
                                'latest_score' => $attempts->first()?->total_score,
                            ];
                        }

                        return view('filament.components.exam-progress', compact('examData', 'student'));
                    })
                    ->modalSubmitAction(false)
                    ->modalCancelActionLabel('Close')
                    ->visible(fn ($record) => $record->assignment->exams->count() > 0),

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Auto-set graded_by when score is provided
                        if (isset($data['score']) && $data['score'] !== null && !isset($data['graded_by'])) {
                            $data['graded_by'] = auth()->id();
                            $data['graded_at'] = $data['graded_at'] ?? now();
                            if ($data['status'] === AssignmentSubmission::STATUS_SUBMITTED) {
                                $data['status'] = AssignmentSubmission::STATUS_GRADED;
                            }
                        }
                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('submitted_at', 'desc');
    }
}
