<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('teaching_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete(); // Teacher
            $table->foreignId('classroom_id')->constrained('class_rooms')->cascadeOnDelete();
            $table->foreignId('subject_id')->constrained('subjects')->cascadeOnDelete();
            $table->foreignId('lesson_id')->nullable()->constrained('lessons')->nullOnDelete();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->dateTime('start_time');
            $table->dateTime('end_time');
            $table->text('notes')->nullable(); // Additional notes for the schedule
            $table->enum('status', ['scheduled', 'in_progress', 'completed', 'cancelled'])->default('scheduled');
            $table->boolean('is_recurring')->default(false); // For recurring schedules
            $table->json('recurring_pattern')->nullable(); // Store recurring pattern data
            $table->timestamps();

            // Indexes for performance
            $table->index(['user_id', 'start_time']);
            $table->index(['team_id', 'start_time']);
            $table->index(['classroom_id', 'start_time']);
            $table->index(['subject_id', 'start_time']);
            $table->index(['start_time', 'end_time']);

            // Ensure no overlapping schedules for the same classroom
            $table->unique(['classroom_id', 'start_time'], 'unique_classroom_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('teaching_schedules');
    }
};
