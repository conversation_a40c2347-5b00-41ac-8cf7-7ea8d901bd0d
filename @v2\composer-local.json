{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1", "filament/filament": "*", "filament/spatie-laravel-media-library-plugin": "*", "filament/spatie-laravel-settings-plugin": "*", "filament/spatie-laravel-tags-plugin": "*", "filament/spatie-laravel-translatable-plugin": "*", "flowframe/laravel-trend": "^0.1.1", "guzzlehttp/guzzle": "^7.2", "laravel/framework": "^10.10", "laravel/horizon": "^5.21", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "squirephp/countries-en": "^3.3", "squirephp/currencies-en": "^3.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "larastan/larastan": "^2.1", "phpstan/phpstan-deprecation-rules": "^1.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "cs": ["pint"], "pint": "pint", "test:phpstan": "phpstan analyse", "test": ["@test:phpstan"]}, "extra": {"laravel": {"dont-discover": []}}, "repositories": [{"type": "path", "url": "../filament/packages/*"}], "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "dev", "prefer-stable": true}