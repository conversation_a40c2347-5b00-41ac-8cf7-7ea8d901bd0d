@extends('layouts.app')

@section('title', 'Sitemap')

@section('content')
<div class="bg-white">
    <!-- Hero section -->
    <div class="relative bg-gray-900">
        <div class="absolute inset-0">
            <img class="w-full h-full object-cover" src="https://images.unsplash.com/photo-1481627834876-b7833e8f5570?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1471&q=80" alt="">
            <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
        </div>
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">Site Map</h1>
            <p class="mt-6 text-xl text-gray-300 max-w-3xl">
                Explore all the pages and content available on our educational platform. Find courses, books, exercises, and more.
            </p>
        </div>
    </div>

    <!-- Sitemap Content -->
    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">All Pages</h2>
            <p class="mt-4 text-lg text-gray-600">Browse through all available content on our platform</p>
        </div>

        @if(empty($pages))
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No content available</h3>
                <p class="mt-1 text-sm text-gray-500">There are no pages to display at the moment.</p>
            </div>
        @else
            <div class="space-y-12">
                @foreach($pages as $category => $categoryPages)
                    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                        <div class="bg-blue-600 px-6 py-4">
                            <h3 class="text-xl font-bold text-white">{{ $category }}</h3>
                            <p class="text-blue-100 text-sm">{{ count($categoryPages) }} {{ Str::plural('page', count($categoryPages)) }}</p>
                        </div>
                        
                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                @foreach($categoryPages as $page)
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <h4 class="text-lg font-semibold text-gray-900 mb-2">
                                                    <a href="{{ $page['url'] }}" class="hover:text-blue-600 transition-colors duration-200" target="_blank">
                                                        {{ $page['title'] }}
                                                        <svg class="inline-block w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                                        </svg>
                                                    </a>
                                                </h4>
                                                @if(!empty($page['description']))
                                                    <p class="text-sm text-gray-600 mb-3 line-clamp-2">
                                                        {{ Str::limit($page['description'], 120) }}
                                                    </p>
                                                @endif
                                                <div class="flex items-center text-xs text-gray-500">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    Updated: {{ $page['last_modified']->format('M d, Y') }}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-4 flex items-center justify-between">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $category }}
                                            </span>
                                            <a href="{{ $page['url'] }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium" target="_blank">
                                                Visit Page →
                                            </a>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif

        <!-- Additional Information -->
        <div class="mt-16 bg-gray-50 rounded-lg p-8">
            <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Need Help Finding Something?</h3>
                <p class="text-gray-600 mb-6">
                    Can't find what you're looking for? Try our search feature or contact our support team.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('homepage') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z" />
                        </svg>
                        Back to Homepage
                    </a>
                    <a href="mailto:<EMAIL>" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        Contact Support
                    </a>
                </div>
            </div>
        </div>

        <!-- XML Sitemap Link -->
        <div class="mt-8 text-center">
            <p class="text-sm text-gray-500">
                For search engines: <a href="{{ route('sitemap') }}" class="text-blue-600 hover:text-blue-500">XML Sitemap</a>
            </p>
        </div>
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
