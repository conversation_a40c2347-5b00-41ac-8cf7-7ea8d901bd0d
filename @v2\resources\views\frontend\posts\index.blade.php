@extends('layouts.frontend')

@section('title', 'Blog Posts')

@section('content')
<div class="bg-white">
    <div class="relative bg-purple-900">
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">Blog Posts</h1>
            <p class="mt-6 text-xl text-purple-100 max-w-3xl">
                Read our latest articles, insights, and educational content.
            </p>
        </div>
    </div>

    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($posts as $post)
                    <article class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-500">{{ $post->published_at->format('M d, Y') }}</span>
                                @if($post->team)
                                    <span class="text-xs text-gray-500">{{ $post->team->name }}</span>
                                @endif
                            </div>
                            
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $post->title }}</h3>
                            @if($post->excerpt)
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ strip_tags($post->excerpt) }}</p>
                            @else
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ strip_tags($post->content) }}</p>
                            @endif
                            
                            <div class="flex items-center justify-between">
                                @if($post->user)
                                    <div class="flex items-center text-sm text-gray-500">
                                        <span>by {{ $post->user->name }}</span>
                                    </div>
                                @endif
                                <a href="{{ route('frontend.posts.show', $post) }}" class="text-purple-600 hover:text-purple-500 font-medium text-sm">
                                    Read More →
                                </a>
                            </div>
                        </div>
                    </article>
                @endforeach
            </div>
            <div class="mt-12">{{ $posts->links() }}</div>
        @else
            <div class="text-center py-12">
                <h3 class="mt-2 text-sm font-medium text-gray-900">No posts available</h3>
                <p class="mt-1 text-sm text-gray-500">Check back later for new articles.</p>
            </div>
        @endif
    </div>
</div>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
