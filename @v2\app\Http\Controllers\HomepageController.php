<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class HomepageController extends Controller
{
    /**
     * Display the homepage.
     */
    public function index(): View
    {
        return view('homepage');
    }

    /**
     * Display the downloads page.
     */
    public function downloads(): View
    {
        return view('downloads');
    }

    /**
     * Display the student dashboard page.
     */
    public function studentDashboard(): View
    {
        return view('student-dashboard');
    }

    /**
     * Display the teacher dashboard page.
     */
    public function teacherDashboard(): View
    {
        return view('teacher-dashboard');
    }

    /**
     * Display the teacher event list page.
     */
    public function teacherEventList(): View
    {
        return view('teacher-eventlist');
    }

    /**
     * Display the teacher event show page.
     */
    public function teacherEventShow(): View
    {
        return view('teacher-eventshow');
    }

    /**
     * Display the teacher timetable page.
     */
    public function teacherTimetable(): View
    {
        return view('teacher-timetable');
    }

    /**
     * Display the register page.
     */
    public function register(): View|\Illuminate\Http\RedirectResponse
    {
        // Redirect authenticated users to their dashboard
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->needsProfileCompletion()) {
                return redirect()->route('profile.edit');
            }
            return redirect($user->getDashboardRoute());
        }

        return view('register');
    }

    /**
     * Display the login page.
     */
    public function login(): View|\Illuminate\Http\RedirectResponse
    {
        // Redirect authenticated users to their dashboard
        if (Auth::check()) {
            $user = Auth::user();
            if ($user->needsProfileCompletion()) {
                return redirect()->route('profile.edit');
            }
            return redirect($user->getDashboardRoute());
        }

        return view('login');
    }
}
