/* Teacher Dashboard Specific Styles */

/* Circle Progress Styles */
.circle-progress {
    position: relative;
    width: 80px;
    height: 80px;
}

.circle-progress svg {
    transform: rotate(-90deg);
}

.circle-progress circle {
    fill: none;
    stroke-width: 8;
    stroke-linecap: round;
}

.circle-bg {
    stroke: #e6e6e6;
}

.circle-progress-value {
    stroke: #3b82f6;
    transition: stroke-dasharray 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1rem;
    font-weight: 600;
}

/* Calendar Styles */
.calendar-day {
    height: 100px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.calendar-day:hover {
    overflow-y: auto;
    background-color: #f8fafc;
}

.calendar-event {
    font-size: 0.7rem;
    padding: 2px 4px;
    margin-bottom: 2px;
    border-radius: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-event:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Tab Styles */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

.tab-btn {
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.tab-btn:hover {
    background-color: #f3f4f6;
}

.tab-btn.active {
    background-color: #dbeafe;
    color: #2563eb;
}

/* Modal Styles */
.modal {
    transition: opacity 0.25s ease;
    backdrop-filter: blur(4px);
}

.modal-content {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Course Card Styles */
.course-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.course-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Quick Access Button Styles */
.quick-access-btn {
    transition: all 0.2s ease;
}

.quick-access-btn:hover {
    transform: translateX(4px);
}

/* Notification Styles */
.notification-item {
    transition: all 0.2s ease;
}

.notification-item:hover {
    transform: translateX(2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Form Styles */
.form-input {
    transition: all 0.2s ease;
}

.form-input:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Stats Card Styles */
.stats-card {
    transition: all 0.3s ease;
}

.stats-card:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Action Button Styles */
.action-btn {
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

/* Schedule Grid Styles */
.schedule-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background-color: #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.schedule-cell {
    background-color: white;
    min-height: 80px;
    padding: 4px;
    transition: background-color 0.2s ease;
}

.schedule-cell:hover {
    background-color: #f3f4f6;
}

.schedule-cell.today {
    background-color: #dbeafe;
}

/* Student List Styles */
.student-item {
    transition: all 0.2s ease;
    border-radius: 8px;
}

.student-item:hover {
    background-color: #f8fafc;
    transform: translateX(4px);
}

/* Assignment Card Styles */
.assignment-card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.assignment-card:hover {
    border-left-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.assignment-card.overdue {
    border-left-color: #ef4444;
}

.assignment-card.due-soon {
    border-left-color: #f59e0b;
}

.assignment-card.completed {
    border-left-color: #10b981;
}

/* Resource Card Styles */
.resource-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.resource-card:hover {
    transform: scale(1.02);
}

.resource-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.resource-card:hover::after {
    transform: translateX(100%);
}

/* Loading States */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .circle-progress {
        width: 60px;
        height: 60px;
    }
    
    .progress-text {
        font-size: 0.8rem;
    }
    
    .calendar-day {
        height: 60px;
        font-size: 0.8rem;
    }
    
    .calendar-event {
        font-size: 0.6rem;
        padding: 1px 2px;
    }
    
    .tab-btn {
        font-size: 0.8rem;
        padding: 8px 12px;
    }
    
    .quick-access-btn {
        font-size: 0.9rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.4s ease-out forwards;
    transform: translateY(20px);
    opacity: 0;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Utility Classes */
.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
