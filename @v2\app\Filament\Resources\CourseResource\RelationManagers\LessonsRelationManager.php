<?php

namespace App\Filament\Resources\CourseResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class LessonsRelationManager extends RelationManager
{
    protected static string $relationship = 'lessons';

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?string $title = 'Course Lessons';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        
                        Forms\Components\TextInput::make('chapter')
                            ->maxLength(255)
                            ->placeholder('e.g., Module 1, Week 2, Session A')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('duration_minutes')
                            ->label('Duration (minutes)')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(480) // 8 hours max
                            ->placeholder('Expected lesson duration')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('sort_order')
                            ->label('Order')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->columnSpan(1),
                        
                        Forms\Components\Toggle::make('is_published')
                            ->label('Published')
                            ->default(false)
                            ->columnSpan(1),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\RichEditor::make('content')
                            ->required()
                            ->toolbarButtons([
                                'attachFiles',
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'h2',
                                'h3',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'underline',
                                'undo',
                            ])
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Learning Information')
                    ->schema([
                        Forms\Components\Textarea::make('objectives')
                            ->label('Learning Objectives')
                            ->rows(3)
                            ->placeholder('What students will learn from this lesson')
                            ->columnSpan(1),
                        
                        Forms\Components\Textarea::make('summary')
                            ->label('Lesson Summary')
                            ->rows(3)
                            ->placeholder('Brief summary of the lesson content')
                            ->columnSpan(1),
                    ])
                    ->columns(2),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('chapter')
                    ->searchable()
                    ->badge()
                    ->color('gray')
                    ->placeholder('No chapter')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('formatted_duration')
                    ->label('Duration')
                    ->placeholder('Not set'),
                
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Order')
                    ->numeric()
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_published')
                    ->label('Published')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_chapter')
                    ->label('Has Chapter')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('chapter')),
                
                Tables\Filters\TernaryFilter::make('is_published')
                    ->label('Published Status'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Auto-increment sort_order for new lessons
                        if (!isset($data['sort_order']) || $data['sort_order'] === 0) {
                            $maxOrder = $this->getOwnerRecord()->lessons()->max('sort_order') ?? 0;
                            $data['sort_order'] = $maxOrder + 1;
                        }

                        // Set the polymorphic relationship
                        $data['lessonable_id'] = $this->getOwnerRecord()->id;
                        $data['lessonable_type'] = get_class($this->getOwnerRecord());

                        return $data;
                    })
                    ->successNotificationTitle('Course lesson created successfully'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('gray')
                    ->action(function ($record) {
                        $newLesson = $record->replicate();
                        $newLesson->title = $record->title . ' (Copy)';
                        $newLesson->is_published = false;

                        // Set the polymorphic relationship to the same parent
                        $newLesson->lessonable_id = $this->getOwnerRecord()->id;
                        $newLesson->lessonable_type = get_class($this->getOwnerRecord());

                        // Update sort order
                        $maxOrder = $this->getOwnerRecord()->lessons()->max('sort_order') ?? 0;
                        $newLesson->sort_order = $maxOrder + 1;

                        $newLesson->save();
                    })
                    ->successNotificationTitle('Lesson duplicated successfully'),
                Tables\Actions\Action::make('toggle_published')
                    ->icon(fn ($record) => $record->is_published ? 'heroicon-o-eye-slash' : 'heroicon-o-eye')
                    ->color(fn ($record) => $record->is_published ? 'warning' : 'success')
                    ->label(fn ($record) => $record->is_published ? 'Unpublish' : 'Publish')
                    ->action(function ($record) {
                        $record->update(['is_published' => !$record->is_published]);
                    })
                    ->successNotificationTitle(fn ($record) => $record->is_published ? 'Lesson published' : 'Lesson unpublished'),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc')
            ->reorderable('sort_order');
    }
}
