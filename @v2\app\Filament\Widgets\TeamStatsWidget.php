<?php

namespace App\Filament\Widgets;

use App\Models\Shop\Customer;
use App\Models\Shop\Order;
use App\Models\Shop\Product;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class TeamStatsWidget extends BaseWidget
{
    protected static ?int $sort = -1; // Show first

    protected function getStats(): array
    {
        $currentTenant = Filament::getTenant();
        
        if (!$currentTenant) {
            return [
                Stat::make('Select Team', 'Please select a team to view statistics')
                    ->description('Use the team switcher above')
                    ->descriptionIcon('heroicon-m-information-circle')
                    ->color('gray'),
            ];
        }

        // Get team-specific counts
        $usersCount = User::where('team_id', $currentTenant->id)->count();
        $customersCount = Customer::where('team_id', $currentTenant->id)->count();
        $productsCount = Product::where('team_id', $currentTenant->id)->count();
        $ordersCount = Order::where('team_id', $currentTenant->id)->count();
        
        // Get recent activity (last 30 days)
        $recentOrders = Order::where('team_id', $currentTenant->id)
            ->where('created_at', '>=', now()->subDays(30))
            ->count();
            
        $recentCustomers = Customer::where('team_id', $currentTenant->id)
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return [
            Stat::make('School Users', $usersCount)
                ->description("Users in {$currentTenant->name}")
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),
                
            Stat::make('Customers', $customersCount)
                ->description("{$recentCustomers} new this month")
                ->descriptionIcon('heroicon-m-user-group')
                ->color('success'),
                
            Stat::make('Products', $productsCount)
                ->description("Total products available")
                ->descriptionIcon('heroicon-m-cube')
                ->color('warning'),
                
            Stat::make('Orders', $ordersCount)
                ->description("{$recentOrders} orders this month")
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color('info'),
        ];
    }

    public function getHeading(): ?string
    {
        $currentTenant = Filament::getTenant();
        return $currentTenant ? "Team Statistics - {$currentTenant->name}" : 'Team Statistics';
    }
}
