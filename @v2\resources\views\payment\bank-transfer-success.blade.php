@extends('layouts.frontend')

@section('title', 'Payment Verified Successfully')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-green-600 px-6 py-4">
                <h1 class="text-2xl font-bold text-white">Payment Verified Successfully!</h1>
                <p class="text-green-100 mt-1">Your subscription has been activated</p>
            </div>

            <div class="p-6">
                <!-- Success Message -->
                <div class="text-center py-8">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
                        <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Payment Confirmed!</h2>
                    <p class="text-gray-600 mb-6">Your bank transfer has been verified and your subscription is now active.</p>
                </div>

                <!-- Payment Details -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Payment Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Amount Paid</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900">${{ number_format($payment->amount, 2) }} {{ $payment->currency }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Payment Reference</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900 font-mono">{{ $payment->uuid }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Verification Time</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900">{{ $payment->verified_at ? $payment->verified_at->format('M d, Y H:i') : 'Just now' }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <span class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Verified & Active
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">What's Next?</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Your subscription is now active and ready to use</li>
                                    <li>Access your dashboard to start using all features</li>
                                    <li>You'll receive a confirmation email shortly</li>
                                    <li>Your next billing date will be shown in your account</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ route('dashboard') }}" class="flex-1 bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-center">
                        Go to Dashboard
                    </a>
                    <a href="{{ route('pricing') }}" class="flex-1 bg-gray-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 text-center">
                        View All Plans
                    </a>
                </div>

                <!-- Support Information -->
                <div class="mt-8 text-center text-sm text-gray-600">
                    <p>Need help? Contact our support team at <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-500"><EMAIL></a></p>
                    <p class="mt-1">Keep this page for your records or take a screenshot</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
