// Animated SVG Interactive Effects
document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all animated SVG effects
    initializeAnimatedSVGs();
    
    // Add intersection observer for scroll-triggered animations
    initializeScrollAnimations();
    
    // Add mouse interaction effects
    initializeMouseInteractions();
    
    // Add performance optimizations
    initializePerformanceOptimizations();
});

function initializeAnimatedSVGs() {
    // Add random delays to floating particles for more natural movement
    const particles = document.querySelectorAll('.particle');
    particles.forEach((particle, index) => {
        const randomDelay = Math.random() * 5;
        const randomDuration = 6 + Math.random() * 4;
        const randomSize = 4 + Math.random() * 8;
        
        particle.style.animationDelay = `${randomDelay}s`;
        particle.style.animationDuration = `${randomDuration}s`;
        particle.style.width = `${randomSize}px`;
        particle.style.height = `${randomSize}px`;
        
        // Add random colors
        const colors = ['#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899', '#10b981'];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];
        particle.style.background = `${randomColor}33`; // 20% opacity
    });
    
    // Add interactive hover effects to orbital systems
    const orbitalSystems = document.querySelectorAll('[data-type="orbital-system"]');
    orbitalSystems.forEach(system => {
        system.addEventListener('mouseenter', () => {
            system.style.animationPlayState = 'paused';
        });
        
        system.addEventListener('mouseleave', () => {
            system.style.animationPlayState = 'running';
        });
    });
    
    // Add click effects to pulsing orbs
    const pulsingOrbs = document.querySelectorAll('.pulse-glow-svg');
    pulsingOrbs.forEach(orb => {
        orb.addEventListener('click', () => {
            orb.style.animation = 'none';
            setTimeout(() => {
                orb.style.animation = 'pulseGlowSvg 2s ease-in-out infinite';
            }, 100);
        });
    });
}

function initializeScrollAnimations() {
    // Create intersection observer for scroll-triggered animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                
                // Add fade-in animation
                if (element.classList.contains('fade-on-scroll')) {
                    element.classList.add('fade-in');
                }
                
                // Trigger SVG drawing animations
                if (element.querySelector('.draw-svg')) {
                    const drawElements = element.querySelectorAll('.draw-svg');
                    drawElements.forEach((drawEl, index) => {
                        setTimeout(() => {
                            drawEl.style.animation = 'drawPath 3s ease-in-out forwards';
                        }, index * 200);
                    });
                }
                
                // Trigger morphing animations
                if (element.querySelector('.morph-shape')) {
                    const morphElements = element.querySelectorAll('.morph-shape');
                    morphElements.forEach(morphEl => {
                        morphEl.style.animation = 'morphShape 4s ease-in-out infinite';
                    });
                }
            }
        });
    }, observerOptions);
    
    // Observe all elements with animation classes
    const animatedElements = document.querySelectorAll('.fade-on-scroll, [class*="animated-svg"]');
    animatedElements.forEach(el => observer.observe(el));
}

function initializeMouseInteractions() {
    // Add mouse follow effect for certain elements
    let mouseX = 0;
    let mouseY = 0;
    
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
        
        // Move floating elements slightly based on mouse position
        const floatingElements = document.querySelectorAll('.magnetic-field');
        floatingElements.forEach(element => {
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            const deltaX = (mouseX - centerX) * 0.02;
            const deltaY = (mouseY - centerY) * 0.02;
            
            element.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
        });
    });
    
    // Add hover effects to interactive elements
    const interactiveElements = document.querySelectorAll('.hover-scale, .hover-lift');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            element.style.transform = 'scale(1.05) translateY(-2px)';
            element.style.transition = 'transform 0.3s ease';
        });
        
        element.addEventListener('mouseleave', () => {
            element.style.transform = 'scale(1) translateY(0)';
        });
    });
    
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('button, .btn-primary, .btn-secondary');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

function initializePerformanceOptimizations() {
    // Pause animations when tab is not visible
    document.addEventListener('visibilitychange', () => {
        const animatedElements = document.querySelectorAll('[class*="animation"], [class*="spin"], [class*="pulse"]');
        
        if (document.hidden) {
            animatedElements.forEach(el => {
                el.style.animationPlayState = 'paused';
            });
        } else {
            animatedElements.forEach(el => {
                el.style.animationPlayState = 'running';
            });
        }
    });
    
    // Reduce animations on low-end devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        const complexAnimations = document.querySelectorAll('.kaleidoscope-svg, .dna-helix, .spiral-svg');
        complexAnimations.forEach(el => {
            el.style.animationDuration = '8s'; // Slower animations
        });
    }
    
    // Respect user's motion preferences
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        const allAnimations = document.querySelectorAll('[class*="animation"], [class*="spin"], [class*="pulse"]');
        allAnimations.forEach(el => {
            el.style.animation = 'none';
        });
    }
}

// Utility functions for dynamic SVG creation
function createFloatingParticle(container, options = {}) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    
    const size = options.size || (4 + Math.random() * 8);
    const color = options.color || '#06b6d4';
    const delay = options.delay || Math.random() * 5;
    const duration = options.duration || (6 + Math.random() * 4);
    
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    particle.style.background = `${color}33`;
    particle.style.left = `${Math.random() * 100}%`;
    particle.style.animationDelay = `${delay}s`;
    particle.style.animationDuration = `${duration}s`;
    
    container.appendChild(particle);
    
    return particle;
}

function createPulsingOrb(container, options = {}) {
    const orb = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    orb.setAttribute('class', 'pulse-glow-svg');
    orb.setAttribute('viewBox', '0 0 100 100');
    orb.style.width = options.size || '64px';
    orb.style.height = options.size || '64px';
    
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    circle.setAttribute('cx', '50');
    circle.setAttribute('cy', '50');
    circle.setAttribute('r', '30');
    circle.setAttribute('fill', options.color || '#06b6d4');
    circle.setAttribute('opacity', '0.7');
    
    orb.appendChild(circle);
    container.appendChild(orb);
    
    return orb;
}

// Export functions for use in other scripts
window.AnimatedSVG = {
    createFloatingParticle,
    createPulsingOrb,
    initializeAnimatedSVGs,
    initializeScrollAnimations,
    initializeMouseInteractions
};

// Add CSS for ripple effect
const rippleCSS = `
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`;

const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
