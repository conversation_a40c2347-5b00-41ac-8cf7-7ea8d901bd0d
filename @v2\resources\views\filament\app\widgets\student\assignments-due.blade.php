<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-clipboard-document-list class="w-5 h-5 text-primary-600 mr-2" />
                📝 การบ้านที่ต้องส่ง
            </div>
        </x-slot>

        <div class="space-y-4">
            @foreach($this->getAssignments() as $assignment)
                <div class="p-4 rounded-lg border-l-4 transition-all duration-200 hover:shadow-md
                    @if($assignment['priority'] === 'high') border-red-500 bg-red-50 dark:bg-red-900/20
                    @elseif($assignment['priority'] === 'medium') border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20
                    @else border-blue-500 bg-blue-50 dark:bg-blue-900/20
                    @endif">
                    
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 dark:text-white">{{ $assignment['title'] }}</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">วิชา: {{ $assignment['subject'] }}</p>
                            <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">กำหนดส่ง: {{ $assignment['due_date'] }}</p>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <span class="px-3 py-1 rounded-full text-xs font-medium
                                @if($assignment['status'] === 'pending') bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200
                                @elseif($assignment['status'] === 'in_progress') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200
                                @else bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200
                                @endif">
                                @if($assignment['status'] === 'pending') ยังไม่เริ่มทำ
                                @elseif($assignment['status'] === 'in_progress') กำลังทำ
                                @else ยังไม่เริ่มทำ
                                @endif
                            </span>
                            
                            <button class="px-4 py-2 rounded-lg text-sm font-medium transition-colors
                                @if($assignment['priority'] === 'high') bg-red-600 hover:bg-red-700 text-white
                                @elseif($assignment['priority'] === 'medium') bg-yellow-600 hover:bg-yellow-700 text-white
                                @else bg-blue-600 hover:bg-blue-700 text-white
                                @endif">
                                @if($assignment['status'] === 'in_progress') ทำต่อ
                                @else เริ่มทำ
                                @endif
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
