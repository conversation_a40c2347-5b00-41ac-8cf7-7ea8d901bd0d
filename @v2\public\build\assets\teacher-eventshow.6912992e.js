document.addEventListener("DOMContentLoaded",function(){const u=document.getElementById("menu-toggle"),i=document.getElementById("mobile-menu"),m=document.getElementById("close-menu");u&&i&&u.addEventListener("click",function(){i.classList.add("active")}),m&&i&&m.addEventListener("click",function(){i.classList.remove("active")});const s=document.getElementById("copyLinkBtn");s&&s.addEventListener("click",function(){const e=window.location.href;navigator.clipboard&&window.isSecureContext?navigator.clipboard.writeText(e).then(function(){y()}).catch(function(o){console.error("Failed to copy: ",o),f(e)}):f(e)});function f(e){const o=document.createElement("textarea");o.value=e,o.style.top="0",o.style.left="0",o.style.position="fixed",document.body.appendChild(o),o.focus(),o.select();try{document.execCommand("copy")?y():l("\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E25\u0E34\u0E07\u0E01\u0E4C\u0E44\u0E14\u0E49","error")}catch(t){console.error("Fallback: Oops, unable to copy",t),l("\u0E44\u0E21\u0E48\u0E2A\u0E32\u0E21\u0E32\u0E23\u0E16\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E25\u0E34\u0E07\u0E01\u0E4C\u0E44\u0E14\u0E49","error")}document.body.removeChild(o)}function y(){const e=s.innerHTML;s.innerHTML=`
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            \u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E25\u0E34\u0E07\u0E01\u0E4C\u0E41\u0E25\u0E49\u0E27
        `,s.classList.add("success-bounce"),setTimeout(function(){s.innerHTML=e,s.classList.remove("success-bounce")},2e3),l("\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E25\u0E34\u0E07\u0E01\u0E4C\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27","success")}document.querySelectorAll(".btn-test, .btn-exercise").forEach(e=>{e.addEventListener("click",function(){if(!this.classList.contains("btn-disabled")){const o=this.textContent.trim(),c=this.closest("tr").querySelector("td:nth-child(2)").textContent.trim();b(o,c)}})});function b(e,o){const t=document.createElement("div");t.className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",t.innerHTML=`
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 modal-content">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">\u0E40\u0E1B\u0E34\u0E14\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">\u0E23\u0E32\u0E22\u0E25\u0E30\u0E40\u0E2D\u0E35\u0E22\u0E14</h4>
                        <p><strong>\u0E19\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19:</strong> ${o}</p>
                        <p><strong>\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21:</strong> ${e}</p>
                    </div>
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">\u0E04\u0E33\u0E41\u0E19\u0E30\u0E19\u0E33</h4>
                        <p class="text-sm text-gray-600">
                            ${e==="\u0E17\u0E33\u0E41\u0E1A\u0E1A\u0E17\u0E14\u0E2A\u0E2D\u0E1A"?"\u0E19\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19\u0E08\u0E30\u0E44\u0E14\u0E49\u0E17\u0E33\u0E41\u0E1A\u0E1A\u0E17\u0E14\u0E2A\u0E2D\u0E1A\u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E1B\u0E23\u0E30\u0E40\u0E21\u0E34\u0E19\u0E04\u0E27\u0E32\u0E21\u0E40\u0E02\u0E49\u0E32\u0E43\u0E08\u0E43\u0E19\u0E1A\u0E17\u0E40\u0E23\u0E35\u0E22\u0E19":"\u0E19\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19\u0E08\u0E30\u0E44\u0E14\u0E49\u0E1D\u0E36\u0E01\u0E17\u0E33\u0E41\u0E1A\u0E1A\u0E1D\u0E36\u0E01\u0E2B\u0E31\u0E14\u0E40\u0E1E\u0E37\u0E48\u0E2D\u0E40\u0E2A\u0E23\u0E34\u0E21\u0E17\u0E31\u0E01\u0E29\u0E30\u0E01\u0E32\u0E23\u0E40\u0E23\u0E35\u0E22\u0E19\u0E23\u0E39\u0E49"}
                        </p>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 flex-1 open-activity">
                            \u0E40\u0E1B\u0E34\u0E14\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21
                        </button>
                        <button class="close-modal bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400">
                            \u0E22\u0E01\u0E40\u0E25\u0E34\u0E01
                        </button>
                    </div>
                </div>
            </div>
        `,document.body.appendChild(t),t.querySelectorAll(".close-modal").forEach(r=>{r.addEventListener("click",function(){document.body.removeChild(t)})}),t.addEventListener("click",function(r){r.target===t&&document.body.removeChild(t)}),t.querySelector(".open-activity").addEventListener("click",function(){l(`\u0E40\u0E1B\u0E34\u0E14${e}\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A ${o}`,"info"),document.body.removeChild(t)})}document.querySelectorAll("td:nth-child(3), td:nth-child(4)").forEach(e=>{const o=e.textContent.trim();if(o==="-")e.classList.add("score-missing");else if(o.includes("/")){const[t,c]=o.split("/").map(Number),n=t/c*100;n>=90?e.classList.add("score-excellent"):n>=80?e.classList.add("score-good"):n>=70?e.classList.add("score-average"):e.classList.add("score-poor")}}),document.querySelectorAll("tbody tr").forEach((e,o)=>{e.style.animationDelay=`${o*.1}s`,e.classList.add("table-row-enter")});const a=document.querySelector(".progress-bar");if(a){const e=a.style.width;a.style.width="0%",setTimeout(()=>{a.style.width=e},500)}function l(e,o="info"){const t=document.createElement("div");t.className=`notification ${o}`,t.innerHTML=`
            <div class="flex items-center">
                <span>${e}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `,document.body.appendChild(t),setTimeout(()=>{t.parentElement&&(t.style.transform="translateX(100%)",setTimeout(()=>{t.remove()},300))},5e3)}const p=document.querySelector('input[type="search"]');p&&p.addEventListener("input",function(){const e=this.value.toLowerCase();document.querySelectorAll("tbody tr").forEach(t=>{t.querySelector("td:nth-child(2)").textContent.toLowerCase().includes(e)?t.style.display="":t.style.display="none"})});function g(){const o=document.querySelector(".student-table").querySelectorAll("tr");let t="";o.forEach(x=>{const w=x.querySelectorAll("th, td"),L=Array.from(w).map(C=>`"${C.textContent.trim()}"`);t+=L.join(",")+`
`});const c=new Blob([t],{type:"text/csv;charset=utf-8;"}),n=document.createElement("a"),r=URL.createObjectURL(c);n.setAttribute("href",r),n.setAttribute("download","student_results.csv"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),l("\u0E2A\u0E48\u0E07\u0E2D\u0E2D\u0E01\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27","success")}const h=document.querySelector(".header-card");if(h){const e=document.createElement("button");e.className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center ml-2",e.innerHTML=`
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            \u0E2A\u0E48\u0E07\u0E2D\u0E2D\u0E01\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25
        `,e.addEventListener("click",g);const o=h.querySelector(".flex.flex-col.md\\:flex-row");o&&o.appendChild(e)}const v=document.querySelector(".header-card"),d=document.querySelector(".table-container");v&&v.classList.add("fade-in"),d&&(d.classList.add("fade-in"),d.style.animationDelay="0.2s"),document.addEventListener("keydown",function(e){(e.ctrlKey||e.metaKey)&&e.key==="c"&&!e.target.matches("input, textarea")&&(e.preventDefault(),s&&s.click()),e.key==="Escape"&&document.querySelectorAll(".fixed.inset-0").forEach(t=>{t.parentElement&&t.remove()})})});
