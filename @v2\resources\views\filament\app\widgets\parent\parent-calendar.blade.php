<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-calendar-days class="w-5 h-5 text-primary-600 mr-2" />
                📅 ปฏิทินกิจกรรมครอบครัว
            </div>
        </x-slot>

        @php
            $calendarData = $this->getCalendarData();
        @endphp

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- Calendar -->
            <div class="w-full lg:w-2/3">
                <!-- Calendar Header -->
                <div class="flex justify-between items-center mb-4">
                    <button 
                        wire:click="previousMonth"
                        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                        <x-heroicon-o-chevron-left class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    </button>
                    <h3 class="font-medium text-lg text-gray-900 dark:text-white">{{ $calendarData['month_name'] }}</h3>
                    <button 
                        wire:click="nextMonth"
                        class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                        <x-heroicon-o-chevron-right class="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    </button>
                </div>

                <!-- Calendar Grid -->
                <div class="grid grid-cols-7 gap-1">
                    <!-- Day Headers -->
                    @foreach($calendarData['day_names'] as $dayName)
                        <div class="text-center font-medium text-gray-500 dark:text-gray-400 py-2 text-sm">{{ $dayName }}</div>
                    @endforeach
                    
                    <!-- Calendar Days -->
                    @foreach($calendarData['days'] as $day)
                        <div class="border rounded-lg p-2 h-20 overflow-hidden hover:overflow-y-auto transition-all cursor-pointer
                            {{ $day['is_current_month'] ? 'text-gray-900 dark:text-white' : 'text-gray-400 dark:text-gray-600' }}
                            {{ $day['is_today'] ? 'bg-primary-50 dark:bg-primary-900/20 border-primary-200 dark:border-primary-700 font-medium' : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700' }}
                            hover:shadow-sm">
                            
                            <div class="text-sm font-medium">{{ $day['date'] }}</div>
                            
                            @foreach($day['events'] as $event)
                                <div class="text-xs px-1 py-0.5 mt-1 rounded truncate
                                    @if($event['color'] === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200
                                    @elseif($event['color'] === 'green') bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                    @elseif($event['color'] === 'red') bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200
                                    @elseif($event['color'] === 'purple') bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200
                                    @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                    @endif">
                                    {{ $event['title'] }}
                                </div>
                            @endforeach
                        </div>
                    @endforeach
                </div>
            </div>
            
            <!-- Today's Activities Sidebar -->
            <div class="w-full lg:w-1/3">
                <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                    <h4 class="font-medium text-lg mb-3 flex items-center text-gray-900 dark:text-white">
                        <x-heroicon-o-bell class="w-5 h-5 text-primary-600 mr-2" />
                        การแจ้งเตือนวันนี้
                    </h4>
                    
                    <div class="space-y-4">
                        @foreach($this->getTodayActivities() as $activity)
                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                                <div class="flex items-start mb-3">
                                    <div class="flex-shrink-0 mr-3">
                                        <div class="p-2 rounded-lg
                                            @if($activity['color'] === 'green') bg-green-100 dark:bg-green-900/50
                                            @elseif($activity['color'] === 'blue') bg-blue-100 dark:bg-blue-900/50
                                            @elseif($activity['color'] === 'red') bg-red-100 dark:bg-red-900/50
                                            @endif">
                                            @if($activity['type'] === 'child-progress')
                                                <x-heroicon-o-academic-cap class="w-5 h-5 text-{{ $activity['color'] }}-600 dark:text-{{ $activity['color'] }}-400" />
                                            @elseif($activity['type'] === 'teacher-message')
                                                <x-heroicon-o-chat-bubble-left-right class="w-5 h-5 text-{{ $activity['color'] }}-600 dark:text-{{ $activity['color'] }}-400" />
                                            @elseif($activity['type'] === 'payment-reminder')
                                                <x-heroicon-o-currency-dollar class="w-5 h-5 text-{{ $activity['color'] }}-600 dark:text-{{ $activity['color'] }}-400" />
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <h5 class="font-medium text-gray-900 dark:text-white">{{ $activity['title'] }}</h5>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $activity['time'] }}</p>
                                        
                                        @if(isset($activity['child']))
                                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                                @if(isset($activity['score']))
                                                    {{ $activity['child'] }} • {{ $activity['subject'] }} • คะแนน: {{ $activity['score'] }}
                                                @elseif(isset($activity['teacher']))
                                                    {{ $activity['child'] }} • จาก{{ $activity['teacher'] }} • {{ $activity['subject'] }}
                                                @endif
                                            </div>
                                        @endif
                                        
                                        @if(isset($activity['amount']))
                                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                                จำนวน: {{ $activity['amount'] }} • ครบกำหนด: {{ $activity['due_date'] }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                
                                <div class="flex flex-wrap gap-2">
                                    @foreach($activity['actions'] as $action)
                                        <button class="text-xs px-3 py-1.5 rounded-full flex items-center transition-colors
                                            @if($action['color'] === 'green') bg-green-600 hover:bg-green-700 text-white
                                            @elseif($action['color'] === 'blue') bg-blue-600 hover:bg-blue-700 text-white
                                            @elseif($action['color'] === 'purple') bg-purple-600 hover:bg-purple-700 text-white
                                            @elseif($action['color'] === 'red') bg-red-600 hover:bg-red-700 text-white
                                            @elseif($action['color'] === 'gray') bg-gray-600 hover:bg-gray-700 text-white
                                            @endif">
                                            @if($action['icon'] === 'heroicon-o-eye')
                                                <x-heroicon-o-eye class="w-3 h-3 mr-1" />
                                            @elseif($action['icon'] === 'heroicon-o-heart')
                                                <x-heroicon-o-heart class="w-3 h-3 mr-1" />
                                            @elseif($action['icon'] === 'heroicon-o-chat-bubble-left-right')
                                                <x-heroicon-o-chat-bubble-left-right class="w-3 h-3 mr-1" />
                                            @elseif($action['icon'] === 'heroicon-o-pencil')
                                                <x-heroicon-o-pencil class="w-3 h-3 mr-1" />
                                            @elseif($action['icon'] === 'heroicon-o-credit-card')
                                                <x-heroicon-o-credit-card class="w-3 h-3 mr-1" />
                                            @elseif($action['icon'] === 'heroicon-o-document-text')
                                                <x-heroicon-o-document-text class="w-3 h-3 mr-1" />
                                            @else
                                                <x-heroicon-o-document class="w-3 h-3 mr-1" />
                                            @endif
                                            {{ $action['label'] }}
                                        </button>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
