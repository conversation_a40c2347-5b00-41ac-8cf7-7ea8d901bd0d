@extends('layouts.frontend')

@section('title', $course->title)

@section('content')
<div class="bg-white">
    <!-- Course Header -->
    <div class="relative bg-gray-900">
        @if($course->featured_image)
            <div class="absolute inset-0">
                <img class="w-full h-full object-cover" src="{{ Storage::url($course->featured_image) }}" alt="{{ $course->title }}">
                <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>
        @endif
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <div class="flex items-center mb-4">
                @if($course->subject)
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mr-4">
                        {{ $course->subject->name }}
                    </span>
                @endif
                @if($course->team)
                    <span class="text-gray-300">by {{ $course->team->name }}</span>
                @endif
            </div>
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">{{ $course->title }}</h1>
            <p class="mt-6 text-xl text-gray-300 max-w-3xl">
                {{ strip_tags($course->description) }}
            </p>
        </div>
    </div>

    <!-- Course Content -->
    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Course Description</h2>
                    <div class="prose max-w-none">
                        {!! $course->description !!}
                    </div>
                </div>

                @if($course->lessons && $course->lessons->count() > 0)
                    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Course Lessons</h2>
                        <div class="space-y-4">
                            @foreach($course->lessons as $lesson)
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="font-semibold text-gray-900">{{ $lesson->title }}</h3>
                                            @if($lesson->description)
                                                <p class="text-sm text-gray-600 mt-1">{{ strip_tags($lesson->description) }}</p>
                                            @endif
                                        </div>
                                        <div class="flex items-center text-sm text-gray-500">
                                            @if($lesson->duration)
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                {{ $lesson->duration }} min
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Course Information</h3>
                    
                    <div class="space-y-4">
                        @if($course->lessons)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Lessons</span>
                                <span class="text-sm font-medium text-gray-900">{{ $course->lessons->count() }}</span>
                            </div>
                        @endif
                        
                        @if($course->level)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Level</span>
                                <span class="text-sm font-medium text-gray-900">{{ ucfirst($course->level) }}</span>
                            </div>
                        @endif
                        
                        @if($course->language)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Language</span>
                                <span class="text-sm font-medium text-gray-900">{{ $course->language }}</span>
                            </div>
                        @endif
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Last Updated</span>
                            <span class="text-sm font-medium text-gray-900">{{ $course->updated_at->format('M d, Y') }}</span>
                        </div>
                    </div>

                    @auth
                        <div class="mt-6">
                            <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                Enroll in Course
                            </button>
                        </div>
                    @else
                        <div class="mt-6">
                            <a href="{{ route('login') }}" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-center block">
                                Login to Enroll
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
