# Media Manager Usage Examples

## Team-Aware Media Manager (Recommended)

For team-based applications, use the `TeamMediaManagerInput` component that automatically filters media by team:

```php
<?php

namespace App\Filament\Resources;

use App\Forms\Components\TeamMediaManagerInput;
use Filament\Forms;
use Filament\Forms\Form;

class ProductResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required(),

            // Team-filtered media manager
            TeamMediaManagerInput::make('product_images')
                ->label('Product Images')
                ->disk('public')
                ->teamAware() // Enable team-specific filtering
                ->schema([
                    Forms\Components\TextInput::make('alt_text')
                        ->label('Alt Text')
                        ->maxLength(255),
                    Forms\Components\Select::make('image_type')
                        ->label('Image Type')
                        ->options([
                            'primary' => 'Primary Image',
                            'gallery' => 'Gallery Image',
                            'thumbnail' => 'Thumbnail',
                        ])
                        ->required(),
                ]),
        ]);
    }
}
```

## Basic Usage in Filament Forms

Here are practical examples of how to use the `MediaManagerInput` component in your existing Filament resources.

### Example 1: Adding Media to Blog Posts

```php
<?php

namespace App\Filament\Resources\Blog;

use App\Forms\Components\TeamMediaManagerInput;
// Alternative: use TomatoPHP\FilamentMediaManager\Form\MediaManagerInput;
use Filament\Forms;
use Filament\Forms\Form;

class PostResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('title')
                ->required()
                ->maxLength(255),
            
            Forms\Components\Textarea::make('excerpt')
                ->maxLength(500),
            
            // Add team-aware media manager for featured images
            TeamMediaManagerInput::make('featured_images')
                ->label('Featured Images')
                ->disk('public')
                ->schema([
                    Forms\Components\TextInput::make('alt_text')
                        ->label('Alt Text')
                        ->maxLength(255),
                    Forms\Components\TextInput::make('caption')
                        ->label('Caption')
                        ->maxLength(255),
                ]),
            
            Forms\Components\RichEditor::make('content')
                ->required(),
        ]);
    }
}
```

### Example 2: Adding Media to Products

```php
<?php

namespace App\Filament\Clusters\Products\Resources;

use TomatoPHP\FilamentMediaManager\Form\MediaManagerInput;
use Filament\Forms;
use Filament\Forms\Form;

class PhysicalProductResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required()
                ->maxLength(255),
            
            Forms\Components\TextInput::make('price')
                ->required()
                ->numeric(),
            
            // Product gallery
            MediaManagerInput::make('gallery')
                ->label('Product Gallery')
                ->disk('public')
                ->schema([
                    Forms\Components\TextInput::make('title')
                        ->label('Image Title')
                        ->maxLength(255),
                    Forms\Components\Textarea::make('description')
                        ->label('Image Description')
                        ->maxLength(500),
                    Forms\Components\Toggle::make('is_primary')
                        ->label('Primary Image')
                        ->default(false),
                ]),
            
            // Product documents/manuals
            MediaManagerInput::make('documents')
                ->label('Product Documents')
                ->disk('public')
                ->schema([
                    Forms\Components\TextInput::make('document_type')
                        ->label('Document Type')
                        ->options([
                            'manual' => 'Manual',
                            'warranty' => 'Warranty',
                            'specification' => 'Specification',
                        ])
                        ->required(),
                    Forms\Components\TextInput::make('version')
                        ->label('Version')
                        ->maxLength(50),
                ]),
            
            Forms\Components\Textarea::make('description')
                ->maxLength(1000),
        ]);
    }
}
```

### Example 3: User Profile with Avatar

```php
<?php

namespace App\Filament\Resources;

use TomatoPHP\FilamentMediaManager\Form\MediaManagerInput;
use Filament\Forms;
use Filament\Forms\Form;

class UserResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required()
                ->maxLength(255),
            
            Forms\Components\TextInput::make('email')
                ->email()
                ->required()
                ->maxLength(255),
            
            // User avatar
            MediaManagerInput::make('avatar')
                ->label('Profile Picture')
                ->disk('public')
                ->schema([
                    Forms\Components\TextInput::make('alt_text')
                        ->label('Alt Text')
                        ->default(fn($get) => $get('../../name') . ' profile picture')
                        ->maxLength(255),
                ]),
            
            Forms\Components\Textarea::make('bio')
                ->label('Biography')
                ->maxLength(1000),
        ]);
    }
}
```

### Example 4: Team with Logo and Documents

```php
<?php

namespace App\Filament\Resources;

use TomatoPHP\FilamentMediaManager\Form\MediaManagerInput;
use Filament\Forms;
use Filament\Forms\Form;

class TeamResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required()
                ->maxLength(255),
            
            // Team logo
            MediaManagerInput::make('logo')
                ->label('Team Logo')
                ->disk('public')
                ->schema([
                    Forms\Components\Select::make('logo_type')
                        ->label('Logo Type')
                        ->options([
                            'primary' => 'Primary Logo',
                            'secondary' => 'Secondary Logo',
                            'icon' => 'Icon Only',
                        ])
                        ->required(),
                ]),
            
            // Team documents
            MediaManagerInput::make('documents')
                ->label('Team Documents')
                ->disk('public')
                ->schema([
                    Forms\Components\Select::make('document_category')
                        ->label('Category')
                        ->options([
                            'policy' => 'Policy',
                            'procedure' => 'Procedure',
                            'form' => 'Form',
                            'other' => 'Other',
                        ])
                        ->required(),
                    Forms\Components\TextInput::make('access_level')
                        ->label('Access Level')
                        ->options([
                            'public' => 'Public',
                            'internal' => 'Internal',
                            'confidential' => 'Confidential',
                        ])
                        ->required(),
                ]),
            
            Forms\Components\Textarea::make('description')
                ->maxLength(1000),
        ]);
    }
}
```

## Advanced Configuration Options

### 1. Custom Disk Configuration

```php
MediaManagerInput::make('files')
    ->disk('s3') // Use S3 instead of local storage
    ->schema([
        // ... your custom fields
    ])
```

### 2. Multiple Collections

```php
// In your form schema
MediaManagerInput::make('hero_images')
    ->label('Hero Images')
    ->disk('public'),

MediaManagerInput::make('gallery_images')
    ->label('Gallery Images')
    ->disk('public'),

MediaManagerInput::make('thumbnails')
    ->label('Thumbnails')
    ->disk('public'),
```

### 3. Conditional Fields in Schema

```php
MediaManagerInput::make('media')
    ->schema([
        Forms\Components\Select::make('media_type')
            ->options([
                'image' => 'Image',
                'video' => 'Video',
                'document' => 'Document',
            ])
            ->live()
            ->required(),
        
        Forms\Components\TextInput::make('alt_text')
            ->label('Alt Text')
            ->visible(fn($get) => $get('media_type') === 'image')
            ->maxLength(255),
        
        Forms\Components\TextInput::make('duration')
            ->label('Duration (seconds)')
            ->visible(fn($get) => $get('media_type') === 'video')
            ->numeric(),
        
        Forms\Components\TextInput::make('file_size')
            ->label('File Size (MB)')
            ->visible(fn($get) => $get('media_type') === 'document')
            ->numeric(),
    ])
```

## Tips and Best Practices

1. **Use descriptive collection names**: The collection name becomes the folder name in the media manager.

2. **Add validation to custom fields**: Always validate your custom schema fields appropriately.

3. **Consider file size limits**: Be mindful of upload limits for your server configuration.

4. **Use appropriate disk configurations**: Choose between 'public', 'local', or cloud storage based on your needs.

5. **Organize with folders**: Use the folder structure in the media manager to keep files organized.

6. **Add meaningful metadata**: Use the schema to capture important metadata about your media files.
