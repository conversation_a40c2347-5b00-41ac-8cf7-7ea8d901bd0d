{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "arcanedev/log-viewer": "11.0", "bezhansalleh/filament-shield": "*", "cwsps154/app-settings": "^1.0", "filament/filament": "^3.0", "filament/spatie-laravel-media-library-plugin": "^3.0", "filament/spatie-laravel-settings-plugin": "^3.0", "filament/spatie-laravel-tags-plugin": "^3.0", "filament/spatie-laravel-translatable-plugin": "^3.0", "flowframe/laravel-trend": "^0.2.0", "guzzlehttp/guzzle": "*", "laravel/framework": "^11.0", "laravel/horizon": "^5.21", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.21", "laravel/tinker": "^2.8", "mix-code/filament-multi-2fa": "^1.3", "socialiteproviders/line": "^4.1", "statikbe/laravel-filament-chained-translation-manager": "^3.3", "tomatophp/filament-media-manager": "^1.1", "tomatophp/filament-payments": "^1.0", "tomatophp/filament-subscriptions": "^1.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "barryvdh/laravel-ide-helper": "^3.5", "fakerphp/faker": "^1.9.1", "larastan/larastan": "^2.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^8.1", "phpstan/phpstan-deprecation-rules": "^1.1", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"files": ["app/Helpers/TeamSettingsHelper.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "cs": ["pint"], "pint": "pint", "test:phpstan": "phpstan analyse", "test": ["@test:phpstan"], "ide-helper": ["@php artisan ide-helper:generate", "@php artisan ide-helper:models --write", "@php artisan ide-helper:meta"], "ide-helper:generate": "@php artisan ide-helper:generate", "ide-helper:models": "@php artisan ide-helper:models --write", "ide-helper:meta": "@php artisan ide-helper:meta"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true}}, "minimum-stability": "dev", "prefer-stable": true}