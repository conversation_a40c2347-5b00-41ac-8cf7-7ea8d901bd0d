<div class="space-y-4">
    <div class="text-sm text-gray-600 mb-4">
        Exam completion progress for <strong>{{ $student->name }}</strong>
    </div>
    
    @foreach($examData as $data)
        <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex items-start justify-between mb-3">
                <div class="flex-1">
                    <h4 class="font-medium text-gray-900">{{ $data['exam']->title }}</h4>
                    <div class="flex items-center space-x-2 mt-1">
                        @if($data['exam']->subject)
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ $data['exam']->subject->name }}
                            </span>
                        @endif
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {{ ucfirst($data['exam']->type) }}
                        </span>
                        @if($data['completed'])
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                ✓ Completed
                            </span>
                        @else
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                ✗ Not Completed
                            </span>
                        @endif
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Total Score</div>
                    <div class="font-semibold text-gray-900">{{ $data['exam']->total_score }}</div>
                </div>
            </div>
            
            @if($data['completed'])
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                    <div class="text-center p-3 bg-gray-50 rounded">
                        <div class="text-lg font-semibold text-gray-900">{{ $data['attempts']->count() }}</div>
                        <div class="text-xs text-gray-500">Attempts</div>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded">
                        <div class="text-lg font-semibold text-green-600">{{ $data['best_score'] ?? 0 }}</div>
                        <div class="text-xs text-gray-500">Best Score</div>
                    </div>
                    <div class="text-center p-3 bg-blue-50 rounded">
                        <div class="text-lg font-semibold text-blue-600">{{ $data['latest_score'] ?? 0 }}</div>
                        <div class="text-xs text-gray-500">Latest Score</div>
                    </div>
                </div>
                
                @if($data['attempts']->count() > 0)
                    <div class="border-t pt-3">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">Recent Attempts</h5>
                        <div class="space-y-2">
                            @foreach($data['attempts']->take(3) as $attempt)
                                <div class="flex items-center justify-between text-sm">
                                    <div class="flex items-center space-x-2">
                                        <span class="text-gray-500">Attempt #{{ $attempt->attempt_number }}</span>
                                        <span class="text-gray-400">•</span>
                                        <span class="text-gray-500">{{ $attempt->submitted_at?->format('M j, Y g:i A') }}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="font-medium">{{ $attempt->total_score }}/{{ $data['exam']->total_score }}</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                            @if($attempt->status === 'graded') bg-green-100 text-green-800
                                            @elseif($attempt->status === 'submitted') bg-blue-100 text-blue-800
                                            @else bg-gray-100 text-gray-800
                                            @endif">
                                            {{ ucfirst($attempt->status) }}
                                        </span>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            @else
                <div class="text-center py-4 text-gray-500">
                    <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-sm">Student has not attempted this exam yet</p>
                </div>
            @endif
        </div>
    @endforeach
</div>
