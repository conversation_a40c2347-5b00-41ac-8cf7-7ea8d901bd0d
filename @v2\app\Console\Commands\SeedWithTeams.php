<?php

namespace App\Console\Commands;

use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SeedWithTeams extends Command
{
    protected $signature = 'seed:with-teams {--fresh : Fresh migrate before seeding}';
    protected $description = 'Seed the database with team assignments (team_id = 1)';

    public function handle()
    {
        if ($this->option('fresh')) {
            $this->info('Running fresh migration...');
            Artisan::call('migrate:fresh');
            $this->info('Migration completed.');
        }

        // Ensure we have a team with ID = 1
        $team = Team::find(1);
        if (!$team) {
            $this->info('Creating default team with ID = 1...');
            Team::create([
                'id' => 1,
                'name' => 'Default Team',
                'slug' => 'default-team',
                'description' => 'The default team for seeded data',
                'is_active' => true,
            ]);
        }

        $this->info('Seeding database with team assignments...');
        Artisan::call('db:seed');
        
        $this->info('Verifying team assignments...');
        $this->verifyTeamAssignments();
        
        $this->info('Database seeded successfully with team_id = 1!');
    }

    private function verifyTeamAssignments()
    {
        $models = [
            'users' => \App\Models\User::class,
            'blog_authors' => \App\Models\Blog\Author::class,
            'blog_categories' => \App\Models\Blog\Category::class,
            'blog_posts' => \App\Models\Blog\Post::class,
            'shop_customers' => \App\Models\Shop\Customer::class,
            'shop_brands' => \App\Models\Shop\Brand::class,
            'shop_categories' => \App\Models\Shop\Category::class,
            'shop_products' => \App\Models\Shop\Product::class,
            'shop_orders' => \App\Models\Shop\Order::class,
        ];

        foreach ($models as $name => $modelClass) {
            if (class_exists($modelClass)) {
                $total = $modelClass::count();
                $withTeam = $modelClass::whereNotNull('team_id')->count();
                $withTeamOne = $modelClass::where('team_id', 1)->count();
                
                $this->line("📊 {$name}: {$total} total, {$withTeam} with team_id, {$withTeamOne} with team_id=1");
            }
        }
    }
}
