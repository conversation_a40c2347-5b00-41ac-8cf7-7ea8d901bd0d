<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use TomatoPHP\FilamentSubscriptions\Models\Plan;

class ManageSubscription extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static string $view = 'filament.pages.manage-subscription';

    protected static ?string $navigationGroup = 'Billing';

    protected static ?int $navigationSort = 1;

    protected static ?string $title = 'Manage Subscription';

    public static function canAccess(): bool
    {
        $user = Auth::user();
        
        // Don't show for super admins
        if ($user && $user->team_id === null && $user->hasRole('super_admin')) {
            return false;
        }

        return true;
    }

    public function getViewData(): array
    {
        $user = Auth::user();
        $team = Filament::getTenant();

        if (!$team) {
            return [
                'hasSubscription' => false,
                'availablePlans' => Plan::where('is_active', true)->orderBy('sort_order')->get(),
            ];
        }

        $subscriptionStatus = $team->getSubscriptionStatus();
        $availablePlans = Plan::where('is_active', true)->orderBy('sort_order')->get();
        
        return [
            'hasSubscription' => $subscriptionStatus['has_subscription'],
            'subscriptionStatus' => $subscriptionStatus,
            'availablePlans' => $availablePlans,
            'teamName' => $team->name,
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('viewPricing')
                ->label('View All Plans')
                ->icon('heroicon-o-eye')
                ->url(route('pricing'))
                ->openUrlInNewTab(),
                
            Action::make('upgradePlan')
                ->label('Upgrade Plan')
                ->icon('heroicon-o-arrow-up')
                ->color('success')
                ->visible(fn () => $this->getViewData()['hasSubscription'])
                ->url(route('pricing'))
                ->openUrlInNewTab(),
        ];
    }

    public function cancelSubscription(): void
    {
        $team = Filament::getTenant();
        
        if (!$team) {
            Notification::make()
                ->title('Error')
                ->body('No team found')
                ->danger()
                ->send();
            return;
        }

        $activeSubscription = $team->getActiveSubscription();
        
        if (!$activeSubscription) {
            Notification::make()
                ->title('No Active Subscription')
                ->body('You do not have an active subscription to cancel')
                ->warning()
                ->send();
            return;
        }

        // Cancel the subscription
        $activeSubscription->update([
            'canceled_at' => now(),
        ]);

        Notification::make()
            ->title('Subscription Cancelled')
            ->body('Your subscription has been cancelled. You will continue to have access until the end of your billing period.')
            ->success()
            ->send();

        // Refresh the page data
        $this->redirect(static::getUrl());
    }

    public function resumeSubscription(): void
    {
        $team = Filament::getTenant();
        
        if (!$team) {
            Notification::make()
                ->title('Error')
                ->body('No team found')
                ->danger()
                ->send();
            return;
        }

        $activeSubscription = $team->getActiveSubscription();
        
        if (!$activeSubscription || !$activeSubscription->canceled_at) {
            Notification::make()
                ->title('No Cancelled Subscription')
                ->body('You do not have a cancelled subscription to resume')
                ->warning()
                ->send();
            return;
        }

        // Resume the subscription
        $activeSubscription->update([
            'canceled_at' => null,
        ]);

        Notification::make()
            ->title('Subscription Resumed')
            ->body('Your subscription has been resumed successfully.')
            ->success()
            ->send();

        // Refresh the page data
        $this->redirect(static::getUrl());
    }
}
