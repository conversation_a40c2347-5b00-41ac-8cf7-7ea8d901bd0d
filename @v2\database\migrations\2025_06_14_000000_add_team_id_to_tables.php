<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraint to users table (team_id column already exists)
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('team_id')->references('id')->on('teams')->nullOnDelete();
        });

        // Add team_id to blog tables
        Schema::table('blog_authors', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('blog_categories', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('blog_posts', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('blog_links', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to shop tables
        Schema::table('shop_customers', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('shop_brands', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('shop_categories', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('shop_products', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('shop_orders', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('shop_order_items', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to pivot tables
        Schema::table('shop_category_product', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('taggables', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('tag_id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('addressables', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('address_id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to other content tables
        Schema::table('tags', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('comments', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('media', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to shop_order_addresses (the actual table name)
        Schema::table('shop_order_addresses', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to addresses (from addressable migration)
        Schema::table('addresses', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to shop_payments (the actual table name)
        Schema::table('shop_payments', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to imports/exports
        Schema::table('imports', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('exports', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('failed_import_rows', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to notifications
        Schema::table('notifications', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        // Add team_id to settings
        Schema::table('settings', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });

        Schema::table('app_settings', function (Blueprint $table) {
            $table->foreignId('team_id')->nullable()->after('id')->constrained('teams')->nullOnDelete();
            $table->index('team_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove team_id from all tables in reverse order
        $tables = [
            'app_settings',
            'settings',
            'notifications',
            'failed_import_rows',
            'exports',
            'imports',
            'shop_payments',
            'shop_order_addresses',
            'addresses',
            'addressables',
            'taggables',
            'shop_category_product',
            'media',
            'comments',
            'tags',
            'shop_order_items',
            'shop_orders',
            'shop_products',
            'shop_categories',
            'shop_brands',
            'shop_customers',
            'blog_links',
            'blog_posts',
            'blog_categories',
            'blog_authors',
            'users'
        ];

        foreach ($tables as $tableName) {
            if (Schema::hasTable($tableName) && Schema::hasColumn($tableName, 'team_id')) {
                Schema::table($tableName, function (Blueprint $table) use ($tableName) {
                    // For users table, only drop the foreign key (column exists in base migration)
                    if ($tableName === 'users') {
                        $table->dropForeign(['team_id']);
                    } else {
                        // For other tables, drop foreign key, index, and column
                        $table->dropForeign(['team_id']);
                        $table->dropIndex(['team_id']);
                        $table->dropColumn('team_id');
                    }
                });
            }
        }
    }
};
