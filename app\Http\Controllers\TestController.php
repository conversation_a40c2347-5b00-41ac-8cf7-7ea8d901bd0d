<?php

namespace App\Http\Controllers;

use App\Models\Team;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Support\Str;

class TestController extends Controller
{
    public function __construct()
    {
        // Only allow access when APP_DEBUG is true
        if (!config('app.debug')) {
            abort(404);
        }
    }

    public function index()
    {
        // Get all teams
        $teams = Team::with(['users.roles'])->get();
        
        // Get all resources
        $resources = $this->getFilamentResources();
        
        // Get current test session data
        $currentUser = Auth::user();
        $currentTeam = Filament::getTenant();
        
        return view('test.permissions', compact('teams', 'resources', 'currentUser', 'currentTeam'));
    }

    public function switchUser(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'team_id' => 'nullable|exists:teams,id',
        ]);

        $user = User::findOrFail($request->user_id);
        $team = $request->team_id ? Team::findOrFail($request->team_id) : null;

        // Store original user in session for easy switching back
        if (!Session::has('original_user_id')) {
            Session::put('original_user_id', Auth::id());
        }

        // Login as the selected user
        Auth::login($user);

        // Set tenant if provided or if user is superadmin, get a default team
        if ($team) {
            Filament::setTenant($team);
            Session::put('current_tenant_id', $team->id);
        } else if ($user->team_id === null && $user->hasRole('super_admin')) {
            // For superadmin users, set a default team for testing purposes
            $defaultTeam = \App\Models\Team::where('is_active', true)->first();
            if ($defaultTeam) {
                Filament::setTenant($defaultTeam);
                Session::put('current_tenant_id', $defaultTeam->id);
                $team = $defaultTeam; // Update for success message
            }
        } else if ($user->team_id) {
            // For regular users, use their assigned team
            $userTeam = $user->team;
            if ($userTeam) {
                Filament::setTenant($userTeam);
                Session::put('current_tenant_id', $userTeam->id);
                $team = $userTeam;
            }
        } else {
            Session::forget('current_tenant_id');
        }

        return redirect()->back()->with('success', "Switched to user: {$user->name}" . ($team ? " in team: {$team->name}" : ''));
    }

    public function switchBack()
    {
        $originalUserId = Session::get('original_user_id');
        
        if ($originalUserId) {
            $originalUser = User::find($originalUserId);
            if ($originalUser) {
                Auth::login($originalUser);
                Session::forget('original_user_id');
                Session::forget('current_tenant_id');
                
                return redirect()->back()->with('success', "Switched back to: {$originalUser->name}");
            }
        }

        return redirect()->back()->with('error', 'Could not switch back to original user');
    }

    public function testResource(Request $request)
    {
        $request->validate([
            'resource' => 'required|string',
        ]);

        $resourceClass = $request->resource;

        // Decode the resource class if it's URL encoded
        $resourceClass = urldecode($resourceClass);

        if (!class_exists($resourceClass) || !is_subclass_of($resourceClass, Resource::class)) {
            return response()->json([
                'error' => 'Invalid resource class: ' . $resourceClass,
                'exists' => class_exists($resourceClass),
                'is_resource' => class_exists($resourceClass) ? is_subclass_of($resourceClass, Resource::class) : false
            ], 400);
        }

        try {
            $permissions = [
                'canViewAny' => $resourceClass::canViewAny(),
                'canCreate' => $resourceClass::canCreate(),
            ];

            // Try to get a sample record for record-specific permissions
            $model = $resourceClass::getModel();
            $record = null;
            
            $currentTeam = Filament::getTenant();
            if ($currentTeam) {
                $record = $model::where('team_id', $currentTeam->id)->first();
            } else {
                $record = $model::first();
            }

            if ($record) {
                $permissions['canView'] = $resourceClass::canView($record);
                $permissions['canEdit'] = $resourceClass::canEdit($record);
                $permissions['canDelete'] = $resourceClass::canDelete($record);
            }

            return response()->json([
                'success' => true,
                'resource' => class_basename($resourceClass),
                'permissions' => $permissions,
                'user' => Auth::user()->name,
                'team' => $currentTeam?->name,
                'record_tested' => $record ? true : false,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error testing resource: ' . $e->getMessage()
            ], 500);
        }
    }

    public function testAllResources()
    {
        $resources = $this->getFilamentResources();
        $results = [];
        $currentTeam = Filament::getTenant();

        foreach ($resources as $resourceClass) {
            try {
                $permissions = [
                    'canViewAny' => $resourceClass::canViewAny(),
                    'canCreate' => $resourceClass::canCreate(),
                ];

                // Try to get a sample record
                $model = $resourceClass::getModel();
                $record = null;
                
                if ($currentTeam) {
                    $record = $model::where('team_id', $currentTeam->id)->first();
                } else {
                    $record = $model::first();
                }

                if ($record) {
                    $permissions['canView'] = $resourceClass::canView($record);
                    $permissions['canEdit'] = $resourceClass::canEdit($record);
                    $permissions['canDelete'] = $resourceClass::canDelete($record);
                }

                $results[class_basename($resourceClass)] = $permissions;

            } catch (\Exception $e) {
                $results[class_basename($resourceClass)] = ['error' => $e->getMessage()];
            }
        }

        return response()->json([
            'success' => true,
            'results' => $results,
            'user' => Auth::user()->name,
            'team' => $currentTeam?->name,
        ]);
    }

    public function getResourceUrls(Request $request)
    {
        $request->validate([
            'resource' => 'required|string',
        ]);

        $resourceClass = $request->resource;

        // Decode the resource class if it's URL encoded
        $resourceClass = urldecode($resourceClass);

        if (!class_exists($resourceClass) || !is_subclass_of($resourceClass, Resource::class)) {
            return response()->json([
                'error' => 'Invalid resource class: ' . $resourceClass,
                'exists' => class_exists($resourceClass),
                'is_resource' => class_exists($resourceClass) ? is_subclass_of($resourceClass, Resource::class) : false
            ], 400);
        }

        try {
            $currentTeam = Filament::getTenant();
            $currentUser = Auth::user();
            $urls = [];

            // For superadmin users without a current tenant, try to get a default team
            if (!$currentTeam && $currentUser && $currentUser->team_id === null && $currentUser->hasRole('super_admin')) {
                $defaultTeam = \App\Models\Team::where('is_active', true)->first();
                if ($defaultTeam) {
                    $currentTeam = $defaultTeam;
                    // Temporarily set the tenant for URL generation
                    Filament::setTenant($defaultTeam);
                }
            }

            // Get resource URLs with proper tenant parameter handling
            if ($resourceClass::canViewAny()) {
                try {
                    $urls['index'] = $resourceClass::getUrl('index', [], true, $currentTeam?->slug);
                } catch (\Exception $e) {
                    // If tenant is required but missing, try with a default tenant
                    if ($currentTeam) {
                        $urls['index'] = $resourceClass::getUrl('index', ['tenant' => $currentTeam->slug]);
                    } else {
                        $urls['index'] = 'Error: ' . $e->getMessage();
                    }
                }
            }

            if ($resourceClass::canCreate()) {
                try {
                    $urls['create'] = $resourceClass::getUrl('create', [], true, $currentTeam?->slug);
                } catch (\Exception $e) {
                    if ($currentTeam) {
                        $urls['create'] = $resourceClass::getUrl('create', ['tenant' => $currentTeam->slug]);
                    } else {
                        $urls['create'] = 'Error: ' . $e->getMessage();
                    }
                }
            }

            // Try to get a sample record for edit/view URLs
            $model = $resourceClass::getModel();
            $record = null;

            if ($currentTeam) {
                $record = $model::where('team_id', $currentTeam->id)->first();
            } else {
                $record = $model::first();
            }

            if ($record) {
                if ($resourceClass::canView($record)) {
                    try {
                        $urls['view'] = $resourceClass::getUrl('view', ['record' => $record], true, $currentTeam?->slug);
                    } catch (\Exception $e) {
                        if ($currentTeam) {
                            $urls['view'] = $resourceClass::getUrl('view', ['record' => $record, 'tenant' => $currentTeam->slug]);
                        } else {
                            $urls['view'] = 'Error: ' . $e->getMessage();
                        }
                    }
                }
                if ($resourceClass::canEdit($record)) {
                    try {
                        $urls['edit'] = $resourceClass::getUrl('edit', ['record' => $record], true, $currentTeam?->slug);
                    } catch (\Exception $e) {
                        if ($currentTeam) {
                            $urls['edit'] = $resourceClass::getUrl('edit', ['record' => $record, 'tenant' => $currentTeam->slug]);
                        } else {
                            $urls['edit'] = 'Error: ' . $e->getMessage();
                        }
                    }
                }
            }

            return response()->json([
                'success' => true,
                'urls' => $urls,
                'resource' => class_basename($resourceClass),
                'current_team' => $currentTeam?->name,
                'is_superadmin' => $currentUser && $currentUser->team_id === null && $currentUser->hasRole('super_admin'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error getting resource URLs: ' . $e->getMessage(),
                'trace' => config('app.debug') ? $e->getTraceAsString() : null
            ], 500);
        }
    }

    protected function getFilamentResources(): array
    {
        $resourcePaths = [
            app_path('Filament/Resources'),
        ];

        $resources = [];

        foreach ($resourcePaths as $path) {
            if (!is_dir($path)) continue;

            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($path)
            );

            foreach ($files as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $relativePath = str_replace($path . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $className = 'App\\Filament\\Resources\\' . str_replace(['/', '.php'], ['\\', ''], $relativePath);

                    if (class_exists($className) && is_subclass_of($className, Resource::class)) {
                        $resources[] = $className;
                    }
                }
            }
        }

        return $resources;
    }

    /**
     * Show SVG demo page
     */
    public function svgDemo()
    {
        return view('test.svg-demo');
    }

    /**
     * Show SafeIcon demo page
     */
    public function safeIconDemo()
    {
        return view('test.safeicon-demo');
    }

    /**
     * Test dashboard with all testing tools
     */
    public function dashboard()
    {
        return view('test.dashboard');
    }
}
