<x-filament-panels::page>
    @if(!$this->getViewData()['hasSubscription'])
        <!-- No Subscription State -->
        <div class="space-y-6">
            <x-filament::section>
                <x-slot name="heading">
                    No Active Subscription
                </x-slot>
                
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No subscription found</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ $this->getViewData()['teamName'] }} doesn't have an active subscription.</p>
                    <div class="mt-6">
                        <a href="{{ route('pricing') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            Choose a Plan
                        </a>
                    </div>
                </div>
            </x-filament::section>

            <!-- Available Plans -->
            <x-filament::section>
                <x-slot name="heading">
                    Available Plans
                </x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($this->getViewData()['availablePlans']->take(6) as $plan)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h4 class="font-semibold text-gray-900 dark:text-gray-100">{{ $plan->name }}</h4>
                        <p class="text-2xl font-bold text-gray-900 dark:text-gray-100 mt-2">
                            ${{ number_format($plan->price, 2) }}
                            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">/ {{ $plan->invoice_interval }}</span>
                        </p>
                        @if($plan->trial_period > 0)
                        <p class="text-sm text-green-600 dark:text-green-400 mt-1">{{ $plan->trial_period }}-day free trial</p>
                        @endif
                        <a href="{{ route('pricing.select', $plan->slug) }}" class="mt-3 block w-full text-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                            Select Plan
                        </a>
                    </div>
                    @endforeach
                </div>
            </x-filament::section>
        </div>
    @else
        <!-- Active Subscription State -->
        <div class="space-y-6">
            @php $status = $this->getViewData()['subscriptionStatus']; @endphp
            
            <!-- Current Plan -->
            <x-filament::section>
                <x-slot name="heading">
                    Current Subscription
                </x-slot>
                
                <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-green-900 dark:text-green-100">{{ $status['plan_name'] }}</h3>
                            <p class="text-sm text-green-700 dark:text-green-300 mt-1">Active subscription</p>
                        </div>
                        <div class="text-right">
                            @if($status['subscription_ends_at'])
                            <p class="text-sm text-green-600 dark:text-green-400">
                                @if($status['subscription_canceled_at'])
                                    Expires: {{ \Carbon\Carbon::parse($status['subscription_ends_at'])->format('M d, Y') }}
                                @else
                                    Renews: {{ \Carbon\Carbon::parse($status['subscription_ends_at'])->format('M d, Y') }}
                                @endif
                            </p>
                            @endif
                            @if($status['subscription_canceled_at'])
                            <p class="text-sm text-red-600 dark:text-red-400">
                                Cancelled: {{ \Carbon\Carbon::parse($status['subscription_canceled_at'])->format('M d, Y') }}
                            </p>
                            @endif
                        </div>
                    </div>
                </div>
            </x-filament::section>

            <!-- Usage Statistics -->
            <x-filament::section>
                <x-slot name="heading">
                    Usage & Limits
                </x-slot>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Students -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Students</h4>
                            @if($status['can_add_students'])
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                                    Can add more
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200">
                                    Limit reached
                                </span>
                            @endif
                        </div>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ $status['student_count'] }}</p>
                            <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">of {{ $status['student_limit'] }}</p>
                        </div>
                        <div class="mt-3">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-600 dark:bg-blue-400 h-2 rounded-full" style="width: {{ ($status['student_count'] / max($status['student_limit'], 1)) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Parents -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100">Parents</h4>
                            @if($status['can_add_parents'])
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                                    Can add more
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200">
                                    Limit reached
                                </span>
                            @endif
                        </div>
                        <div class="flex items-baseline">
                            <p class="text-2xl font-semibold text-gray-900 dark:text-gray-100">{{ $status['parent_count'] }}</p>
                            <p class="ml-2 text-sm text-gray-500 dark:text-gray-400">of {{ $status['parent_limit'] }}</p>
                        </div>
                        <div class="mt-3">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-green-600 dark:bg-green-400 h-2 rounded-full" style="width: {{ ($status['parent_count'] / max($status['parent_limit'], 1)) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </x-filament::section>

            <!-- Subscription Actions -->
            <x-filament::section>
                <x-slot name="heading">
                    Subscription Management
                </x-slot>
                
                <div class="flex flex-col sm:flex-row gap-4">
                    @if($status['subscription_canceled_at'])
                        <x-filament::button
                            wire:click="resumeSubscription"
                            color="success"
                            icon="heroicon-o-play"
                        >
                            Resume Subscription
                        </x-filament::button>
                    @else
                        <x-filament::button
                            wire:click="cancelSubscription"
                            color="danger"
                            icon="heroicon-o-x-mark"
                            wire:confirm="Are you sure you want to cancel your subscription? You will continue to have access until the end of your billing period."
                        >
                            Cancel Subscription
                        </x-filament::button>
                    @endif
                    
                    <x-filament::button
                        tag="a"
                        href="{{ route('pricing') }}"
                        target="_blank"
                        color="gray"
                        icon="heroicon-o-arrow-up"
                    >
                        Upgrade Plan
                    </x-filament::button>
                </div>
            </x-filament::section>
        </div>
    @endif
</x-filament-panels::page>
