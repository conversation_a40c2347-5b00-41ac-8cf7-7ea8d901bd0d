document.addEventListener("DOMContentLoaded",function(){const m=document.getElementById("menu-toggle"),r=document.getElementById("mobile-menu"),g=document.getElementById("close-menu");m&&r&&m.addEventListener("click",function(){r.classList.add("active")}),g&&r&&g.addEventListener("click",function(){r.classList.remove("active")});const b=document.getElementById("assignmentForm");document.getElementById("classSelect");const f=document.getElementById("subjectSelect"),a=document.getElementById("bookSelect"),d=document.getElementById("lessonSelect"),o=document.getElementById("lessonSelectContainer"),p={\u0E20\u0E32\u0E29\u0E32\u0E44\u0E17\u0E22:["\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E44\u0E17\u0E22 \u0E1B.5","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E44\u0E17\u0E22 \u0E1B.6","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E44\u0E17\u0E22 \u0E21.1","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E44\u0E17\u0E22 \u0E21.2"],\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C:["\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E1B.5","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E1B.6","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E21.1","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E21.2"],\u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C:["\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E1B.5","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E1B.6","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E21.1","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E21.2"],\u0E2A\u0E31\u0E07\u0E04\u0E21\u0E28\u0E36\u0E01\u0E29\u0E32:["\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E2A\u0E31\u0E07\u0E04\u0E21\u0E28\u0E36\u0E01\u0E29\u0E32 \u0E1B.5","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E2A\u0E31\u0E07\u0E04\u0E21\u0E28\u0E36\u0E01\u0E29\u0E32 \u0E1B.6","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E2A\u0E31\u0E07\u0E04\u0E21\u0E28\u0E36\u0E01\u0E29\u0E32 \u0E21.1","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E2A\u0E31\u0E07\u0E04\u0E21\u0E28\u0E36\u0E01\u0E29\u0E32 \u0E21.2"],\u0E20\u0E32\u0E29\u0E32\u0E2D\u0E31\u0E07\u0E01\u0E24\u0E29:["\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E2D\u0E31\u0E07\u0E01\u0E24\u0E29 \u0E1B.5","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E2D\u0E31\u0E07\u0E01\u0E24\u0E29 \u0E1B.6","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E2D\u0E31\u0E07\u0E01\u0E24\u0E29 \u0E21.1","\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E2D\u0E31\u0E07\u0E01\u0E24\u0E29 \u0E21.2"]},v={"\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E1B.5":["\u0E1A\u0E17\u0E17\u0E35\u0E48 1 - \u0E1E\u0E37\u0E49\u0E19\u0E10\u0E32\u0E19\u0E01\u0E32\u0E23\u0E04\u0E33\u0E19\u0E27\u0E13","\u0E1A\u0E17\u0E17\u0E35\u0E48 2 - \u0E40\u0E28\u0E29\u0E2A\u0E48\u0E27\u0E19\u0E41\u0E25\u0E30\u0E17\u0E28\u0E19\u0E34\u0E22\u0E21","\u0E1A\u0E17\u0E17\u0E35\u0E48 3 - \u0E23\u0E39\u0E1B\u0E40\u0E23\u0E02\u0E32\u0E04\u0E13\u0E34\u0E15","\u0E1A\u0E17\u0E17\u0E35\u0E48 4 - \u0E01\u0E32\u0E23\u0E27\u0E31\u0E14","\u0E1A\u0E17\u0E17\u0E35\u0E48 5 - \u0E2A\u0E16\u0E34\u0E15\u0E34\u0E41\u0E25\u0E30\u0E04\u0E27\u0E32\u0E21\u0E19\u0E48\u0E32\u0E08\u0E30\u0E40\u0E1B\u0E47\u0E19"],"\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E20\u0E32\u0E29\u0E32\u0E44\u0E17\u0E22 \u0E1B.5":["\u0E1A\u0E17\u0E17\u0E35\u0E48 1 - \u0E01\u0E32\u0E23\u0E2D\u0E48\u0E32\u0E19\u0E41\u0E25\u0E30\u0E01\u0E32\u0E23\u0E40\u0E02\u0E35\u0E22\u0E19","\u0E1A\u0E17\u0E17\u0E35\u0E48 2 - \u0E27\u0E23\u0E23\u0E13\u0E04\u0E14\u0E35\u0E44\u0E17\u0E22","\u0E1A\u0E17\u0E17\u0E35\u0E48 3 - \u0E44\u0E27\u0E22\u0E32\u0E01\u0E23\u0E13\u0E4C","\u0E1A\u0E17\u0E17\u0E35\u0E48 4 - \u0E01\u0E32\u0E23\u0E40\u0E02\u0E35\u0E22\u0E19\u0E40\u0E23\u0E35\u0E22\u0E07\u0E04\u0E27\u0E32\u0E21"],"\u0E2B\u0E19\u0E31\u0E07\u0E2A\u0E37\u0E2D\u0E40\u0E23\u0E35\u0E22\u0E19\u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C \u0E1B.5":["\u0E1A\u0E17\u0E17\u0E35\u0E48 1 - \u0E2A\u0E34\u0E48\u0E07\u0E21\u0E35\u0E0A\u0E35\u0E27\u0E34\u0E15\u0E41\u0E25\u0E30\u0E2A\u0E34\u0E48\u0E07\u0E44\u0E21\u0E48\u0E21\u0E35\u0E0A\u0E35\u0E27\u0E34\u0E15","\u0E1A\u0E17\u0E17\u0E35\u0E48 2 - \u0E41\u0E23\u0E07\u0E41\u0E25\u0E30\u0E01\u0E32\u0E23\u0E40\u0E04\u0E25\u0E37\u0E48\u0E2D\u0E19\u0E17\u0E35\u0E48","\u0E1A\u0E17\u0E17\u0E35\u0E48 3 - \u0E41\u0E2A\u0E07\u0E41\u0E25\u0E30\u0E40\u0E2A\u0E35\u0E22\u0E07","\u0E1A\u0E17\u0E17\u0E35\u0E48 4 - \u0E2A\u0E32\u0E23\u0E41\u0E25\u0E30\u0E2A\u0E21\u0E1A\u0E31\u0E15\u0E34"]};f&&a&&f.addEventListener("change",function(){const s=this.value;a.innerHTML='<option value="\u0E44\u0E21\u0E48\u0E23\u0E30\u0E1A\u0E38">\u0E44\u0E21\u0E48\u0E23\u0E30\u0E1A\u0E38</option>',s&&p[s]&&p[s].forEach(t=>{const e=document.createElement("option");e.value=t,e.textContent=t,a.appendChild(e)}),o&&(o.style.display="none")}),a&&d&&o&&a.addEventListener("change",function(){const s=this.value;s!=="\u0E44\u0E21\u0E48\u0E23\u0E30\u0E1A\u0E38"&&v[s]?(d.innerHTML='<option value="" disabled selected>\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E1A\u0E17\u0E40\u0E23\u0E35\u0E22\u0E19</option>',v[s].forEach(t=>{const e=document.createElement("option");e.value=t,e.textContent=t,d.appendChild(e)}),o.style.display="block"):o.style.display="none"});const u=document.querySelectorAll(".checkbox-item");u.forEach(s=>{s.addEventListener("change",function(){const t=this.nextElementSibling;this.checked&&(t.classList.add("success-bounce"),setTimeout(()=>{t.classList.remove("success-bounce")},600))})}),b&&b.addEventListener("submit",function(s){s.preventDefault(),new FormData(this);const t=[];if(u.forEach(n=>{if(n.checked){const l=n.nextElementSibling.querySelector("span").textContent;t.push(l)}}),t.length===0){c("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21\u0E2D\u0E22\u0E48\u0E32\u0E07\u0E19\u0E49\u0E2D\u0E22 1 \u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21","error");return}const e=this.querySelector('button[type="submit"]'),i=e.innerHTML;e.innerHTML='<i class="fas fa-spinner fa-spin mr-2"></i>\u0E01\u0E33\u0E25\u0E31\u0E07\u0E21\u0E2D\u0E1A\u0E2B\u0E21\u0E32\u0E22...',e.disabled=!0,setTimeout(()=>{e.innerHTML=i,e.disabled=!1,c("\u0E21\u0E2D\u0E1A\u0E2B\u0E21\u0E32\u0E22\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27","success"),this.reset(),o&&(o.style.display="none"),u.forEach(n=>{n.checked=!1,n.nextElementSibling.classList.remove("bg-indigo-50","border-indigo-500")}),S(t)},2e3)}),document.querySelectorAll(".activity-card").forEach(s=>{s.querySelectorAll("button").forEach(e=>{e.addEventListener("click",function(){const i=this.textContent.trim(),n=s.querySelector("h3").textContent;switch(i){case"\u0E14\u0E39\u0E23\u0E32\u0E22\u0E25\u0E30\u0E40\u0E2D\u0E35\u0E22\u0E14":x(n);break;case"\u0E1B\u0E34\u0E14\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21":y(s);break;case"\u0E40\u0E01\u0E47\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25":h();break}})})});function x(s){const t=document.createElement("div");t.className="modal-overlay fixed inset-0 z-50 flex items-center justify-center",t.innerHTML=`
            <div class="modal-content bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">${s}</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">\u0E08\u0E33\u0E19\u0E27\u0E19\u0E19\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19\u0E17\u0E35\u0E48\u0E2A\u0E48\u0E07</h4>
                            <p class="text-2xl font-bold text-blue-600">22/38 \u0E04\u0E19</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">\u0E04\u0E30\u0E41\u0E19\u0E19\u0E40\u0E09\u0E25\u0E35\u0E48\u0E22</h4>
                            <p class="text-2xl font-bold text-green-600">78.5 \u0E04\u0E30\u0E41\u0E19\u0E19</p>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">\u0E19\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19\u0E17\u0E35\u0E48\u0E22\u0E31\u0E07\u0E44\u0E21\u0E48\u0E2A\u0E48\u0E07\u0E07\u0E32\u0E19</h4>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">\u0E19.\u0E2A.\u0E2A\u0E21\u0E43\u0E08 \u0E43\u0E08\u0E14\u0E35</span>
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">\u0E19\u0E32\u0E22\u0E2A\u0E21\u0E0A\u0E32\u0E22 \u0E14\u0E35\u0E43\u0E08</span>
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">\u0E19.\u0E2A.\u0E2A\u0E21\u0E2B\u0E27\u0E31\u0E07 \u0E23\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19</span>
                        </div>
                    </div>
                </div>
            </div>
        `,document.body.appendChild(t),t.querySelector(".close-modal").addEventListener("click",function(){document.body.removeChild(t)}),t.addEventListener("click",function(i){i.target===t&&document.body.removeChild(t)})}function y(s){confirm("\u0E04\u0E38\u0E13\u0E15\u0E49\u0E2D\u0E07\u0E01\u0E32\u0E23\u0E1B\u0E34\u0E14\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21\u0E19\u0E35\u0E49\u0E2B\u0E23\u0E37\u0E2D\u0E44\u0E21\u0E48?")&&(s.style.transition="all 0.5s ease",s.style.transform="scale(0.8)",s.style.opacity="0.5",setTimeout(()=>{const t=s.querySelector(".bg-indigo-100, .bg-blue-100, .bg-purple-100"),e=s.querySelector(".bg-indigo-600, .bg-blue-600, .bg-purple-600");t&&(t.className=t.className.replace(/bg-(indigo|blue|purple)-100/,"bg-gray-100")),e&&(e.className=e.className.replace(/bg-(indigo|blue|purple)-600/,"bg-gray-600"),e.textContent="\u0E40\u0E2A\u0E23\u0E47\u0E08\u0E2A\u0E34\u0E49\u0E19"),s.style.transform="scale(1)",s.style.opacity="1",c("\u0E1B\u0E34\u0E14\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27","success")},500))}function h(s){c("\u0E01\u0E33\u0E25\u0E31\u0E07\u0E40\u0E15\u0E23\u0E35\u0E22\u0E21\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E14\u0E32\u0E27\u0E19\u0E4C\u0E42\u0E2B\u0E25\u0E14...","info"),setTimeout(()=>{c("\u0E14\u0E32\u0E27\u0E19\u0E4C\u0E42\u0E2B\u0E25\u0E14\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27","success")},2e3)}function S(s){const t=document.querySelector(".grid.grid-cols-1.md\\:grid-cols-2");if(!t)return;const e=document.createElement("div");e.className="activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow card-fade-in";const i=new Date,n=new Date(i.getTime()+3*24*60*60*1e3);e.innerHTML=`
            <div class="bg-green-100 px-4 py-3 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="font-bold text-green-800">\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21\u0E43\u0E2B\u0E21\u0E48</h3>
                    <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">\u0E01\u0E33\u0E25\u0E31\u0E07\u0E14\u0E33\u0E40\u0E19\u0E34\u0E19\u0E01\u0E32\u0E23</span>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-2">
                    <i class="far fa-calendar-alt text-green-500 mr-2"></i>
                    <span class="text-sm">\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\u0E2A\u0E2D\u0E19: ${i.toLocaleDateString("th-TH")}</span>
                </div>
                <div class="flex items-center mb-3">
                    <i class="far fa-calendar-check text-green-500 mr-2"></i>
                    <span class="text-sm">\u0E01\u0E33\u0E2B\u0E19\u0E14\u0E2A\u0E48\u0E07: ${n.toLocaleDateString("th-TH")} (16:00 \u0E19.)</span>
                </div>
                <div class="mb-3">
                    <h4 class="text-sm font-medium mb-2">\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21:</h4>
                    <div class="flex flex-wrap gap-2">
                        ${s.map(l=>`<span class="bg-green-50 text-green-700 text-xs px-2 py-1 rounded-full">${l}</span>`).join("")}
                    </div>
                </div>
                <div class="border-t border-gray-100 pt-3 mb-3">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-sm font-medium">
                            <i class="fas fa-users text-green-500 mr-1"></i>
                            \u0E08\u0E33\u0E19\u0E27\u0E19\u0E19\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19\u0E43\u0E19\u0E2B\u0E49\u0E2D\u0E07: 0 \u0E04\u0E19
                        </span>
                        <span class="text-sm font-medium text-green-600">\u0E2A\u0E48\u0E07\u0E41\u0E25\u0E49\u0E27: 0 \u0E04\u0E19</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-fill" style="width: 0%; background: linear-gradient(90deg, #10B981 0%, #34D399 100%);"></div>
                    </div>
                </div>
                <div class="flex flex-wrap gap-2">
                    <button class="bg-green-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                        <i class="fas fa-eye mr-1"></i>
                        \u0E14\u0E39\u0E23\u0E32\u0E22\u0E25\u0E30\u0E40\u0E2D\u0E35\u0E22\u0E14
                    </button>
                    <button class="bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center">
                        <i class="fas fa-times-circle mr-1"></i>
                        \u0E1B\u0E34\u0E14\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21
                    </button>
                    <button class="bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center">
                        <i class="fas fa-download mr-1"></i>
                        \u0E40\u0E01\u0E47\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25
                    </button>
                </div>
            </div>
        `,t.insertBefore(e,t.firstChild),e.querySelectorAll("button").forEach(l=>{l.addEventListener("click",function(){const k=this.textContent.trim(),C=e.querySelector("h3").textContent;switch(k){case"\u0E14\u0E39\u0E23\u0E32\u0E22\u0E25\u0E30\u0E40\u0E2D\u0E35\u0E22\u0E14":x(C);break;case"\u0E1B\u0E34\u0E14\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21":y(e);break;case"\u0E40\u0E01\u0E47\u0E1A\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25":h();break}})})}function c(s,t="info"){const e=document.createElement("div");switch(e.className="notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300",t){case"success":e.classList.add("bg-green-500","text-white");break;case"error":e.classList.add("bg-red-500","text-white");break;case"warning":e.classList.add("bg-yellow-500","text-white");break;default:e.classList.add("bg-blue-500","text-white")}e.innerHTML=`
            <div class="flex items-center">
                <span>${s}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `,document.body.appendChild(e),setTimeout(()=>{e.parentElement&&(e.style.transform="translateX(100%)",setTimeout(()=>{e.remove()},300))},5e3)}const E=document.querySelector(".lg\\:col-span-1"),w=document.querySelector(".lg\\:col-span-2");E&&E.classList.add("form-slide-in"),w&&w.classList.add("card-fade-in")});
