@extends('layouts.frontend')

@section('title', 'Checkout - ' . $plan->name)

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-blue-600 px-6 py-4">
                <h1 class="text-2xl font-bold text-white">Complete Your Subscription</h1>
                <p class="text-blue-100 mt-1">You're subscribing to {{ $plan->name }}</p>
            </div>

            <div class="p-6">
                <!-- Plan Summary -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-2">Plan Summary</h2>
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-medium text-gray-900">{{ $plan->name }}</h3>
                            <p class="text-sm text-gray-600">{{ strip_tags($plan->description) }}</p>
                            @if($plan->trial_period > 0)
                            <p class="text-sm text-green-600 font-medium mt-1">
                                {{ $plan->trial_period }}-day free trial included
                            </p>
                            @endif
                        </div>
                        <div class="text-right">
                            <p class="text-2xl font-bold text-gray-900">${{ number_format($plan->price, 2) }}</p>
                            <p class="text-sm text-gray-600">per {{ $plan->invoice_interval }}</p>
                        </div>
                    </div>
                </div>

                @if($existingSubscription)
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Existing Subscription</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>You already have an active subscription. This new subscription will replace your current one.</p>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                <!-- Payment Form -->
                <form action="{{ route('subscription.process') }}" method="POST" id="checkout-form">
                    @csrf
                    <input type="hidden" name="plan_slug" value="{{ $plan->slug }}">

                    <!-- Payment Methods -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Choose Payment Method</h3>
                        
                        <div class="space-y-3">
                            <!-- PayPal (Credit Card) -->
                            <label class="relative flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method" value="paypal" class="sr-only" required>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.26-.93 4.778-4.005 7.201-9.138 7.201h-2.19a.563.563 0 0 0-.556.479l-1.187 7.527h-.506l-.24 1.516a.641.641 0 0 0 .633.74h3.94a.563.563 0 0 0 .556-.479l.035-.22.671-4.25.043-.28a.563.563 0 0 1 .556-.479h.35c3.73 0 6.65-1.514 7.5-5.89.354-1.834.166-3.368-.77-4.583z"/>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <span class="block text-sm font-medium text-gray-900">Credit Card / PayPal</span>
                                        <span class="block text-sm text-gray-500">Visa, Mastercard, American Express, PayPal</span>
                                    </div>
                                </div>
                                <div class="absolute inset-0 border-2 border-transparent rounded-lg pointer-events-none"></div>
                            </label>

                            <!-- Bank Transfer -->
                            <label class="relative flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method" value="bank-transfer" class="sr-only">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <span class="block text-sm font-medium text-gray-900">Bank Transfer</span>
                                        <span class="block text-sm text-gray-500">Direct transfer from your bank account</span>
                                    </div>
                                </div>
                                <div class="absolute inset-0 border-2 border-transparent rounded-lg pointer-events-none"></div>
                            </label>

                            <!-- PromptPay QR Code -->
                            <label class="relative flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                <input type="radio" name="payment_method" value="promptpay" class="sr-only">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-6 w-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1zm12 0h2a1 1 0 001-1V6a1 1 0 00-1-1h-2a1 1 0 00-1 1v1a1 1 0 001 1zM5 20h2a1 1 0 001-1v-1a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <span class="block text-sm font-medium text-gray-900">PromptPay QR Code</span>
                                        <span class="block text-sm text-gray-500">Scan QR code with your mobile banking app</span>
                                    </div>
                                </div>
                                <div class="absolute inset-0 border-2 border-transparent rounded-lg pointer-events-none"></div>
                            </label>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-6">
                        <label class="flex items-start">
                            <input type="checkbox" class="mt-1 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500" required>
                            <span class="ml-2 text-sm text-gray-600">
                                I agree to the <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a> and <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
                            </span>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex items-center justify-between">
                        <a href="{{ route('pricing') }}" class="text-gray-600 hover:text-gray-500">
                            ← Back to pricing
                        </a>
                        <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            @if($plan->trial_period > 0)
                                Start Free Trial
                            @else
                                Subscribe Now
                            @endif
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Remove selected styling from all labels
            paymentMethods.forEach(m => {
                const label = m.closest('label');
                const border = label.querySelector('.absolute');
                border.classList.remove('border-blue-500', 'bg-blue-50');
            });
            
            // Add selected styling to chosen method
            if (this.checked) {
                const label = this.closest('label');
                const border = label.querySelector('.absolute');
                border.classList.add('border-blue-500', 'bg-blue-50');
            }
        });
    });
});
</script>
@endsection
