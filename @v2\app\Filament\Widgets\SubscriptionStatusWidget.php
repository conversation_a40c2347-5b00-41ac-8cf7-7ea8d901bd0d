<?php

namespace App\Filament\Widgets;

use Filament\Widgets\Widget;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;

class SubscriptionStatusWidget extends Widget
{
    protected static string $view = 'filament.widgets.subscription-status';

    protected int | string | array $columnSpan = 'full';

    protected static ?int $sort = -10; // Show at the top

    public static function canView(): bool
    {
        $user = Auth::user();
        
        // Don't show for super admins
        if ($user && $user->team_id === null && $user->hasRole('super_admin')) {
            return false;
        }

        return true;
    }

    public function getViewData(): array
    {
        $user = Auth::user();
        $team = Filament::getTenant();

        if (!$team) {
            return [
                'hasSubscription' => false,
                'status' => 'no_team',
                'message' => 'No team assigned',
            ];
        }

        $subscriptionStatus = $team->getSubscriptionStatus();
        
        return [
            'hasSubscription' => $subscriptionStatus['has_subscription'],
            'status' => $subscriptionStatus['status'],
            'planName' => $subscriptionStatus['plan_name'] ?? null,
            'studentCount' => $subscriptionStatus['student_count'] ?? 0,
            'studentLimit' => $subscriptionStatus['student_limit'] ?? 0,
            'parentCount' => $subscriptionStatus['parent_count'] ?? 0,
            'parentLimit' => $subscriptionStatus['parent_limit'] ?? 0,
            'canAddStudents' => $subscriptionStatus['can_add_students'] ?? false,
            'canAddParents' => $subscriptionStatus['can_add_parents'] ?? false,
            'subscriptionEndsAt' => $subscriptionStatus['subscription_ends_at'] ?? null,
            'subscriptionCanceledAt' => $subscriptionStatus['subscription_canceled_at'] ?? null,
            'teamName' => $team->name,
        ];
    }
}
