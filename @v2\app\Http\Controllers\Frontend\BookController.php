<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Book;

class BookController extends Controller
{
    public function index()
    {
        // Frontend shows books from all teams (no tenant scoping)
        $books = Book::withoutGlobalScopes()
            ->where('is_active', true)
            ->with(['subject', 'team'])
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('frontend.books.index', compact('books'));
    }

    public function show($bookId)
    {
        // Find book without global scopes
        $book = Book::withoutGlobalScopes()->findOrFail($bookId);

        // Check if book is active
        if (!$book->is_active) {
            abort(404);
        }

        // Load relationships without tenant scoping
        $book->loadMissing(['subject', 'team', 'lessons' => function ($query) {
            $query->withoutGlobalScopes()->where('is_active', true)->orderBy('sort_order');
        }]);

        return view('frontend.books.show', compact('book'));
    }
}
