// Teacher Timetable JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle (inherited from layout)
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeMenu = document.getElementById('close-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('active');
        });
    }

    if (closeMenu && mobileMenu) {
        closeMenu.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
        });
    }

    // Form elements
    const dateInput = document.getElementById('date');
    const timeSelect = document.getElementById('time');
    const classSelect = document.getElementById('class');
    const subjectSelect = document.getElementById('subject');
    const bookSelect = document.getElementById('book');
    const chapterSelect = document.getElementById('chapter');
    const materialsSection = document.getElementById('materials-section');
    const previewSection = document.getElementById('preview-section');
    const saveBtn = document.getElementById('save-btn');

    // Subject and book data
    const subjectBooks = {
        'คณิตศาสตร์': [
            'คณิตศาสตร์ ป.5',
            'คณิตศาสตร์ ป.6',
            'คณิตศาสตร์ ม.1',
            'คณิตศาสตร์ ม.2',
            'คณิตศาสตร์ ม.3'
        ],
        'วิทยาศาสตร์': [
            'วิทยาศาสตร์ ป.5',
            'วิทยาศาสตร์ ป.6',
            'วิทยาศาสตร์ ม.1',
            'วิทยาศาสตร์ ม.2',
            'วิทยาศาสตร์ ม.3'
        ],
        'ภาษาไทย': [
            'ภาษาไทย ป.5',
            'ภาษาไทย ป.6',
            'ภาษาไทย ม.1',
            'ภาษาไทย ม.2',
            'ภาษาไทย ม.3'
        ],
        'ภาษาอังกฤษ': [
            'ภาษาอังกฤษ ป.5',
            'ภาษาอังกฤษ ป.6',
            'ภาษาอังกฤษ ม.1',
            'ภาษาอังกฤษ ม.2',
            'ภาษาอังกฤษ ม.3'
        ],
        'สังคมศึกษา': [
            'สังคมศึกษา ป.5',
            'สังคมศึกษา ป.6',
            'สังคมศึกษา ม.1',
            'สังคมศึกษา ม.2',
            'สังคมศึกษา ม.3'
        ]
    };

    const bookChapters = {
        'คณิตศาสตร์ ป.5': [
            'บทที่ 1 - จำนวนและการดำเนินการ',
            'บทที่ 2 - การวัด',
            'บทที่ 3 - เรขาคณิต',
            'บทที่ 4 - พีชคณิต',
            'บทที่ 5 - การวิเคราะห์ข้อมูลและความน่าจะเป็น'
        ],
        'วิทยาศาสตร์ ป.5': [
            'บทที่ 1 - สิ่งมีชีวิตและสิ่งไม่มีชีวิต',
            'บทที่ 2 - แรงและการเคลื่อนที่',
            'บทที่ 3 - แสงและเสียง',
            'บทที่ 4 - สารและสมบัติ'
        ],
        'ภาษาไทย ป.5': [
            'บทที่ 1 - การอ่านและการเขียน',
            'บทที่ 2 - วรรณคดีไทย',
            'บทที่ 3 - ไวยากรณ์',
            'บทที่ 4 - การเขียนเรียงความ'
        ]
    };

    // Subject selection change handler
    if (subjectSelect && bookSelect) {
        subjectSelect.addEventListener('change', function() {
            const selectedSubject = this.value;
            
            // Clear book options
            bookSelect.innerHTML = '<option value="" disabled selected>เลือกหนังสือเรียน</option>';
            
            if (selectedSubject && subjectBooks[selectedSubject]) {
                subjectBooks[selectedSubject].forEach(book => {
                    const option = document.createElement('option');
                    option.value = book;
                    option.textContent = book;
                    bookSelect.appendChild(option);
                });
            }
            
            // Clear chapter and hide materials
            if (chapterSelect) {
                chapterSelect.innerHTML = '<option value="" disabled selected>เลือกบทเรียน</option>';
            }
            if (materialsSection) {
                materialsSection.classList.add('hidden');
            }
            
            updatePreview();
        });
    }

    // Book selection change handler
    if (bookSelect && chapterSelect) {
        bookSelect.addEventListener('change', function() {
            const selectedBook = this.value;
            
            // Clear chapter options
            chapterSelect.innerHTML = '<option value="" disabled selected>เลือกบทเรียน</option>';
            
            if (selectedBook && bookChapters[selectedBook]) {
                bookChapters[selectedBook].forEach(chapter => {
                    const option = document.createElement('option');
                    option.value = chapter;
                    option.textContent = chapter;
                    chapterSelect.appendChild(option);
                });
            }
            
            updatePreview();
        });
    }

    // Chapter selection change handler
    if (chapterSelect && materialsSection) {
        chapterSelect.addEventListener('change', function() {
            if (this.value) {
                materialsSection.classList.remove('hidden');
                materialsSection.classList.add('slide-in');
            }
            updatePreview();
        });
    }

    // Tab switching functionality
    window.switchTab = function(tab) {
        const defaultTab = document.getElementById('tab-default');
        const customTab = document.getElementById('tab-custom');
        const defaultContent = document.getElementById('content-default');
        const customContent = document.getElementById('content-custom');
        
        if (tab === 'default') {
            defaultTab.classList.add('tab-active');
            customTab.classList.remove('tab-active');
            defaultContent.classList.remove('hidden');
            customContent.classList.add('hidden');
        } else {
            defaultTab.classList.remove('tab-active');
            customTab.classList.add('tab-active');
            defaultContent.classList.add('hidden');
            customContent.classList.remove('hidden');
        }
        
        updatePreview();
    };

    // Media item selection
    const mediaItems = document.querySelectorAll('.media-item');
    mediaItems.forEach(item => {
        item.addEventListener('click', function() {
            this.classList.toggle('selected');
            updatePreview();
        });
    });

    // Material download buttons
    const downloadButtons = document.querySelectorAll('[class*="ดาวน์โหลด"], [class*="ดูตัวอย่าง"]');
    downloadButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            const materialType = this.closest('.material-card')?.querySelector('span')?.textContent || 'สื่อการสอน';
            showNotification(`กำลังดาวน์โหลด ${materialType}...`, 'info');
            
            // Simulate download
            setTimeout(() => {
                showNotification(`ดาวน์โหลด ${materialType} เรียบร้อยแล้ว`, 'success');
            }, 2000);
        });
    });

    // Add homework button
    const addHomeworkBtn = document.getElementById('add-homework-btn');
    const homeworkModal = document.getElementById('homework-modal');
    const closeModalBtn = document.getElementById('close-modal-btn');
    const addHomeworkConfirmBtn = document.getElementById('add-homework-confirm-btn');

    if (addHomeworkBtn && homeworkModal) {
        addHomeworkBtn.addEventListener('click', function() {
            homeworkModal.classList.remove('hidden');
        });
    }

    if (closeModalBtn && homeworkModal) {
        closeModalBtn.addEventListener('click', function() {
            homeworkModal.classList.add('hidden');
        });
    }

    if (addHomeworkConfirmBtn && homeworkModal) {
        addHomeworkConfirmBtn.addEventListener('click', function() {
            const title = document.getElementById('homework-title')?.value;
            const desc = document.getElementById('homework-desc')?.value;
            const due = document.getElementById('homework-due')?.value;
            
            if (title && desc && due) {
                showNotification('เพิ่มการบ้านเรียบร้อยแล้ว', 'success');
                homeworkModal.classList.add('hidden');
                
                // Clear form
                document.getElementById('homework-title').value = '';
                document.getElementById('homework-desc').value = '';
                document.getElementById('homework-due').value = '';
            } else {
                showNotification('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
            }
        });
    }

    // Close modal when clicking outside
    if (homeworkModal) {
        homeworkModal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    }

    // Save button functionality
    if (saveBtn) {
        saveBtn.addEventListener('click', function() {
            if (validateForm()) {
                // Show loading state
                const originalText = this.innerHTML;
                this.innerHTML = '<div class="spinner w-5 h-5 mr-2"></div>กำลังบันทึก...';
                this.disabled = true;
                
                // Simulate save
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                    
                    // Show success modal
                    const successModal = document.getElementById('success-modal');
                    if (successModal) {
                        successModal.classList.remove('hidden');
                    }
                }, 2000);
            }
        });
    }

    // Success modal close button
    const successCloseBtn = document.getElementById('success-close-btn');
    const successModal = document.getElementById('success-modal');
    
    if (successCloseBtn && successModal) {
        successCloseBtn.addEventListener('click', function() {
            successModal.classList.add('hidden');
            
            // Reset form
            resetForm();
        });
    }

    // Form validation
    function validateForm() {
        const requiredFields = [dateInput, timeSelect, classSelect, subjectSelect];
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (field && !field.value) {
                isValid = false;
                field.classList.add('input-error');
                field.classList.add('error-shake');
                
                setTimeout(() => {
                    field.classList.remove('error-shake');
                }, 500);
            } else if (field) {
                field.classList.remove('input-error');
                field.classList.add('input-success');
            }
        });
        
        if (!isValid) {
            showNotification('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
        }
        
        return isValid;
    }

    // Reset form
    function resetForm() {
        const form = document.querySelector('form') || document.querySelector('.form-section');
        if (form) {
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.value = '';
                input.classList.remove('input-error', 'input-success');
            });
        }
        
        // Hide sections
        if (materialsSection) {
            materialsSection.classList.add('hidden');
        }
        if (previewSection) {
            previewSection.classList.add('hidden');
        }
        
        // Reset media selections
        mediaItems.forEach(item => {
            item.classList.remove('selected');
        });
    }

    // Update preview
    function updatePreview() {
        if (!previewSection) return;
        
        const date = dateInput?.value;
        const time = timeSelect?.value;
        const className = classSelect?.value;
        const subject = subjectSelect?.value;
        
        if (date && time && className && subject) {
            // Update preview content
            const previewDate = document.getElementById('preview-date');
            const previewTime = document.getElementById('preview-time');
            const previewClass = document.getElementById('preview-class');
            const previewSubject = document.getElementById('preview-subject');
            
            if (previewDate) previewDate.textContent = formatDate(date);
            if (previewTime) previewTime.textContent = time;
            if (previewClass) previewClass.textContent = className;
            if (previewSubject) previewSubject.textContent = subject;
            
            // Update materials
            updatePreviewMaterials();
            
            // Show preview
            previewSection.classList.remove('hidden');
            previewSection.classList.add('fade-in');
        } else {
            previewSection.classList.add('hidden');
        }
    }

    // Update preview materials
    function updatePreviewMaterials() {
        const previewMaterials = document.getElementById('preview-materials');
        if (!previewMaterials) return;
        
        const selectedMaterials = [];
        
        // Check default materials
        const materialsVisible = materialsSection && !materialsSection.classList.contains('hidden');
        if (materialsVisible) {
            selectedMaterials.push('แผนการสอน', 'ใบงาน', 'PowerPoint', 'วิดีโอประกอบการสอน');
        }
        
        // Check custom materials
        const selectedCustomMaterials = document.querySelectorAll('.media-item.selected');
        selectedCustomMaterials.forEach(item => {
            const materialName = item.querySelector('p')?.textContent;
            if (materialName) {
                selectedMaterials.push(materialName);
            }
        });
        
        // Update preview
        previewMaterials.innerHTML = selectedMaterials.map(material => 
            `<span class="bg-purple-50 text-purple-600 px-3 py-1 rounded-full text-xs">${material}</span>`
        ).join('');
    }

    // Format date for display
    function formatDate(dateString) {
        const date = new Date(dateString);
        const months = [
            'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
            'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
        ];
        
        return `${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear() + 543}`;
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        notification.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }

    // Add event listeners for form inputs to update preview
    [dateInput, timeSelect, classSelect, subjectSelect].forEach(input => {
        if (input) {
            input.addEventListener('change', updatePreview);
        }
    });

    // Add fade-in animation to main elements
    const formSection = document.querySelector('.form-section');
    if (formSection) {
        formSection.classList.add('fade-in');
    }
});
