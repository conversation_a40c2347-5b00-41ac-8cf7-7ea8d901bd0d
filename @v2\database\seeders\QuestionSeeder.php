<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Exam\Question;
use App\Models\Exam\Choice;
use App\Models\Exam\Exam;
use App\Models\Subject;
use App\Models\Team;
use App\Models\User;

class QuestionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $teams = Team::all();

        foreach ($teams as $team) {
            // Get exams for this team
            $exams = Exam::where('team_id', $team->id)->get();

            // Get subjects for this team
            $subjects = Subject::where('team_id', $team->id)->get();

            // Get teachers for this team
            $teachers = User::where('team_id', $team->id)
                ->whereHas('roles', function ($query) {
                    $query->where('name', 'teacher');
                })
                ->get();

            if ($exams->isEmpty() || $subjects->isEmpty() || $teachers->isEmpty()) {
                continue;
            }

            foreach ($exams as $exam) {
                // Create 5-10 questions per exam
                $questionCount = fake()->numberBetween(5, 10);

                for ($i = 1; $i <= $questionCount; $i++) {
                    $questionType = collect(['multiple_choice', 'short_answer', 'essay'])->random();
                    $difficulty = collect(['easy', 'medium', 'hard'])->random();

                    $question = Question::create([
                        'team_id' => $team->id,
                        'exam_id' => $exam->id,
                        'user_id' => $teachers->random()->id,
                        'subject_id' => $subjects->random()->id,
                        'question_text' => $this->getQuestionText($exam->title, $i, $questionType),
                        'question_type' => $questionType,
                        'points' => $this->getPoints($difficulty),
                        'explanation' => $this->getExplanation($questionType),
                        'difficulty_level' => $difficulty,
                        'tags' => $this->getTags($exam->title, $difficulty),
                        'sort_order' => $i,
                        'is_active' => true,
                    ]);

                    // Create choices for multiple choice questions
                    if ($questionType === 'multiple_choice') {
                        $this->createChoicesForQuestion($question);
                    }
                }
            }
        }
    }

    private function getQuestionText(string $examTitle, int $questionNumber, string $type): string
    {
        $questions = [
            'multiple_choice' => [
                "What is the primary concept discussed in {$examTitle}?",
                "Which of the following best describes the main principle of {$examTitle}?",
                "In the context of {$examTitle}, what is the most important factor?",
                "According to {$examTitle}, which statement is correct?",
                "What is the key characteristic of the topic covered in {$examTitle}?",
            ],
            'short_answer' => [
                "Briefly explain the main concept of {$examTitle}.",
                "Define the key terms related to {$examTitle}.",
                "Describe the process involved in {$examTitle}.",
                "What are the main components of {$examTitle}?",
                "Explain how {$examTitle} applies in real-world scenarios.",
            ],
            'essay' => [
                "Discuss in detail the importance of {$examTitle} in modern education.",
                "Analyze the various aspects of {$examTitle} and their implications.",
                "Compare and contrast different approaches to {$examTitle}.",
                "Evaluate the effectiveness of {$examTitle} in achieving learning objectives.",
                "Critically examine the role of {$examTitle} in student development.",
            ],
        ];

        $questionPool = $questions[$type] ?? $questions['multiple_choice'];
        return "Question {$questionNumber}: " . collect($questionPool)->random();
    }

    private function getPoints(string $difficulty): float
    {
        return match ($difficulty) {
            'easy' => fake()->numberBetween(1, 2),
            'medium' => fake()->numberBetween(2, 4),
            'hard' => fake()->numberBetween(4, 6),
            default => 2,
        };
    }

    private function getExplanation(string $type): string
    {
        $explanations = [
            'multiple_choice' => 'This question tests your understanding of the fundamental concepts. The correct answer demonstrates knowledge of key principles.',
            'short_answer' => 'This question requires you to demonstrate your understanding by providing a concise explanation of the concept.',
            'essay' => 'This question evaluates your ability to analyze, synthesize, and present comprehensive arguments about the topic.',
        ];

        return $explanations[$type] ?? $explanations['multiple_choice'];
    }

    private function getTags(string $examTitle, string $difficulty): array
    {
        $baseTags = ['education', 'learning', $difficulty];
        $examTags = explode(' ', strtolower($examTitle));

        return array_merge($baseTags, array_slice($examTags, 0, 2));
    }

    private function createChoicesForQuestion(Question $question): void
    {
        $choiceTexts = [
            'Option A: This is the first possible answer to the question.',
            'Option B: This is the second possible answer to the question.',
            'Option C: This is the third possible answer to the question.',
            'Option D: This is the fourth possible answer to the question.',
        ];

        // Randomly select which choice is correct (0-3)
        $correctChoiceIndex = fake()->numberBetween(0, 3);

        foreach ($choiceTexts as $index => $choiceText) {
            Choice::create([
                'question_id' => $question->id,
                'choice_text' => $choiceText,
                'is_correct' => $index === $correctChoiceIndex,
                'sort_order' => $index + 1,
            ]);
        }
    }
}
