// Teacher Dashboard JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle (inherited from layout)
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeMenu = document.getElementById('close-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('active');
        });
    }

    if (closeMenu && mobileMenu) {
        closeMenu.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
        });
    }

    // Mobile tab dropdown functionality
    const mobileTabToggle = document.getElementById('mobile-tab-toggle');
    const mobileTabMenu = document.getElementById('mobile-tab-menu');

    if (mobileTabToggle && mobileTabMenu) {
        mobileTabToggle.addEventListener('click', function() {
            mobileTabMenu.classList.toggle('hidden');
            const chevron = this.querySelector('.fa-chevron-down, .fa-chevron-up');
            if (chevron) {
                chevron.classList.toggle('fa-chevron-down');
                chevron.classList.toggle('fa-chevron-up');
            }
        });
    }

    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            const tabText = this.innerHTML;

            // Remove active class from all tabs and contents
            tabButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-blue-100', 'text-blue-600');
                btn.classList.add('text-gray-600', 'hover:bg-gray-100');
            });
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Add active class to clicked tab
            this.classList.add('active', 'bg-blue-100', 'text-blue-600');
            this.classList.remove('text-gray-600', 'hover:bg-gray-100');

            // Update mobile toggle text
            if (mobileTabToggle) {
                const toggleText = mobileTabToggle.querySelector('span');
                if (toggleText) {
                    toggleText.innerHTML = tabText;
                }
                // Hide mobile menu after selection
                if (mobileTabMenu) {
                    mobileTabMenu.classList.add('hidden');
                    const chevron = mobileTabToggle.querySelector('.fa-chevron-up, .fa-chevron-down');
                    if (chevron) {
                        chevron.classList.remove('fa-chevron-up');
                        chevron.classList.add('fa-chevron-down');
                    }
                }
            }

            // Show target content
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });

    // Course card click handlers
    const courseCards = document.querySelectorAll('.course-card');
    courseCards.forEach(card => {
        card.addEventListener('click', function() {
            const courseName = this.querySelector('h3').textContent;
            showCourseDetails(courseName);
        });
    });

    function showCourseDetails(courseName) {
        // Create modal for course details
        const modal = document.createElement('div');
        modal.className = 'modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.style.display = 'flex';
        
        modal.innerHTML = `
            <div class="modal-content bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">${courseName}</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">จำนวนนักเรียน</h4>
                            <p class="text-2xl font-bold text-blue-600">32 คน</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">ความก้าวหน้า</h4>
                            <p class="text-2xl font-bold text-green-600">75%</p>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">บทเรียนล่าสุด</h4>
                        <ul class="space-y-2">
                            <li class="flex justify-between items-center">
                                <span>บทที่ 8: สมการเชิงเส้น</span>
                                <span class="text-green-600">เสร็จแล้ว</span>
                            </li>
                            <li class="flex justify-between items-center">
                                <span>บทที่ 9: ระบบสมการ</span>
                                <span class="text-yellow-600">กำลังสอน</span>
                            </li>
                            <li class="flex justify-between items-center">
                                <span>บทที่ 10: อสมการ</span>
                                <span class="text-gray-400">ยังไม่เริ่ม</span>
                            </li>
                        </ul>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">ดูรายละเอียด</button>
                        <button class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">เพิ่มบทเรียน</button>
                        <button class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">สร้างแบบทดสอบ</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal functionality
        const closeButton = modal.querySelector('.close-modal');
        closeButton.addEventListener('click', function() {
            document.body.removeChild(modal);
        });
        
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // Quick access button handlers
    const quickAccessButtons = document.querySelectorAll('.quick-access-btn');
    quickAccessButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.textContent.trim();
            showNotification(`${action} - ฟีเจอร์นี้จะเปิดใช้งานเร็วๆ นี้`, 'info');
        });
    });

    // Add homework button handlers
    const addHomeworkButtons = document.querySelectorAll('.add-homework-btn');
    addHomeworkButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            showHomeworkModal();
        });
    });

    function showHomeworkModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.style.display = 'flex';
        
        modal.innerHTML = `
            <div class="modal-content bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">เพิ่มการบ้าน</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">หัวข้อการบ้าน</label>
                        <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="เช่น แบบฝึกหัดบทที่ 5">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">รายละเอียด</label>
                        <textarea class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" rows="3" placeholder="รายละเอียดการบ้าน..."></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">กำหนดส่ง</label>
                        <input type="date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    </div>
                    <div class="flex space-x-2">
                        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex-1">เพิ่มการบ้าน</button>
                        <button type="button" class="close-modal bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400">ยกเลิก</button>
                    </div>
                </form>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal functionality
        const closeButtons = modal.querySelectorAll('.close-modal');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.body.removeChild(modal);
            });
        });
        
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });

        // Form submission
        const form = modal.querySelector('form');
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            showNotification('เพิ่มการบ้านเรียบร้อยแล้ว', 'success');
            document.body.removeChild(modal);
        });
    }

    // Calendar navigation
    const calendarPrevButton = document.querySelector('.calendar-prev');
    const calendarNextButton = document.querySelector('.calendar-next');
    
    if (calendarPrevButton) {
        calendarPrevButton.addEventListener('click', function() {
            showNotification('เปลี่ยนเดือนก่อนหน้า', 'info');
        });
    }
    
    if (calendarNextButton) {
        calendarNextButton.addEventListener('click', function() {
            showNotification('เปลี่ยนเดือนถัดไป', 'info');
        });
    }

    // Calendar event click handlers
    const calendarEvents = document.querySelectorAll('.calendar-event');
    calendarEvents.forEach(event => {
        event.addEventListener('click', function(e) {
            e.stopPropagation();
            const eventText = this.textContent;
            showNotification(`คลิกที่กิจกรรม: ${eventText}`, 'info');
        });
    });

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
        
        switch (type) {
            case 'success':
                notification.classList.add('bg-green-500', 'text-white');
                break;
            case 'error':
                notification.classList.add('bg-red-500', 'text-white');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-500', 'text-white');
                break;
            default:
                notification.classList.add('bg-blue-500', 'text-white');
        }
        
        notification.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // Animate progress circles on load
    const progressCircles = document.querySelectorAll('.circle-progress-value');
    progressCircles.forEach(circle => {
        const strokeDasharray = circle.getAttribute('stroke-dasharray');
        if (strokeDasharray) {
            const [progress, total] = strokeDasharray.split(' ').map(Number);
            const percentage = (progress / total) * 100;
            
            // Animate the circle
            setTimeout(() => {
                circle.style.strokeDasharray = `${progress} ${total}`;
            }, 500);
        }
    });

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.bg-white');
    cards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Form validation and submission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic validation
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500');
                } else {
                    field.classList.remove('border-red-500');
                }
            });
            
            if (isValid) {
                showNotification('บันทึกข้อมูลเรียบร้อยแล้ว', 'success');
            } else {
                showNotification('กรุณากรอกข้อมูลให้ครบถ้วน', 'error');
            }
        });
    });

    // Search functionality
    const searchInputs = document.querySelectorAll('input[type="search"]');
    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            // Implement search logic here
            console.log('Searching for:', searchTerm);
        });
    });
});
