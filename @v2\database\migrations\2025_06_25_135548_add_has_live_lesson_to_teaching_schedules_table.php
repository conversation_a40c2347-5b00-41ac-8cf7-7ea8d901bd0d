<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('teaching_schedules', function (Blueprint $table) {
            $table->boolean('has_live_lesson')->default(false)->after('recurring_pattern');
            $table->foreignId('live_video_id')->nullable()->constrained('live_videos')->nullOnDelete()->after('has_live_lesson');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('teaching_schedules', function (Blueprint $table) {
            $table->dropForeign(['live_video_id']);
            $table->dropColumn(['has_live_lesson', 'live_video_id']);
        });
    }
};
