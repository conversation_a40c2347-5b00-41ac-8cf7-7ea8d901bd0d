document.addEventListener("DOMContentLoaded",function(){p(),g(),L(),v();function p(){const s=document.getElementById("registerForm");s.querySelectorAll(".form-input").forEach(o=>{o.addEventListener("blur",c),o.addEventListener("input",l)});function c(o){const t=o.target,e=t.value.trim(),a=t.name;switch(i(t),a){case"role":e?d(t):n(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E1B\u0E23\u0E30\u0E40\u0E20\u0E17\u0E1C\u0E39\u0E49\u0E43\u0E0A\u0E49");break;case"name":e?e.length<2?n(t,"\u0E0A\u0E37\u0E48\u0E2D\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E2D\u0E22\u0E48\u0E32\u0E07\u0E19\u0E49\u0E2D\u0E22 2 \u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23"):d(t):n(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E0A\u0E37\u0E48\u0E2D-\u0E19\u0E32\u0E21\u0E2A\u0E01\u0E38\u0E25");break;case"email":e?u(e)?d(t):n(t,"\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E2D\u0E35\u0E40\u0E21\u0E25\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"):n(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2D\u0E35\u0E40\u0E21\u0E25");break;case"password":e?e.length<8?n(t,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E2D\u0E22\u0E48\u0E32\u0E07\u0E19\u0E49\u0E2D\u0E22 8 \u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23"):f(e)?d(t):n(t,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23\u0E43\u0E2B\u0E0D\u0E48 \u0E40\u0E25\u0E47\u0E01 \u0E41\u0E25\u0E30\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02"):n(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19");break;case"password_confirmation":const m=s.querySelector('input[name="password"]');e?e!==m.value?n(t,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E44\u0E21\u0E48\u0E15\u0E23\u0E07\u0E01\u0E31\u0E19"):d(t):n(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19");break}}function l(o){const t=o.target;t.classList.contains("error")&&i(t)}function n(o,t){o.classList.add("error"),o.classList.remove("success");const e=document.createElement("div");e.className="error-message",e.innerHTML=`
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${t}
            `,o.parentNode.appendChild(e)}function d(o){o.classList.add("success"),o.classList.remove("error")}function i(o){o.classList.remove("error","success");const t=o.parentNode.querySelector(".error-message");t&&t.remove()}function u(o){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o)}function f(o){const t=/[A-Z]/.test(o),e=/[a-z]/.test(o),a=/\d/.test(o);return t&&e&&a}}function g(){const s=document.querySelectorAll('.social-button:not([id="phoneLogin"])');console.log("Found social buttons:",s.length),s.forEach(c=>{console.log("Button ID:",c.id)}),s.forEach(c=>{const l=c.id.replace("Login","");console.log("Adding event listener for provider:",l),c.addEventListener("click",()=>r(l,c))});function r(c,l){console.log("Social login clicked for provider:",c),l.textContent,h(l,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E40\u0E0A\u0E37\u0E48\u0E2D\u0E21\u0E15\u0E48\u0E2D...");const n=`/auth/${c}`;console.log("Redirecting to:",n),window.location.href=n}}function L(){const s=document.getElementById("phoneLogin"),r=document.getElementById("phoneModal"),c=document.getElementById("closePhoneModal"),l=document.getElementById("sendOtp"),n=document.getElementById("verifyOtp"),d=document.getElementById("phoneNumber"),i=document.querySelectorAll(".otp-input");s&&s.addEventListener("click",function(){r.classList.remove("hidden"),d.focus()}),c&&c.addEventListener("click",function(){r.classList.add("hidden"),o()}),r&&r.addEventListener("click",function(e){e.target===this&&(this.classList.add("hidden"),o())}),l&&l.addEventListener("click",u),n&&n.addEventListener("click",f),i.forEach((e,a)=>{e.addEventListener("input",function(m){m.target.value.length===1&&a<i.length-1&&i[a+1].focus()}),e.addEventListener("keydown",function(m){m.key==="Backspace"&&m.target.value===""&&a>0&&i[a-1].focus()})});function u(){const e=d.value.trim(),a=document.getElementById("countryCode").value;if(!e){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C","error");return}if(!t(e)){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error");return}h(l,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E48\u0E07 OTP..."),setTimeout(()=>{document.getElementById("phoneStep").classList.add("hidden"),document.getElementById("otpStep").classList.remove("hidden"),w(l,"\u0E2A\u0E48\u0E07 OTP"),window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2A\u0E48\u0E07 OTP \u0E44\u0E1B\u0E22\u0E31\u0E07 "+a+e+" \u0E41\u0E25\u0E49\u0E27","success"),i[0].focus()},2e3)}function f(){const e=Array.from(i).map(a=>a.value).join("");if(e.length!==6){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01 OTP \u0E43\u0E2B\u0E49\u0E04\u0E23\u0E1A 6 \u0E2B\u0E25\u0E31\u0E01","error");return}h(n,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E15\u0E23\u0E27\u0E08\u0E2A\u0E2D\u0E1A..."),setTimeout(()=>{e==="123456"?(window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19\u0E15\u0E31\u0E27\u0E15\u0E19\u0E2A\u0E33\u0E40\u0E23\u0E47\u0E08!","success"),r.classList.add("hidden"),o()):(window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E23\u0E2B\u0E31\u0E2A OTP \u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error"),i.forEach(a=>a.value=""),i[0].focus()),w(n,"\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19 OTP")},2e3)}function o(){document.getElementById("phoneStep").classList.remove("hidden"),document.getElementById("otpStep").classList.add("hidden"),d.value="",i.forEach(e=>e.value="")}function t(e){return/^[0-9]{9,10}$/.test(e)}}function v(){const s=document.getElementById("registerForm"),r=document.getElementById("submitBtn");s&&s.addEventListener("submit",function(n){n.preventDefault(),c()&&l()});function c(){const n=s.querySelectorAll(".form-input[required]");let d=!0;return n.forEach(i=>{const u=new Event("blur");i.dispatchEvent(u),(i.classList.contains("error")||!i.value.trim())&&(d=!1)}),d}async function l(){h(r,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E1A\u0E31\u0E0D\u0E0A\u0E35...");try{const n=new FormData(s),i=await(await fetch("/api/register",{method:"POST",headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content"),Accept:"application/json"},body:n})).json();i.success?(window.LayoutUtils&&window.LayoutUtils.showNotification(i.message,"success"),setTimeout(()=>{window.location.href=i.redirect},1500)):(window.LayoutUtils&&window.LayoutUtils.showNotification(i.message||"\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14","error"),i.errors&&Object.keys(i.errors).forEach(u=>{const f=s.querySelector(`[name="${u}"]`);f&&showFieldError(f,i.errors[u][0])}))}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E1A\u0E31\u0E0D\u0E0A\u0E35","error")}w(r,"\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E1A\u0E31\u0E0D\u0E0A\u0E35")}}function h(s,r){s.disabled=!0,s.innerHTML=`
            <div class="loading">
                <div class="loading-spinner"></div>
                ${r}
            </div>
        `}function w(s,r){s.disabled=!1,s.innerHTML=r}});
