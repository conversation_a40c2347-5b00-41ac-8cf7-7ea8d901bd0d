var Qt=Object.defineProperty,Ct=e=>{throw TypeError(e)},es=(e,t,s)=>t in e?Qt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,a=(e,t,s)=>es(e,typeof t!="symbol"?t+"":t,s),Ee=(e,t,s)=>t.has(e)||Ct("Cannot "+s),l=(e,t,s)=>(Ee(e,t,"read from private field"),s?s.call(e):t.get(e)),k=(e,t,s)=>t.has(e)?Ct("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,s),E=(e,t,s,i)=>(Ee(e,t,"write to private field"),i?i.call(e,s):t.set(e,s),s),o=(e,t,s)=>(Ee(e,t,"access private method"),s),Se={arrowUp:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 15l-6-6-6 6"/></svg>',arrowDown:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>',arrowRight:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>',attention:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>',clear:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="15" y1="9" x2="9" y2="15"></line><line x1="9" y1="9" x2="15" y2="15"></line></svg>',cross:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>',check:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>',partialCheck:'<svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 25 25" fill="none" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>'},A=(e,t)=>{if(t.innerHTML="",typeof e=="string")t.innerHTML=e;else{let s=e.cloneNode(!0);t.appendChild(s)}},Te=e=>{let t=e?{...e}:{};return Object.keys(Se).forEach(s=>{t[s]||(t[s]=Se[s])}),t},ts=e=>e.reduce((t,{name:s},i)=>(t+=s,i<e.length-1&&(t+=", "),t),""),B,y,$,T,u,wt,q,ye,xe,z,G,Lt,Me,St,Ne,_e,He,Ae,Oe,Tt,Ie,Be,yt,Ve,Pe,Ge,We,re,de=class{constructor({value:t,showTags:s,tagsCountText:i,clearable:n,isAlwaysOpened:c,searchable:r,placeholder:v,disabled:g,isSingleSelect:w,id:C,ariaLabel:b,iconElements:f,inputCallback:L,searchCallback:x,openCallback:M,closeCallback:O,keydownCallback:V,focusCallback:I,blurCallback:N,nameChangeCallback:F}){k(this,u),a(this,"value"),a(this,"showTags"),a(this,"tagsCountText"),a(this,"clearable"),a(this,"isAlwaysOpened"),a(this,"searchable"),a(this,"placeholder"),a(this,"disabled"),a(this,"isSingleSelect"),a(this,"id"),a(this,"ariaLabel"),a(this,"iconElements"),a(this,"isOpened"),a(this,"searchText"),a(this,"srcElement"),k(this,B),k(this,y),k(this,$),k(this,T),a(this,"inputCallback"),a(this,"searchCallback"),a(this,"openCallback"),a(this,"closeCallback"),a(this,"keydownCallback"),a(this,"focusCallback"),a(this,"blurCallback"),a(this,"nameChangeCallback"),this.value=t,this.showTags=s,this.tagsCountText=i,this.searchable=r,this.placeholder=v,this.clearable=n,this.isAlwaysOpened=c,this.disabled=g,this.isSingleSelect=w,this.id=C,this.ariaLabel=b,this.iconElements=f,this.isOpened=!1,this.searchText="",E(this,B,o(this,u,St).call(this)),E(this,y,o(this,u,Tt).call(this)),E(this,$,o(this,u,yt).call(this)),E(this,T,null),this.inputCallback=L,this.searchCallback=x,this.openCallback=M,this.closeCallback=O,this.keydownCallback=V,this.focusCallback=I,this.blurCallback=N,this.nameChangeCallback=F,this.srcElement=o(this,u,Lt).call(this,{htmlTagsSection:l(this,B),htmlEditControl:l(this,y),htmlOperators:l(this,$)}),o(this,u,wt).call(this)}focus(){setTimeout(()=>l(this,y).focus(),0)}blur(){this.isOpened&&o(this,u,G).call(this),this.clearSearch(),l(this,y).blur()}updateValue(t){this.value=t,o(this,u,q).call(this),o(this,u,z).call(this)}removeItem(t){this.value=this.value.filter(s=>s.id!==t),o(this,u,re).call(this),o(this,u,q).call(this),o(this,u,z).call(this)}clear(){this.value=[],o(this,u,re).call(this),o(this,u,q).call(this),this.clearSearch()}openClose(){o(this,u,G).call(this)}clearSearch(){this.searchText="",this.searchCallback(""),o(this,u,z).call(this)}};B=new WeakMap,y=new WeakMap,$=new WeakMap,T=new WeakMap,u=new WeakSet,wt=function(){o(this,u,q).call(this),o(this,u,z).call(this),o(this,u,ye).call(this)},q=function(){if(l(this,B).innerHTML="",this.showTags){l(this,B).append(...o(this,u,Ne).call(this));let e=ts(this.value);this.nameChangeCallback(e)}else{let e=o(this,u,Oe).call(this);l(this,B).appendChild(e),this.nameChangeCallback(e.innerText)}l(this,B).appendChild(l(this,y))},ye=function(){let e=[];l(this,$).innerHTML="",this.clearable&&e.push(o(this,u,Ve).call(this)),this.isAlwaysOpened||e.push(o(this,u,Ge).call(this,this.isOpened)),e.length&&l(this,$).append(...e)},xe=function(){if(!this.isAlwaysOpened&&l(this,T)){let e=this.isOpened?this.iconElements.arrowUp:this.iconElements.arrowDown;A(e,l(this,T))}},z=function(){var e;(e=this.value)!=null&&e.length?(l(this,y).removeAttribute("placeholder"),this.srcElement.classList.remove("treeselect-input--value-not-selected")):(l(this,y).setAttribute("placeholder",this.placeholder),this.srcElement.classList.add("treeselect-input--value-not-selected")),this.searchable?this.srcElement.classList.remove("treeselect-input--unsearchable"):this.srcElement.classList.add("treeselect-input--unsearchable"),this.isSingleSelect?this.srcElement.classList.add("treeselect-input--is-single-select"):this.srcElement.classList.remove("treeselect-input--is-single-select"),l(this,y).value=this.searchText},G=function(){this.isOpened=!this.isOpened,o(this,u,xe).call(this),this.isOpened?this.openCallback():this.closeCallback()},Lt=function({htmlTagsSection:e,htmlEditControl:t,htmlOperators:s}){let i=document.createElement("div");return i.classList.add("treeselect-input"),i.setAttribute("tabindex","-1"),i.addEventListener("mousedown",n=>o(this,u,Me).call(this,n)),i.addEventListener("focus",()=>this.focusCallback(),!0),i.addEventListener("blur",()=>this.blurCallback(),!0),e.appendChild(t),i.append(e,s),i},Me=function(e){e.stopPropagation(),this.isOpened||o(this,u,G).call(this),this.focus()},St=function(){let e=document.createElement("div");return e.classList.add("treeselect-input__tags"),e},Ne=function(){return this.value.map(e=>{let t=document.createElement("div");t.classList.add("treeselect-input__tags-element"),t.setAttribute("tabindex","-1"),t.setAttribute("tag-id",e.id.toString()),t.setAttribute("title",e.name);let s=o(this,u,He).call(this,e.name),i=o(this,u,Ae).call(this);return t.addEventListener("mousedown",n=>o(this,u,_e).call(this,n,e.id)),t.append(s,i),t})},_e=function(e,t){e.preventDefault(),e.stopPropagation(),this.removeItem(t),this.focus()},He=function(e){let t=document.createElement("span");return t.classList.add("treeselect-input__tags-name"),t.textContent=e,t},Ae=function(){let e=document.createElement("span");return e.classList.add("treeselect-input__tags-cross"),A(this.iconElements.cross,e),e},Oe=function(){let e=document.createElement("span");if(e.classList.add("treeselect-input__tags-count"),!this.value.length)return e.textContent="",e.setAttribute("title",""),e;let t=this.value.length===1?this.value[0].name:`${this.value.length} ${this.tagsCountText}`;return e.textContent=t,e.setAttribute("title",t),e},Tt=function(){let e=document.createElement("input");return e.classList.add("treeselect-input__edit"),this.id&&e.setAttribute("id",this.id),(!this.searchable||this.disabled)&&e.setAttribute("readonly","readonly"),this.disabled&&e.setAttribute("tabindex","-1"),this.ariaLabel.length&&e.setAttribute("aria-label",this.ariaLabel),e.addEventListener("keydown",t=>o(this,u,Ie).call(this,t)),e.addEventListener("input",t=>o(this,u,Be).call(this,t,e)),e},Ie=function(e){e.stopPropagation();let t=e.key;t==="Backspace"&&!this.searchText.length&&this.value.length&&!this.showTags&&this.clear(),t==="Backspace"&&!this.searchText.length&&this.value.length&&this.removeItem(this.value[this.value.length-1].id),e.code==="Space"&&(!this.searchText||!this.searchable)&&o(this,u,G).call(this),(t==="Enter"||t==="ArrowDown"||t==="ArrowUp")&&e.preventDefault(),this.keydownCallback(e),t!=="Tab"&&this.focus()},Be=function(e,t){e.stopPropagation();let s=this.searchText,i=t.value.trim();if(s.length===0&&i.length===0){t.value="";return}if(this.searchable){let n=e.target.value;this.searchCallback(n),this.isOpened||o(this,u,G).call(this)}else t.value="";this.searchText=t.value},yt=function(){let e=document.createElement("div");return e.classList.add("treeselect-input__operators"),e},Ve=function(){let e=document.createElement("span");return e.classList.add("treeselect-input__clear"),e.setAttribute("tabindex","-1"),A(this.iconElements.clear,e),e.addEventListener("mousedown",t=>o(this,u,Pe).call(this,t)),e},Pe=function(e){e.preventDefault(),e.stopPropagation(),(this.searchText.length||this.value.length)&&this.clear(),this.focus()},Ge=function(e){E(this,T,document.createElement("span")),l(this,T).classList.add("treeselect-input__arrow");let t=e?this.iconElements.arrowUp:this.iconElements.arrowDown;return A(t,l(this,T)),l(this,T).addEventListener("mousedown",s=>o(this,u,We).call(this,s)),l(this,T)},We=function(e){e.stopPropagation(),e.preventDefault(),this.focus(),o(this,u,G).call(this)},re=function(){this.inputCallback(this.value)};var xt=({newValue:e,optionsTreeMap:t,isSingleSelect:s,isIndependentNodes:i})=>{ns(t);let n=e.map(r=>t.get(r)??null).filter(r=>r!==null&&!r.disabled),[c]=n;if(s&&n.length&&c){c.checked=!0;return}n.forEach(r=>{r.checked=!0;let v=Ce({option:r,optionsTreeMap:t,isIndependentNodes:i});r.checked=v})},Ce=({option:{id:e,checked:t},optionsTreeMap:s,isIndependentNodes:i})=>{let n=s.get(e)??null;if(n===null)return!1;if(i)return n.checked=!n.disabled&&t,n.checked;let c=Mt({checked:t,currentOption:n,optionsTreeMap:s});return Nt({childOption:n,optionsTreeMap:s}),c},Mt=({checked:e,currentOption:t,optionsTreeMap:s})=>{if(!t.isGroup)return t.checked=!t.disabled&&e,t.isPartialChecked=!1,t.checked;let i=K({id:t.id,optionsTreeMap:s});return!e||t.disabled||t.isPartialChecked?(t.checked=!1,t.isPartialChecked=!1,ue({option:t,children:i,optionsTreeMap:s}),t.checked):_t({children:i,optionsTreeMap:s})?Ht(i)?(t.checked=!1,t.isPartialChecked=!1,t.disabled=!0,t.checked):(t.checked=!1,t.isPartialChecked=!0,i.forEach(n=>{Mt({checked:e,currentOption:n,optionsTreeMap:s})}),t.checked):(t.checked=!0,t.isPartialChecked=!1,ue({option:t,children:i,optionsTreeMap:s}),t.checked)},Nt=({childOption:e,optionsTreeMap:t})=>{let s=t.get(e.childOf)??null;s!==null&&(ss({parentOption:s,optionsTreeMap:t}),Nt({childOption:s,optionsTreeMap:t}))},ss=({parentOption:e,optionsTreeMap:t})=>{let s=K({id:e.id,optionsTreeMap:t});if(Ht(s)){e.checked=!1,e.isPartialChecked=!1,e.disabled=!0;return}if(is(s)){e.checked=!0,e.isPartialChecked=!1;return}if(ls(s)){e.checked=!1,e.isPartialChecked=!0;return}e.checked=!1,e.isPartialChecked=!1},ue=({option:{checked:e,disabled:t},children:s,optionsTreeMap:i})=>{s.forEach(n=>{n.disabled=t||n.disabled,n.checked=e&&!n.disabled,n.isPartialChecked=!1;let c=K({id:n.id,optionsTreeMap:i});ue({option:{checked:e,disabled:t},children:c,optionsTreeMap:i})})},_t=({children:e,optionsTreeMap:t})=>e.some(s=>s.disabled)?!0:e.some(s=>{if(!s.isGroup)return!1;let i=K({id:s.id,optionsTreeMap:t});return _t({children:i,optionsTreeMap:t})}),Ht=e=>e.every(t=>!!t.disabled),is=e=>e.every(t=>t.checked),ls=e=>e.some(t=>t.checked||t.isPartialChecked),ns=e=>{e.forEach(t=>{t.checked=!1,t.isPartialChecked=!1})},os=({options:e,openLevel:t,isIndependentNodes:s})=>{let i={level:0,groupId:""},n=new Map;return At({optionsTreeMap:n,options:e,openLevel:t,groupId:i.groupId,level:i.level}),hs({optionsTreeMap:n,isIndependentNodes:s}),n},At=({optionsTreeMap:e,options:t,openLevel:s,groupId:i,level:n})=>{t.forEach(c=>{var r,v;let g=(((r=c.children)==null?void 0:r.length)??0)>0,w=n>=s&&g,C=n>s,b=((v=c.children)==null?void 0:v.map(L=>L.value))??[],f=c.value;e.has(f)&&console.error(`Validation: You have duplicated option value: ${f}! You should use unique values. Duplicates will lead to unexpected behavior.`),e.set(f,{id:f,name:c.name,childOf:i,isGroup:g,checked:!1,isPartialChecked:!1,level:n,isClosed:w,hidden:C,disabled:c.disabled??!1,isGroupSelectable:!g||(c.isGroupSelectable??!0),children:b,checkboxHtmlElement:null,itemHtmlElement:null,arrowItemHtmlElement:null,checkboxIconHtmlElement:null}),g&&At({optionsTreeMap:e,options:c.children,openLevel:s,groupId:f,level:n+1})})},as=(e,t)=>e===null?null:t.get(e)??t.get(parseInt(e))??null,K=({id:e,optionsTreeMap:t})=>{let s=t.get(e)??null;return s===null?[]:s.children.reduce((i,n)=>{let c=t.get(n)??null;return c!==null&&i.push(c),i},[])},cs=e=>{let t=[],s=[],i=[];e.forEach(c=>{c.checked&&(i.push(c),c.isGroup?s.push(c):t.push(c))});let n=i.filter(c=>!s.some(({id:r})=>r===c.childOf));return{ungroupedNodes:t,groupedNodes:n,allNodes:i}},hs=({optionsTreeMap:e,isIndependentNodes:t})=>{let s=[];e.forEach(i=>{i.disabled&&s.push(i)}),s.forEach(({id:i})=>Ce({option:{id:i,checked:!1},optionsTreeMap:e,isIndependentNodes:t}))},X=(e,{id:t,isClosed:s})=>{K({id:t,optionsTreeMap:e}).forEach(i=>{i.hidden=s??!1,i.isGroup&&!i.isClosed&&X(e,{id:i.id,isClosed:s})})},rs=(e,t)=>{if(t){ds(e);return}e.forEach(s=>{s.checked&&we(s.childOf,e),s.isGroup&&!s.disabled&&(s.checked||s.isPartialChecked)&&(s.isClosed=!1,X(e,s))})},ds=e=>{let t=null;for(let[s,i]of e)if(i.checked&&!i.disabled){t=i;break}t&&(t.isGroup&&(t.isClosed=!1,X(e,t)),we(t.childOf,e))},we=(e,t)=>{let s=t.get(e)??null;s&&(s.isClosed=!1,X(t,s),we(s.childOf,t))},us=(e,t)=>{e.forEach(s=>{let i=s.name.toLowerCase().includes(t.toLowerCase());i&&(s.isGroup&&(s.isClosed=!0),s.childOf&&Ot(s.childOf,e)),s.hidden=!i})},Ot=(e,t)=>{let s=t.get(e)??null;s&&(s.hidden=!1,s.isClosed=!1,Ot(s.childOf,t))},ps=({optionsTreeMap:e,beforeSearchStateMap:t})=>{e.forEach(s=>{let i=t.get(s.id);i&&(s.hidden=i.hidden,s.isClosed=i.isClosed)}),t.clear()},ms=({optionsTreeMap:e,beforeSearchStateMap:t})=>{t.clear(),e.forEach(s=>{t.set(s.id,{hidden:s.hidden,isClosed:s.isClosed})})},vs=e=>new IntersectionObserver(t=>{t.forEach(s=>{s.target.classList.toggle("treeselect-list__item--scroll-not-visible",!s.isIntersecting)})},{root:e,threshold:.5}),bs=({optionsTreeMap:e,emptyListHtmlElement:t,iconElements:s,previousSingleSelectedValue:i,rtl:n})=>{e.forEach(c=>{let r=c.checkboxHtmlElement;r&&(r.checked=c.checked),Es({option:c,previousSingleSelectedValue:i}),Cs(c),ws(c),Ls({option:c,iconElements:s}),Ss(c),gs({option:c,optionsTreeMap:e,rtl:n}),Ts({option:c,iconElements:s}),ys(c)}),fs({optionsTreeMap:e,emptyListHtmlElement:t})},gs=({option:e,optionsTreeMap:t,rtl:s})=>{let i=e.level===0,n=20,c=5,r="0";if(i){let g=!1;for(let[C,b]of t)if(b.isGroup&&b.level===e.level){g=!0;break}let w=!e.isGroup&&g?`${n}px`:`${c}px`;r=e.isGroup?"0":w}else r=e.isGroup?`${e.level*n}px`:`${e.level*n+n}px`;let v=e.itemHtmlElement;v&&(s?v.style.paddingRight=r:v.style.paddingLeft=r,v.setAttribute("level",e.level.toString()),v.setAttribute("group",e.isGroup.toString()))},fs=({optionsTreeMap:e,emptyListHtmlElement:t})=>{let s=!1;for(let[i,n]of e)if(!n.hidden){s=!0;break}t?.classList.toggle("treeselect-list__empty--hidden",s)},ks=(e,t)=>{t&&Object.keys(t).forEach(s=>{let i=t[s];typeof i=="string"&&e.setAttribute(s,i)})},Es=({option:e,previousSingleSelectedValue:t})=>{let s=e.itemHtmlElement;s?.classList.toggle("treeselect-list__item--checked",e.checked);let i=Array.isArray(t)&&t[0]===e.id&&!e.disabled;s?.classList.toggle("treeselect-list__item--single-selected",i)},Cs=e=>{let t=e.itemHtmlElement;t?.classList.toggle("treeselect-list__item--partial-checked",e.isPartialChecked)},ws=e=>{let t=e.itemHtmlElement;t?.classList.toggle("treeselect-list__item--disabled",e.disabled)},Ls=({option:e,iconElements:t})=>{let s=e.arrowItemHtmlElement;if(e.isGroup&&s){let i=e.isClosed?t.arrowRight:t.arrowDown;A(i,s);let n=e.itemHtmlElement;n?.classList.toggle("treeselect-list__item--closed",e.isClosed)}},Ss=e=>{let t=e.itemHtmlElement;t?.classList.toggle("treeselect-list__item--hidden",e.hidden)},Ts=({option:e,iconElements:t})=>{let s=e.checkboxIconHtmlElement;s&&(e.checked?A(t.check,s):e.isPartialChecked?A(t.partialCheck,s):s.innerHTML="")},ys=e=>{let t=e.itemHtmlElement;t?.classList.toggle("treeselect-list__item--non-selectable-group",!e.isGroupSelectable)},xs=({newValue:e,optionsTreeMap:t,isSingleSelect:s,expandSelected:i,isFirstValueUpdate:n,isIndependentNodes:c})=>{xt({newValue:e,optionsTreeMap:t,isSingleSelect:s,isIndependentNodes:c}),n&&i&&rs(t,s)},D,W,R,Q,h,Y,It,Bt,Vt,De,Re,$e,le,Fe,je,Ue,ne,qe,ze,Ye,Ke,Xe,Je,Ze,Qe,et,pe,tt,st,oe,J,me,it,ve=class{constructor({options:t,value:s,openLevel:i,listSlotHtmlComponent:n,tagsSortFn:c,emptyText:r,isSingleSelect:v,iconElements:g,showCount:w,disabledBranchNode:C,expandSelected:b,isIndependentNodes:f,rtl:L,listClassName:x,isBoostedRendering:M,inputCallback:O,arrowClickCallback:V,mouseupCallback:I}){k(this,h),a(this,"options"),a(this,"value"),a(this,"openLevel"),a(this,"listSlotHtmlComponent"),a(this,"tagsSortFn"),a(this,"emptyText"),a(this,"isSingleSelect"),a(this,"showCount"),a(this,"disabledBranchNode"),a(this,"expandSelected"),a(this,"isIndependentNodes"),a(this,"rtl"),a(this,"listClassName"),a(this,"isBoostedRendering"),a(this,"iconElements"),a(this,"searchText"),a(this,"intersectionItemsObserver"),a(this,"selectedNodes"),a(this,"optionsTreeMap"),a(this,"beforeSearchStateMap"),a(this,"emptyListHtmlElement"),a(this,"srcElement"),a(this,"inputCallback"),a(this,"arrowClickCallback"),a(this,"mouseupCallback"),k(this,D,null),k(this,W,!0),k(this,R,[]),k(this,Q,!0),this.options=t,this.value=s,this.openLevel=i??0,this.listSlotHtmlComponent=n??null,this.tagsSortFn=c??null,this.emptyText=r??"No results found...",this.isSingleSelect=v??!1,this.showCount=w??!1,this.disabledBranchNode=C??!1,this.expandSelected=b??!1,this.isIndependentNodes=f??!1,this.rtl=L??!1,this.listClassName=x??"",this.isBoostedRendering=M,this.iconElements=g,this.searchText="",this.intersectionItemsObserver=null,this.selectedNodes={nodes:[],groupedNodes:[],allNodes:[]},this.optionsTreeMap=os({options:this.options,openLevel:this.openLevel,isIndependentNodes:this.isIndependentNodes}),this.beforeSearchStateMap=new Map,this.emptyListHtmlElement=null,this.srcElement=o(this,h,Vt).call(this),this.inputCallback=O,this.arrowClickCallback=V,this.mouseupCallback=I}updateValue(t){this.value=t,E(this,R,this.isSingleSelect?this.value:[]),xs({newValue:t,optionsTreeMap:this.optionsTreeMap,isSingleSelect:this.isSingleSelect,expandSelected:this.expandSelected,isFirstValueUpdate:l(this,Q),isIndependentNodes:this.isIndependentNodes}),o(this,h,Y).call(this),E(this,Q,!1),o(this,h,me).call(this)}updateSearchValue(t){if(t===this.searchText)return;let s=this.searchText===""&&t!=="";this.searchText=t,s&&ms({beforeSearchStateMap:this.beforeSearchStateMap,optionsTreeMap:this.optionsTreeMap}),this.searchText===""&&ps({beforeSearchStateMap:this.beforeSearchStateMap,optionsTreeMap:this.optionsTreeMap}),this.searchText&&us(this.optionsTreeMap,t),o(this,h,Y).call(this),this.focusFirstListElement()}callKeyAction(t){E(this,W,!1);let s=this.srcElement.querySelector(".treeselect-list__item--focused");if(s?.classList.contains("treeselect-list__item--hidden"))return;let i=t.key;i==="Enter"&&s&&s.dispatchEvent(new Event("mousedown")),(i==="ArrowLeft"||i==="ArrowRight")&&o(this,h,It).call(this,s,t),(i==="ArrowDown"||i==="ArrowUp")&&o(this,h,Bt).call(this,s,i)}focusFirstListElement(){let t="treeselect-list__item--focused",s=this.srcElement.querySelector(`.${t}`),i=o(this,h,pe).call(this);if(!i.length)return;s&&s.classList.remove(t);let[n]=i;n.classList.add(t)}isLastFocusedElementExist(){return!!l(this,D)}destroy(){this.intersectionItemsObserver&&this.intersectionItemsObserver.disconnect()}};D=new WeakMap,W=new WeakMap,R=new WeakMap,Q=new WeakMap,h=new WeakSet,Y=function(){bs({optionsTreeMap:this.optionsTreeMap,emptyListHtmlElement:this.emptyListHtmlElement,iconElements:this.iconElements,previousSingleSelectedValue:l(this,R),rtl:this.rtl})},It=function(e,t){if(!e)return;let s=t.key,i=e.querySelector(".treeselect-list__item-checkbox").getAttribute("input-id"),n=as(i,this.optionsTreeMap),c=n.arrowItemHtmlElement;s==="ArrowLeft"&&!n.isClosed&&n.isGroup&&(c.dispatchEvent(new Event("mousedown")),t.preventDefault()),s==="ArrowRight"&&n.isClosed&&n.isGroup&&(c.dispatchEvent(new Event("mousedown")),t.preventDefault())},Bt=function(e,t){var s;let i=o(this,h,pe).call(this);if(!i.length)return;let n="treeselect-list__item--focused";if(e){let c=i.findIndex(L=>L.classList.contains(n));i[c].classList.remove(n);let r=t==="ArrowDown"?c+1:c-1,v=t==="ArrowDown"?0:i.length-1,g=!i[r],w=i[r]??i[v];w.classList.add(n);let C=this.srcElement.getBoundingClientRect(),b=w.getBoundingClientRect();if(g&&t==="ArrowDown"){this.srcElement.scroll(0,0);return}if(g&&t==="ArrowUp"){this.srcElement.scroll(0,this.srcElement.scrollHeight);return}let f=((s=this.listSlotHtmlComponent)==null?void 0:s.clientHeight)??0;if(C.y+C.height<b.y+b.height+f){this.srcElement.scroll(0,this.srcElement.scrollTop+b.height);return}if(C.y>b.y){this.srcElement.scroll(0,this.srcElement.scrollTop-b.height);return}}else{let[c]=i;c.classList.add(n)}},Vt=function(){let e=o(this,h,De).call(this),t=o(this,h,le).call(this,this.options);e.append(...t);let s=o(this,h,je).call(this);e.append(s);let i=o(this,h,Fe).call(this);return i&&e.append(i),e},De=function(){let e=document.createElement("div");return e.classList.add("treeselect-list"),this.listClassName.length>0&&e.classList.add(this.listClassName),this.isSingleSelect&&e.classList.add("treeselect-list--single-select"),this.disabledBranchNode&&e.classList.add("treeselect-list--disabled-branch-node"),e.addEventListener("mouseout",t=>o(this,h,Re).call(this,t)),e.addEventListener("mousemove",()=>o(this,h,$e).call(this)),e.addEventListener("mouseup",()=>this.mouseupCallback(),!0),this.isBoostedRendering&&(this.intersectionItemsObserver=vs(e)),e},Re=function(e){e.stopPropagation(),l(this,D)&&l(this,W)&&l(this,D).classList.add("treeselect-list__item--focused")},$e=function(){E(this,W,!0)},le=function(e){return e.reduce((t,s)=>{var i;if((i=s.children)!=null&&i.length){let c=o(this,h,Ue).call(this,s),r=o(this,h,le).call(this,s.children);return c.append(...r),t.push(c),t}let n=o(this,h,ne).call(this,s,!1);return t.push(n),t},[])},Fe=function(){if(!this.listSlotHtmlComponent)return null;let e=document.createElement("div");return e.classList.add("treeselect-list__slot"),e.appendChild(this.listSlotHtmlComponent),e},je=function(){let e=document.createElement("div");e.classList.add("treeselect-list__empty"),e.setAttribute("title",this.emptyText);let t=document.createElement("span");t.classList.add("treeselect-list__empty-icon"),A(this.iconElements.attention,t);let s=document.createElement("span");return s.classList.add("treeselect-list__empty-text"),s.textContent=this.emptyText,e.append(t,s),this.emptyListHtmlElement=e,e},Ue=function(e){let t=document.createElement("div");t.setAttribute("group-container-id",e.value.toString()),t.classList.add("treeselect-list__group-container");let s=o(this,h,ne).call(this,e,!0);return t.appendChild(s),t},ne=function(e,t){let s=o(this,h,qe).call(this,e);if(t){let c=o(this,h,Xe).call(this,e);s.appendChild(c),s.classList.add("treeselect-list__item--group")}let i=o(this,h,Ze).call(this,e),n=o(this,h,Qe).call(this,e,t);return s.append(i,n),s},qe=function(e){let t=document.createElement("div");t.setAttribute("tabindex","-1"),t.setAttribute("title",e.name),ks(t,e.htmlAttr),t.classList.add("treeselect-list__item"),t.addEventListener("mouseover",()=>o(this,h,ze).call(this,t),!0),t.addEventListener("mouseout",()=>o(this,h,Ye).call(this,t),!0),t.addEventListener("mousedown",i=>o(this,h,Ke).call(this,i,e)),this.intersectionItemsObserver&&this.intersectionItemsObserver.observe(t);let s=this.optionsTreeMap.get(e.value);return s&&(s.itemHtmlElement=t),t},ze=function(e){l(this,W)&&o(this,h,oe).call(this,!0,e)},Ye=function(e){l(this,W)&&(o(this,h,oe).call(this,!1,e),E(this,D,e))},Ke=function(e,t){e.preventDefault(),e.stopPropagation();let s=this.optionsTreeMap.get(t.value)??null;if(s!=null&&s.disabled)return;let i=s?.checkboxHtmlElement;i&&(i.checked=!i.checked,o(this,h,tt).call(this,i,t))},Xe=function(e){let t=document.createElement("span");t.setAttribute("tabindex","-1"),t.classList.add("treeselect-list__item-icon"),A(this.iconElements.arrowDown,t),t.addEventListener("mousedown",i=>o(this,h,Je).call(this,i,e));let s=this.optionsTreeMap.get(e.value);return s&&(s.arrowItemHtmlElement=t),t},Je=function(e,t){e.preventDefault(),e.stopPropagation(),o(this,h,st).call(this,t)},Ze=function(e){let t=document.createElement("div");t.classList.add("treeselect-list__item-checkbox-container");let s=document.createElement("span");s.classList.add("treeselect-list__item-checkbox-icon"),s.innerHTML="";let i=document.createElement("input");i.setAttribute("tabindex","-1"),i.setAttribute("type","checkbox"),i.setAttribute("input-id",e.value.toString()),i.classList.add("treeselect-list__item-checkbox"),t.append(s,i);let n=this.optionsTreeMap.get(e.value);return n&&(n.checkboxHtmlElement=i,n.checkboxIconHtmlElement=s),t},Qe=function(e,t){let s=document.createElement("label");if(s.textContent=e.name,s.classList.add("treeselect-list__item-label"),t&&this.showCount){let i=o(this,h,et).call(this,e);s.appendChild(i)}return s},et=function(e){var t;let s=document.createElement("span"),i=((t=this.optionsTreeMap.get(e.value))==null?void 0:t.children)??[];return s.textContent=`(${i.length})`,s.classList.add("treeselect-list__item-label-counter"),s},pe=function(){let e=[];return this.optionsTreeMap.forEach(t=>{!t.hidden&&t.itemHtmlElement&&e.push(t.itemHtmlElement)}),e},tt=function(e,t){let s=this.optionsTreeMap.get(t.value)??null;if(s===null)return;let i=s.isGroupSelectable??!0;if(s.isGroup&&(this.disabledBranchNode||!i)){let n=s.arrowItemHtmlElement;n?.dispatchEvent(new Event("mousedown"));return}if(this.isSingleSelect){let[n]=l(this,R);if(s.id===n)return;E(this,R,[s.id]),xt({newValue:[s.id],optionsTreeMap:this.optionsTreeMap,isSingleSelect:this.isSingleSelect,isIndependentNodes:this.isIndependentNodes})}else{s.checked=e.checked;let n=Ce({option:s,optionsTreeMap:this.optionsTreeMap,isIndependentNodes:this.isIndependentNodes});e.checked=n}o(this,h,Y).call(this),o(this,h,it).call(this)},st=function(e){let t=this.optionsTreeMap.get(e.value)??null;t!==null&&(t.isClosed=!t.isClosed,X(this.optionsTreeMap,t),o(this,h,Y).call(this),this.arrowClickCallback(t.id,t.isClosed))},oe=function(e,t){let s="treeselect-list__item--focused";if(e){let i=Array.from(this.srcElement.querySelectorAll(`.${s}`));i.length&&i.forEach(n=>n.classList.remove(s)),t.classList.add(s)}else t.classList.remove(s)},J=function(e){return this.tagsSortFn===null?e:[...e].sort((t,s)=>this.tagsSortFn({value:t.id,name:t.name},{value:s.id,name:s.name}))},me=function(){let{ungroupedNodes:e,groupedNodes:t,allNodes:s}=cs(this.optionsTreeMap);this.selectedNodes={nodes:o(this,h,J).call(this,e),groupedNodes:o(this,h,J).call(this,t),allNodes:o(this,h,J).call(this,s)}},it=function(){o(this,h,me).call(this),this.inputCallback(this.selectedNodes),this.value=this.selectedNodes.nodes.map(e=>e.id)};var lt=({parentHtmlContainer:e,staticList:t,appendToBody:s,isSingleSelect:i,value:n,direction:c})=>{e||console.error("Validation: parentHtmlContainer prop is required!"),t&&s&&console.error("Validation: You should set staticList to false if you use appendToBody!"),i&&Array.isArray(n)&&console.error("Validation: if you use isSingleSelect prop, you should pass a single value!"),!i&&!Array.isArray(n)&&console.error("Validation: you should pass an array as a value!"),c&&c!=="auto"&&c!=="bottom"&&c!=="top"&&console.error("Validation: you should pass (auto | top | bottom | undefined) as a value for the direction prop!")},Z=e=>e.map(t=>t.id),Ms=e=>e==null?[]:Array.isArray(e)?e:[e],Ns=(e,t)=>{if(t){let[s]=e;return s??null}return e},p,m,j,ee,U,_,H,S,P,d,be,ae,nt,ot,at,ct,ht,rt,ge,dt,ut,pt,mt,fe,ke,se,ce,te,vt,he,bt,gt,ft,kt,Et,ie=class{constructor({parentHtmlContainer:t,value:s,options:i,openLevel:n,appendToBody:c,alwaysOpen:r,showTags:v,tagsCountText:g,tagsSortFn:w,clearable:C,searchable:b,placeholder:f,grouped:L,isGroupedValue:x,listSlotHtmlComponent:M,disabled:O,emptyText:V,staticList:I,id:N,ariaLabel:F,isSingleSelect:Le,showCount:Pt,disabledBranchNode:Gt,direction:Wt,expandSelected:Dt,saveScrollPosition:Rt,isIndependentNodes:$t,rtl:Ft,listClassName:jt,isBoostedRendering:Ut,iconElements:qt,inputCallback:zt,openCallback:Yt,closeCallback:Kt,nameChangeCallback:Xt,searchCallback:Jt,openCloseGroupCallback:Zt}){k(this,d),a(this,"parentHtmlContainer"),a(this,"value"),a(this,"options"),a(this,"openLevel"),a(this,"appendToBody"),a(this,"alwaysOpen"),a(this,"showTags"),a(this,"tagsCountText"),a(this,"tagsSortFn"),a(this,"clearable"),a(this,"searchable"),a(this,"placeholder"),a(this,"grouped"),a(this,"isGroupedValue"),a(this,"listSlotHtmlComponent"),a(this,"disabled"),a(this,"emptyText"),a(this,"staticList"),a(this,"id"),a(this,"ariaLabel"),a(this,"isSingleSelect"),a(this,"showCount"),a(this,"disabledBranchNode"),a(this,"direction"),a(this,"expandSelected"),a(this,"saveScrollPosition"),a(this,"isIndependentNodes"),a(this,"rtl"),a(this,"listClassName"),a(this,"isBoostedRendering"),a(this,"iconElements"),a(this,"inputCallback"),a(this,"openCallback"),a(this,"closeCallback"),a(this,"nameChangeCallback"),a(this,"searchCallback"),a(this,"openCloseGroupCallback"),a(this,"ungroupedValue"),a(this,"groupedValue"),a(this,"allValue"),a(this,"isListOpened"),a(this,"selectedName"),a(this,"srcElement"),k(this,p,null),k(this,m,null),k(this,j,null),k(this,ee,0),k(this,U,0),k(this,_,null),k(this,H,null),k(this,S,null),k(this,P,null),lt({parentHtmlContainer:t,value:s,staticList:I,appendToBody:c,isSingleSelect:Le}),this.parentHtmlContainer=t,this.value=[],this.options=i??[],this.openLevel=n??0,this.appendToBody=c??!1,this.alwaysOpen=!!(r&&!O),this.showTags=v??!0,this.tagsCountText=g??"elements selected",this.tagsSortFn=w??null,this.clearable=C??!0,this.searchable=b??!0,this.placeholder=f??"Search...",this.grouped=L??!0,this.isGroupedValue=x??!1,this.listSlotHtmlComponent=M??null,this.disabled=O??!1,this.emptyText=V??"No results found...",this.staticList=!!(I&&!this.appendToBody),this.id=N??"",this.ariaLabel=F??"",this.isSingleSelect=Le??!1,this.showCount=Pt??!1,this.disabledBranchNode=Gt??!1,this.direction=Wt??"auto",this.expandSelected=Dt??!1,this.saveScrollPosition=Rt??!0,this.isIndependentNodes=$t??!1,this.rtl=Ft??!1,this.listClassName=jt??"",this.isBoostedRendering=Ut??!1,this.iconElements=Te(qt),this.inputCallback=zt,this.openCallback=Yt,this.closeCallback=Kt,this.nameChangeCallback=Xt,this.searchCallback=Jt,this.openCloseGroupCallback=Zt,this.ungroupedValue=[],this.groupedValue=[],this.allValue=[],this.isListOpened=!1,this.selectedName="",this.srcElement=null,o(this,d,be).call(this,s)}mount(){lt({parentHtmlContainer:this.parentHtmlContainer,value:this.value,staticList:this.staticList,appendToBody:this.appendToBody,isSingleSelect:this.isSingleSelect}),this.iconElements=Te(this.iconElements),o(this,d,be).call(this,this.value)}updateValue(t){let s=Ms(t),i=l(this,p);i&&(i.updateValue(s),o(this,d,ge).call(this,i?.selectedNodes))}destroy(){var t;this.srcElement&&(o(this,d,fe).call(this),this.srcElement.innerHTML="",this.srcElement=null,o(this,d,te).call(this,!0),(t=l(this,p))==null||t.destroy())}focus(){l(this,m)&&l(this,m).focus()}toggleOpenClose(){l(this,m)&&(l(this,m).openClose(),l(this,m).focus())}scrollWindowHandler(){this.updateListPosition()}focusWindowHandler(t){var s,i,n;(s=this.srcElement)!=null&&s.contains(t.target)||(i=l(this,p))!=null&&i.srcElement.contains(t.target)||((n=l(this,m))==null||n.blur(),o(this,d,te).call(this,!1),o(this,d,se).call(this,!1))}blurWindowHandler(){var t;(t=l(this,m))==null||t.blur(),o(this,d,te).call(this,!1),o(this,d,se).call(this,!1)}updateListPosition(){var t;let s=this.srcElement,i=(t=l(this,p))==null?void 0:t.srcElement;if(!s||!i)return;if(this.staticList){i.setAttribute("direction","bottom"),o(this,d,ke).call(this,!1,this.appendToBody);return}let{height:n}=i.getBoundingClientRect(),{x:c,y:r,height:v,width:g}=s.getBoundingClientRect(),w=window.innerHeight,C=r,b=w-r-v,f=C>b&&C>=n&&b<n;if(this.direction!=="auto"&&(f=this.direction==="top"),this.appendToBody){(i.style.top!=="0px"||i.style.left!=="0px")&&(i.style.top="0px",i.style.left="0px");let x=c+window.scrollX,M=f?r+window.scrollY-n:r+window.scrollY+v;i.style.transform=`translate(${x}px,${M}px)`,i.style.width=`${g}px`}let L=f?"top":"bottom";i.getAttribute("direction")!==L&&(i.setAttribute("direction",L),o(this,d,ke).call(this,f,this.appendToBody))}};p=new WeakMap,m=new WeakMap,j=new WeakMap,ee=new WeakMap,U=new WeakMap,_=new WeakMap,H=new WeakMap,S=new WeakMap,P=new WeakMap,d=new WeakSet,be=function(e){var t;this.destroy();let{container:s,list:i,input:n}=o(this,d,nt).call(this);this.srcElement=s,E(this,p,i),E(this,m,n),E(this,_,this.scrollWindowHandler.bind(this)),E(this,H,this.scrollWindowHandler.bind(this)),E(this,S,this.focusWindowHandler.bind(this)),E(this,P,this.blurWindowHandler.bind(this)),this.alwaysOpen&&((t=l(this,m))==null||t.openClose()),this.disabled?this.srcElement.classList.add("treeselect--disabled"):this.srcElement.classList.remove("treeselect--disabled"),this.updateValue(e??this.value)},ae=function({groupedNodes:e,nodes:t,allNodes:s}){this.ungroupedValue=t?Z(t):[],this.groupedValue=e?Z(e):[],this.allValue=s?Z(s):[];let i=[];this.isIndependentNodes||this.isSingleSelect?i=this.allValue:this.isGroupedValue?i=this.groupedValue:i=this.ungroupedValue,this.value=Ns(i,this.isSingleSelect)},nt=function(){let e=this.parentHtmlContainer;e.classList.add("treeselect");let t=new ve({value:[],options:this.options,openLevel:this.openLevel,listSlotHtmlComponent:this.listSlotHtmlComponent,tagsSortFn:this.tagsSortFn,emptyText:this.emptyText,isSingleSelect:this.isSingleSelect,showCount:this.showCount,disabledBranchNode:this.disabledBranchNode,expandSelected:this.expandSelected,isIndependentNodes:this.isIndependentNodes,rtl:this.rtl,listClassName:this.listClassName,isBoostedRendering:this.isBoostedRendering,iconElements:this.iconElements,inputCallback:i=>o(this,d,dt).call(this,i),arrowClickCallback:(i,n)=>o(this,d,ut).call(this,i,n),mouseupCallback:()=>{var i;return(i=l(this,m))==null?void 0:i.focus()}}),s=new de({value:[],showTags:this.showTags,tagsCountText:this.tagsCountText,clearable:this.clearable,isAlwaysOpened:this.alwaysOpen,searchable:this.searchable,placeholder:this.placeholder,disabled:this.disabled,isSingleSelect:this.isSingleSelect,id:this.id,ariaLabel:this.ariaLabel,iconElements:this.iconElements,inputCallback:i=>o(this,d,ot).call(this,i),searchCallback:i=>o(this,d,ct).call(this,i),openCallback:()=>o(this,d,mt).call(this),closeCallback:()=>o(this,d,fe).call(this),keydownCallback:i=>o(this,d,at).call(this,i),focusCallback:()=>o(this,d,ht).call(this),blurCallback:()=>o(this,d,rt).call(this),nameChangeCallback:i=>o(this,d,pt).call(this,i)});return this.rtl&&(e.setAttribute("dir","rtl"),t.srcElement.setAttribute("dir","rtl")),this.appendToBody&&E(this,j,new ResizeObserver(()=>this.updateListPosition())),e.append(s.srcElement),{container:e,list:t,input:s}},ot=function(e){var t,s;let i=Z(e);(t=l(this,p))==null||t.updateValue(i);let n=((s=l(this,p))==null?void 0:s.selectedNodes)??{};o(this,d,ae).call(this,n),o(this,d,he).call(this)},at=function(e){var t;this.isListOpened&&((t=l(this,p))==null||t.callKeyAction(e))},ct=function(e){l(this,U)&&clearTimeout(l(this,U)),E(this,U,window.setTimeout(()=>{var t;(t=l(this,p))==null||t.updateSearchValue(e),this.updateListPosition()},350)),o(this,d,kt).call(this,e)},ht=function(){o(this,d,se).call(this,!0),l(this,S)&&l(this,S)&&l(this,P)&&(document.addEventListener("mousedown",l(this,S),!0),document.addEventListener("focus",l(this,S),!0),window.addEventListener("blur",l(this,P)))},rt=function(){setTimeout(()=>{var e,t;let s=(e=l(this,m))==null?void 0:e.srcElement.contains(document.activeElement),i=(t=l(this,p))==null?void 0:t.srcElement.contains(document.activeElement);!s&&!i&&this.blurWindowHandler()},1)},ge=function(e){var t;if(!e)return;let s=[];this.isIndependentNodes||this.isSingleSelect?s=e.allNodes:this.grouped?s=e.groupedNodes:s=e.nodes,(t=l(this,m))==null||t.updateValue(s),o(this,d,ae).call(this,e)},dt=function(e){var t,s,i;o(this,d,ge).call(this,e),this.isSingleSelect&&!this.alwaysOpen&&((t=l(this,m))==null||t.openClose(),(s=l(this,m))==null||s.clearSearch()),(i=l(this,m))==null||i.focus(),o(this,d,he).call(this)},ut=function(e,t){var s;(s=l(this,m))==null||s.focus(),this.updateListPosition(),o(this,d,Et).call(this,e,t)},pt=function(e){this.selectedName!==e&&(this.selectedName=e,o(this,d,bt).call(this))},mt=function(){var e;this.isListOpened=!0,l(this,_)&&l(this,H)&&(window.addEventListener("scroll",l(this,_),!0),window.addEventListener("resize",l(this,H))),!(!l(this,p)||!this.srcElement)&&(this.appendToBody?(document.body.appendChild(l(this,p).srcElement),(e=l(this,j))==null||e.observe(this.srcElement)):this.srcElement.appendChild(l(this,p).srcElement),this.updateListPosition(),o(this,d,ce).call(this,!0),o(this,d,vt).call(this),o(this,d,gt).call(this))},fe=function(){var e;this.alwaysOpen||(this.isListOpened=!1,l(this,_)&&l(this,H)&&(window.removeEventListener("scroll",l(this,_),!0),window.removeEventListener("resize",l(this,H))),!l(this,p)||!this.srcElement)||!(this.appendToBody?document.body.contains(l(this,p).srcElement):this.srcElement.contains(l(this,p).srcElement))||(E(this,ee,l(this,p).srcElement.scrollTop),this.appendToBody?(document.body.removeChild(l(this,p).srcElement),(e=l(this,j))==null||e.disconnect()):this.srcElement.removeChild(l(this,p).srcElement),o(this,d,ce).call(this,!1),o(this,d,ft).call(this))},ke=function(e,t){if(!l(this,p)||!l(this,m))return;let s=t?"treeselect-list--top-to-body":"treeselect-list--top",i=t?"treeselect-list--bottom-to-body":"treeselect-list--bottom";e?(l(this,p).srcElement.classList.add(s),l(this,p).srcElement.classList.remove(i),l(this,m).srcElement.classList.add("treeselect-input--top"),l(this,m).srcElement.classList.remove("treeselect-input--bottom")):(l(this,p).srcElement.classList.remove(s),l(this,p).srcElement.classList.add(i),l(this,m).srcElement.classList.remove("treeselect-input--top"),l(this,m).srcElement.classList.add("treeselect-input--bottom"))},se=function(e){!l(this,m)||!l(this,p)||(e?(l(this,m).srcElement.classList.add("treeselect-input--focused"),l(this,p).srcElement.classList.add("treeselect-list--focused")):(l(this,m).srcElement.classList.remove("treeselect-input--focused"),l(this,p).srcElement.classList.remove("treeselect-list--focused")))},ce=function(e){var t,s,i,n;e?(t=l(this,m))==null||t.srcElement.classList.add("treeselect-input--opened"):(s=l(this,m))==null||s.srcElement.classList.remove("treeselect-input--opened"),this.staticList?(i=l(this,p))==null||i.srcElement.classList.add("treeselect-list--static"):(n=l(this,p))==null||n.srcElement.classList.remove("treeselect-list--static")},te=function(e){!l(this,_)||!l(this,H)||!l(this,S)||!l(this,P)||((!this.alwaysOpen||e)&&(window.removeEventListener("scroll",l(this,_),!0),window.removeEventListener("resize",l(this,H))),document.removeEventListener("mousedown",l(this,S),!0),document.removeEventListener("focus",l(this,S),!0),window.removeEventListener("blur",l(this,P)))},vt=function(){var e,t,s;let i=(e=l(this,p))==null?void 0:e.isLastFocusedElementExist();this.saveScrollPosition&&i?(t=l(this,p))==null||t.srcElement.scroll(0,l(this,ee)):(s=l(this,p))==null||s.focusFirstListElement()},he=function(){var e;(e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("input",{detail:this.value})),this.inputCallback&&this.inputCallback(this.value)},bt=function(){var e;(e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("name-change",{detail:this.selectedName})),this.nameChangeCallback&&this.nameChangeCallback(this.selectedName)},gt=function(){var e;this.alwaysOpen||((e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("open",{detail:this.value})),this.openCallback&&this.openCallback(this.value))},ft=function(){var e;this.alwaysOpen||((e=this.srcElement)==null||e.dispatchEvent(new CustomEvent("close",{detail:this.value})),this.closeCallback&&this.closeCallback(this.value))},kt=function(e){var t;let s=e?.trim()??"";(t=this.srcElement)==null||t.dispatchEvent(new CustomEvent("search",{detail:s})),this.searchCallback&&this.searchCallback(s)},Et=function(e,t){var s;(s=this.srcElement)==null||s.dispatchEvent(new CustomEvent("open-close-group",{detail:{groupId:e,isClosed:t}})),this.openCloseGroupCallback&&this.openCloseGroupCallback(e,t)};function _s({state:e,name:t,options:s,searchable:i,showCount:n,placeholder:c,rtl:r,disabledBranchNode:v=!0,disabled:g=!1,isSingleSelect:w=!0,showTags:C=!0,clearable:b=!0,isIndependentNodes:f=!0,alwaysOpen:L=!1,emptyText:x,expandSelected:M=!0,grouped:O=!0,openLevel:V=0,direction:I="auto"}){return{state:e,tree:null,formatState:function(N){return Array.isArray(N)?(N??[]).map(F=>F?.toString()):N?.toString()},init(){this.tree=new ie({id:`tree-${t}-id`,ariaLabel:`tree-${t}-label`,parentHtmlContainer:this.$refs.tree,value:this.formatState(this.state),options:s,searchable:i,showCount:n,placeholder:c,disabledBranchNode:v,disabled:g,isSingleSelect:w,showTags:C,clearable:b,isIndependentNodes:f,alwaysOpen:L,emptyText:x,expandSelected:M,grouped:O,openLevel:V,direction:I,rtl:r}),this.tree.srcElement.addEventListener("input",N=>{this.state=N.detail})}}}export{_s as default};
