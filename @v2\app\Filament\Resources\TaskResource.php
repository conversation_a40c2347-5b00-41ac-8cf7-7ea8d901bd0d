<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TaskResource\Pages;
use App\Models\Task;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TaskResource extends Resource
{
    protected static ?string $model = Task::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';

    protected static ?string $navigationGroup = 'Planning';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Tasks & Schedules';

    protected static ?string $modelLabel = 'Task';

    protected static ?string $pluralModelLabel = 'Tasks';

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'tasks';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        
                        Forms\Components\Select::make('assigned_to')
                            ->label('Assigned To')
                            ->relationship('assignedUser', 'name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->where('is_active', true);
                            })
                            ->searchable()
                            ->preload()
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('priority')
                            ->options([
                                'low' => 'Low',
                                'medium' => 'Medium',
                                'high' => 'High',
                                'urgent' => 'Urgent',
                            ])
                            ->default('medium')
                            ->required()
                            ->columnSpan(1),
                        
                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->placeholder('Task description and details')
                            ->columnSpanFull(),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Schedule & Timing')
                    ->schema([
                        Forms\Components\Toggle::make('is_all_day')
                            ->label('All Day Task')
                            ->live()
                            ->columnSpan(1),
                        
                        Forms\Components\DateTimePicker::make('start_datetime')
                            ->label('Start Date & Time')
                            ->required()
                            ->seconds(false)
                            ->visible(fn (Forms\Get $get): bool => !$get('is_all_day'))
                            ->columnSpan(1),
                        
                        Forms\Components\DatePicker::make('start_datetime')
                            ->label('Date')
                            ->required()
                            ->visible(fn (Forms\Get $get): bool => $get('is_all_day'))
                            ->columnSpan(1),
                        
                        Forms\Components\DateTimePicker::make('end_datetime')
                            ->label('End Date & Time')
                            ->seconds(false)
                            ->after('start_datetime')
                            ->visible(fn (Forms\Get $get): bool => !$get('is_all_day'))
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('status')
                            ->options([
                                'pending' => 'Pending',
                                'in_progress' => 'In Progress',
                                'completed' => 'Completed',
                                'cancelled' => 'Cancelled',
                            ])
                            ->default('pending')
                            ->required()
                            ->columnSpan(1),
                        
                        Forms\Components\ColorPicker::make('color')
                            ->label('Display Color')
                            ->default('#3B82F6')
                            ->columnSpan(1),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Alert Settings')
                    ->schema([
                        Forms\Components\Toggle::make('has_alert')
                            ->label('Enable Alert')
                            ->live()
                            ->columnSpan(1),
                        
                        Forms\Components\DateTimePicker::make('alert_datetime')
                            ->label('Custom Alert Time')
                            ->seconds(false)
                            ->visible(fn (Forms\Get $get): bool => $get('has_alert'))
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('alert_minutes_before')
                            ->label('Alert Before Start')
                            ->options([
                                5 => '5 minutes before',
                                10 => '10 minutes before',
                                15 => '15 minutes before',
                                30 => '30 minutes before',
                                60 => '1 hour before',
                                120 => '2 hours before',
                                1440 => '1 day before',
                            ])
                            ->visible(fn (Forms\Get $get): bool => $get('has_alert'))
                            ->columnSpan(1),
                        
                        Forms\Components\Fieldset::make('Notification Methods')
                            ->schema([
                                Forms\Components\Toggle::make('alert_sound')
                                    ->label('Sound Alert')
                                    ->default(true),
                                
                                Forms\Components\Toggle::make('alert_email')
                                    ->label('Email Alert'),
                                
                                Forms\Components\Toggle::make('alert_line')
                                    ->label('LINE Alert'),
                                
                                Forms\Components\TextInput::make('line_user_id')
                                    ->label('LINE User ID')
                                    ->visible(fn (Forms\Get $get): bool => $get('alert_line'))
                                    ->placeholder('LINE user ID for notifications'),
                            ])
                            ->visible(fn (Forms\Get $get): bool => $get('has_alert'))
                            ->columns(2)
                            ->columnSpanFull(),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Recurring Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_recurring')
                            ->label('Recurring Task')
                            ->live()
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('recurring_frequency')
                            ->label('Frequency')
                            ->options([
                                'daily' => 'Daily',
                                'weekly' => 'Weekly',
                                'monthly' => 'Monthly',
                                'yearly' => 'Yearly',
                            ])
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->required(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('recurring_interval')
                            ->label('Interval')
                            ->numeric()
                            ->minValue(1)
                            ->default(1)
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->placeholder('Every X days/weeks/months')
                            ->columnSpan(1),
                        
                        Forms\Components\CheckboxList::make('recurring_days')
                            ->label('Days of Week')
                            ->options([
                                'monday' => 'Monday',
                                'tuesday' => 'Tuesday',
                                'wednesday' => 'Wednesday',
                                'thursday' => 'Thursday',
                                'friday' => 'Friday',
                                'saturday' => 'Saturday',
                                'sunday' => 'Sunday',
                            ])
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring') && $get('recurring_frequency') === 'weekly')
                            ->columns(3)
                            ->columnSpanFull(),
                        
                        Forms\Components\DatePicker::make('recurring_end_date')
                            ->label('End Date')
                            ->visible(fn (Forms\Get $get): bool => $get('is_recurring'))
                            ->after('start_datetime')
                            ->columnSpan(1),
                        
                        Forms\Components\Hidden::make('recurring_pattern')
                            ->dehydrateStateUsing(function (Forms\Get $get) {
                                if (!$get('is_recurring')) {
                                    return null;
                                }
                                return [
                                    'frequency' => $get('recurring_frequency'),
                                    'interval' => $get('recurring_interval') ?? 1,
                                    'days' => $get('recurring_days') ?? [],
                                    'end_date' => $get('recurring_end_date'),
                                ];
                            }),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\TextInput::make('location')
                            ->maxLength(255)
                            ->placeholder('Task location or venue')
                            ->columnSpan(1),
                        
                        Forms\Components\Textarea::make('notes')
                            ->rows(3)
                            ->placeholder('Additional notes or comments')
                            ->columnSpan(1),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('assignedUser.name')
                    ->label('Assigned To')
                    ->searchable()
                    ->sortable()
                    ->placeholder('Unassigned'),
                
                Tables\Columns\TextColumn::make('priority')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'low' => 'success',
                        'medium' => 'warning',
                        'high' => 'danger',
                        'urgent' => 'danger',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'in_progress' => 'info',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('formatted_date')
                    ->label('Date')
                    ->sortable(['start_datetime']),
                
                Tables\Columns\TextColumn::make('time_range')
                    ->label('Time')
                    ->badge()
                    ->color('info'),
                
                Tables\Columns\IconColumn::make('has_alert')
                    ->label('Alert')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\IconColumn::make('is_recurring')
                    ->label('Recurring')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('assigned_to')
                    ->label('Assigned To')
                    ->relationship('assignedUser', 'name'),
                
                Tables\Filters\SelectFilter::make('priority')
                    ->options([
                        'low' => 'Low',
                        'medium' => 'Medium',
                        'high' => 'High',
                        'urgent' => 'Urgent',
                    ]),
                
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ]),
                
                Tables\Filters\Filter::make('today')
                    ->label('Today')
                    ->query(fn (Builder $query): Builder => $query->today()),
                
                Tables\Filters\Filter::make('upcoming')
                    ->label('Upcoming')
                    ->query(fn (Builder $query): Builder => $query->upcoming()),
                
                Tables\Filters\TernaryFilter::make('has_alert')
                    ->label('Has Alert'),
                
                Tables\Filters\TernaryFilter::make('is_recurring')
                    ->label('Recurring'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('start_datetime', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\TaskTimetable::route('/'),
            'list' => Pages\ListTasks::route('/list'),
            'create' => Pages\CreateTask::route('/create'),
            'view' => Pages\ViewTask::route('/{record}'),
            'edit' => Pages\EditTask::route('/{record}/edit'),
        ];
    }
}
