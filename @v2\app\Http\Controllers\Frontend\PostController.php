<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Blog\Post;

class PostController extends Controller
{
    public function index()
    {
        // Frontend shows posts from all teams (no tenant scoping)
        $posts = Post::withoutGlobalScopes()
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->with(['team', 'user'])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        return view('frontend.posts.index', compact('posts'));
    }

    public function show($postId)
    {
        // Find post without global scopes
        $post = Post::withoutGlobalScopes()->findOrFail($postId);

        // Check if post is published
        if (!$post->published_at || $post->published_at > now()) {
            abort(404);
        }

        // Load relationships without tenant scoping
        $post->loadMissing(['team', 'user']);

        return view('frontend.posts.show', compact('post'));
    }
}
