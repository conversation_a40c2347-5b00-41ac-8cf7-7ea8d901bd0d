<?php

namespace App\Filament\Resources\Exam\QuestionResource\Pages;

use App\Filament\Resources\Exam\QuestionResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Facades\Filament;

class CreateQuestion extends CreateRecord
{
    protected static string $resource = QuestionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['team_id'] = Filament::getTenant()->id;
        $data['user_id'] = auth()->id();

        return $data;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
