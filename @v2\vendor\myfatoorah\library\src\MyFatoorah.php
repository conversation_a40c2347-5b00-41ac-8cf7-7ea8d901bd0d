<?php namespace MyFatoorah\Library; use Exception; class  MyFatoorah extends MyFatoorahHelper{protected $config=[];protected $apiURL='';protected $version='2.2';public function __construct($config){$mfCountries=self::getMFCountries();$this->setApiKey($config);$this->setIsTest($config);$this->setVcCode($config);$this->config['loggerObj']=empty($config['loggerObj'])?null:$config['loggerObj'];$this->config['loggerFunc']=empty($config['loggerFunc'])?null:$config['loggerFunc'];self::$loggerObj=$this->config['loggerObj'];self::$loggerFunc=$this->config['loggerFunc'];$code=$this->config['vcCode'];$this->apiURL=$this->config['isTest']?$mfCountries[$code]['testv2']:$mfCountries[$code]['v2'];}public function getApiURL(){return $this->apiURL;}protected function setApiKey($config){if(empty($config['apiKey'])){throw new Exception('Config array must have the "apiKey" key.');}$config['apiKey']=trim($config['apiKey']);if(empty($config['apiKey'])){throw new Exception('The "apiKey" key is required and must be a string.');}$this->config['apiKey']=$config['apiKey'];}protected function setIsTest($config){if(!isset($config['isTest'])){throw new Exception('Config array must have the "isTest" key.');}if(!is_bool($config['isTest'])){throw new Exception('The "isTest" key must be boolean.');}$this->config['isTest']=$config['isTest'];}protected function setVcCode($config){$config['vcCode']=$config['vcCode']?? $config['countryCode']?? '';if(empty($config['vcCode'])){throw new Exception('Config array must have the "vcCode" key.');}$mfCountries=self::getMFCountries();$countriesCodes=array_keys($mfCountries);$config['vcCode']=strtoupper($config['vcCode']);if(!in_array($config['vcCode'],$countriesCodes)){throw new Exception('The "vcCode" key must be one of ('.implode(', ',$countriesCodes).').');}$this->config['vcCode']=$config['vcCode'];}public function callAPI($url,$postFields=null,$orderId=null,$function=null){ini_set('precision','14');ini_set('serialize_precision','-1');$request=isset($postFields)?'POST':'GET';$fields=empty($postFields)?json_encode($postFields,JSON_FORCE_OBJECT):json_encode($postFields,JSON_UNESCAPED_UNICODE);$msgLog="Order #$orderId ----- $function";$this->log("$msgLog - Request: $fields");$curl=curl_init($url);$options=[CURLOPT_CUSTOMREQUEST=>$request,CURLOPT_POSTFIELDS=>$fields,CURLOPT_HTTPHEADER=>['Authorization: Bearer '.$this->config['apiKey'],'Content-Type: application/json'],CURLOPT_RETURNTRANSFER=>true];curl_setopt_array($curl,$options);$res=curl_exec($curl);$err=curl_error($curl);curl_close($curl);if($err){$this->log("$msgLog - cURL Error: $err");throw new Exception('cURL Error: '.$err);}$this->log("$msgLog - Response: $res");$json=json_decode((string) $res);$error=self::getAPIError($json,(string) $res);if($error){$this->log("$msgLog - Error: $error");throw new Exception($error);}return $json;}protected static function getAPIError($json,$res){$isSuccess=$json->IsSuccess ?? false;if($isSuccess){return '';}$hErr=self::getHtmlErrors($res);if($hErr){return $hErr;}if(is_string($json)){return $json;}if(empty($json)){return(!empty($res)?$res:'Kindly review your MyFatoorah admin configuration due to a wrong entry.');}return self::getJsonErrors($json);}protected static function getHtmlErrors($res){$stripHtml=strip_tags($res);if($res!=$stripHtml&&stripos($stripHtml,'apple-developer-merchantid-domain-association')!==false){return trim(preg_replace('/\s+/',' ',$stripHtml));}return '';}protected static function getJsonErrors($json){$errorsVar=isset($json->ValidationErrors)?'ValidationErrors':'FieldsErrors';if(isset($json->$errorsVar)){$blogDatas=array_column($json->$errorsVar,'Error','Name');$mapFun=function($k,$v){return"$k: $v";};$errArr=array_map($mapFun,array_keys($blogDatas),array_values($blogDatas));return implode(', ',$errArr);}if(isset($json->Data->ErrorMessage)){return $json->Data->ErrorMessage;}return empty($json->Message)?'':$json->Message;}public static function log($msg){$loggerObj=self::$loggerObj;$loggerFunc=self::$loggerFunc;if(empty($loggerObj)){return;}if(is_string($loggerObj)){error_log(PHP_EOL.date('d.m.Y h:i:s').' - '.$msg,3,$loggerObj);}elseif(method_exists($loggerObj,$loggerFunc)){$loggerObj->{$loggerFunc}($msg);}}}