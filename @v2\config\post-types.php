<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Post Types Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the available post types for the application.
    | Each type can have its own permissions, categories, and routing.
    |
    */

    'types' => [
        'blog' => [
            'name' => 'Blog',
            'slug' => 'blog',
            'description' => 'Blog posts and articles',
            'icon' => 'heroicon-o-document-text',
            'navigation_group' => 'Content',
            'navigation_sort' => 10,
            'permissions' => [
                'view_any_blog_posts',
                'view_blog_posts',
                'create_blog_posts',
                'update_blog_posts',
                'delete_blog_posts',
                'delete_any_blog_posts',
                'force_delete_blog_posts',
                'force_delete_any_blog_posts',
                'restore_blog_posts',
                'restore_any_blog_posts',
                'replicate_blog_posts',
            ],
            'enabled' => true,
        ],

        'news' => [
            'name' => 'News',
            'slug' => 'news',
            'description' => 'News articles and announcements',
            'icon' => 'heroicon-o-newspaper',
            'navigation_group' => 'Content',
            'navigation_sort' => 20,
            'permissions' => [
                'view_any_news_posts',
                'view_news_posts',
                'create_news_posts',
                'update_news_posts',
                'delete_news_posts',
                'delete_any_news_posts',
                'force_delete_news_posts',
                'force_delete_any_news_posts',
                'restore_news_posts',
                'restore_any_news_posts',
                'replicate_news_posts',
            ],
            'enabled' => true,
        ],

        'knowledge' => [
            'name' => 'Knowledge Base',
            'slug' => 'knowledge',
            'description' => 'Knowledge base articles and documentation',
            'icon' => 'heroicon-o-academic-cap',
            'navigation_group' => 'Content',
            'navigation_sort' => 30,
            'permissions' => [
                'view_any_knowledge_posts',
                'view_knowledge_posts',
                'create_knowledge_posts',
                'update_knowledge_posts',
                'delete_knowledge_posts',
                'delete_any_knowledge_posts',
                'force_delete_knowledge_posts',
                'force_delete_any_knowledge_posts',
                'restore_knowledge_posts',
                'restore_any_knowledge_posts',
                'replicate_knowledge_posts',
            ],
            'enabled' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Post Type
    |--------------------------------------------------------------------------
    |
    | The default post type to use when creating new posts or when no type
    | is specified. This should match one of the keys in the 'types' array.
    |
    */

    'default_type' => 'blog',

    /*
    |--------------------------------------------------------------------------
    | URL Structure
    |--------------------------------------------------------------------------
    |
    | Define the URL structure for post types. The {team-slug} and {type}
    | placeholders will be replaced with actual values.
    |
    */

    'url_structure' => 'backend/{team-slug}/{type}/posts',

    /*
    |--------------------------------------------------------------------------
    | Categories
    |--------------------------------------------------------------------------
    |
    | Configuration for type-specific categories.
    |
    */

    'categories' => [
        'type_specific' => true, // Whether categories are specific to each post type
        'shared_authors' => true, // Whether authors are shared across all post types
    ],

    /*
    |--------------------------------------------------------------------------
    | Navigation
    |--------------------------------------------------------------------------
    |
    | Configuration for navigation display.
    |
    */

    'navigation' => [
        'show_categories_in_sidebar' => false, // Move categories to post page top nav
        'group_by_type' => true, // Group navigation items by post type
    ],
];
