<x-filament-widgets::widget>
    <x-filament::section>
        @if(!$this->getViewData()['hasSubscription'])
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            No Active Subscription
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                            <p>{{ $this->getViewData()['teamName'] }} doesn't have an active subscription. Some features may be limited.</p>
                        </div>
                        <div class="mt-4">
                            <div class="-mx-2 -my-1.5 flex">
                                <a href="{{ route('pricing') }}" class="bg-yellow-50 dark:bg-yellow-900/50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 dark:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-900/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600">
                                    View Plans
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800 dark:text-green-200">
                                Active Subscription: {{ $this->getViewData()['planName'] }}
                            </h3>
                        </div>
                    </div>
                    <div class="text-right">
                        @if($this->getViewData()['subscriptionEndsAt'])
                            <p class="text-xs text-green-600 dark:text-green-400">
                                Expires: {{ \Carbon\Carbon::parse($this->getViewData()['subscriptionEndsAt'])->format('M d, Y') }}
                            </p>
                        @endif
                        @if($this->getViewData()['subscriptionCanceledAt'])
                            <p class="text-xs text-red-600 dark:text-red-400">
                                Canceled: {{ \Carbon\Carbon::parse($this->getViewData()['subscriptionCanceledAt'])->format('M d, Y') }}
                            </p>
                        @endif
                    </div>
                </div>
                
                <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-green-200 dark:border-green-800">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Students</p>
                                <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                                    {{ $this->getViewData()['studentCount'] }}
                                    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                                        / {{ $this->getViewData()['studentLimit'] }}
                                    </span>
                                </p>
                            </div>
                            <div class="text-right">
                                @if($this->getViewData()['canAddStudents'])
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                                        Can add more
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200">
                                        Limit reached
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-green-600 dark:bg-green-400 h-2 rounded-full" style="width: {{ ($this->getViewData()['studentCount'] / max($this->getViewData()['studentLimit'], 1)) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-green-200 dark:border-green-800">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-gray-100">Parents</p>
                                <p class="text-2xl font-bold text-green-600 dark:text-green-400">
                                    {{ $this->getViewData()['parentCount'] }}
                                    <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
                                        / {{ $this->getViewData()['parentLimit'] }}
                                    </span>
                                </p>
                            </div>
                            <div class="text-right">
                                @if($this->getViewData()['canAddParents'])
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                                        Can add more
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200">
                                        Limit reached
                                    </span>
                                @endif
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-green-600 dark:bg-green-400 h-2 rounded-full" style="width: {{ ($this->getViewData()['parentCount'] / max($this->getViewData()['parentLimit'], 1)) * 100 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                @if(!$this->getViewData()['canAddStudents'] || !$this->getViewData()['canAddParents'])
                    <div class="mt-4 text-center">
                        <a href="{{ route('pricing') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            Upgrade Plan
                        </a>
                    </div>
                @endif
            </div>
        @endif
    </x-filament::section>
</x-filament-widgets::widget>
