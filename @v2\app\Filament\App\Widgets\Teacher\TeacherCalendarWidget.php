<?php

namespace App\Filament\App\Widgets\Teacher;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TeacherCalendarWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.teacher.teacher-calendar';
    
    protected int | string | array $columnSpan = 'full';

    public $currentMonth;
    public $currentYear;

    public function mount()
    {
        $this->currentMonth = Carbon::now()->month;
        $this->currentYear = Carbon::now()->year;
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('teacher');
    }

    public function getCalendarData(): array
    {
        $startOfMonth = Carbon::create($this->currentYear, $this->currentMonth, 1);
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        
        // Get first day of calendar (might be from previous month)
        $startOfCalendar = $startOfMonth->copy()->startOfWeek(Carbon::SUNDAY);
        
        // Get last day of calendar (might be from next month)
        $endOfCalendar = $endOfMonth->copy()->endOfWeek(Carbon::SATURDAY);
        
        $days = [];
        $current = $startOfCalendar->copy();
        
        while ($current <= $endOfCalendar) {
            $days[] = [
                'date' => $current->day,
                'is_current_month' => $current->month === $this->currentMonth,
                'is_today' => $current->isToday(),
                'events' => $this->getEventsForDate($current),
                'full_date' => $current->format('Y-m-d')
            ];
            $current->addDay();
        }
        
        return [
            'month_name' => $startOfMonth->locale('th')->translatedFormat('F Y'),
            'days' => $days,
            'day_names' => ['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส']
        ];
    }

    private function getEventsForDate(Carbon $date): array
    {
        // Placeholder data - replace with actual schedule data
        $events = [];
        
        if ($date->day === 15) {
            $events = [
                ['subject' => 'คณิต', 'class' => 'ม.1/1', 'time' => '08:30', 'color' => 'blue'],
                ['subject' => 'ไทย', 'class' => 'ม.1/2', 'time' => '10:30', 'color' => 'green'],
            ];
        } elseif ($date->day === 18) {
            $events = [
                ['subject' => 'คณิต', 'class' => 'ม.1/1', 'time' => '08:30', 'color' => 'blue'],
            ];
        } elseif ($date->day === 23) {
            $events = [
                ['subject' => 'ไทย', 'class' => 'ม.1/2', 'time' => '10:30', 'color' => 'green'],
            ];
        } elseif ($date->day === 25) {
            $events = [
                ['subject' => 'คณิต', 'class' => 'ม.1/1', 'time' => '08:30', 'color' => 'blue'],
                ['subject' => 'วิทย์', 'class' => 'ม.2/3', 'time' => '13:30', 'color' => 'yellow'],
            ];
        }
        
        return $events;
    }

    public function previousMonth()
    {
        if ($this->currentMonth === 1) {
            $this->currentMonth = 12;
            $this->currentYear--;
        } else {
            $this->currentMonth--;
        }
    }

    public function nextMonth()
    {
        if ($this->currentMonth === 12) {
            $this->currentMonth = 1;
            $this->currentYear++;
        } else {
            $this->currentMonth++;
        }
    }

    public function getTodayActivities(): array
    {
        // Placeholder data - replace with actual today's schedule
        return [
            [
                'subject' => 'คณิตศาสตร์',
                'class' => 'ม.1/1',
                'time' => '08:30 - 09:20',
                'book' => 'คณิต ม.1 เล่ม 1',
                'lesson' => 'สมการเชิงเส้น',
                'color' => 'blue',
                'resources' => [
                    ['type' => 'pdf', 'name' => 'แผนการสอน'],
                    ['type' => 'worksheet', 'name' => 'ใบงาน'],
                    ['type' => 'powerpoint', 'name' => 'PowerPoint'],
                    ['type' => 'video', 'name' => 'วิดีโอ'],
                    ['type' => 'homework', 'name' => 'เพิ่มการบ้าน'],
                ]
            ],
            [
                'subject' => 'วิทยาศาสตร์',
                'class' => 'ม.2/3',
                'time' => '13:30 - 14:20',
                'book' => 'วิทย์ ม.2 เล่ม 1',
                'lesson' => 'แรงและการเคลื่อนที่',
                'color' => 'yellow',
                'resources' => [
                    ['type' => 'pdf', 'name' => 'แผนการสอน'],
                    ['type' => 'powerpoint', 'name' => 'PowerPoint'],
                    ['type' => 'homework', 'name' => 'เพิ่มการบ้าน'],
                ]
            ],
        ];
    }
}
