<?php

namespace App\Filament\Resources\MediaManager\FolderResource\Pages;

use App\Filament\Resources\MediaManager\Actions\CreateFolderAction;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Validation\ValidationException;
use TomatoPHP\FilamentMediaManager\Models\Folder;
use TomatoPHP\FilamentMediaManager\Models\Media;
use TomatoPHP\FilamentMediaManager\Resources\FolderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFolders extends ManageRecords
{
    protected static string $resource = FolderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateFolderAction::make(),
        ];
    }

    public function mount(): void
    {
        session()->forget('folder_id');
        session()->forget('folder_password');
    }

    public function folderAction(?Folder $item=null){
        return Actions\Action::make('folderAction')
            ->requiresConfirmation(function (array $arguments){
                if($arguments['record']['is_protected']){
                    return true;
                }
                else {
                    return false;
                }
            })
            ->form(function (array $arguments){
                if($arguments['record']['is_protected']){
                    return [
                        TextInput::make('password')
                            ->password()
                            ->revealable()
                            ->required()
                            ->maxLength(255),
                    ];
                }
                else {
                    return null;
                }
            })
            ->action(function (array $arguments, array $data){
                if($arguments['record']['is_protected']){
                    if($arguments['record']['password'] != $data['password']){
                        Notification::make()
                            ->title('Password is incorrect')
                            ->danger()
                            ->send();

                        return ;
                    }
                    else {
                        session()->put('folder_password', $data['password']);
                    }
                }
                if(!$arguments['record']['model_type']){
                    // Use Filament's URL generation for MediaResource
                    $url = \App\Filament\Resources\MediaManager\MediaResource::getUrl('index', ['folder_id' => $arguments['record']['id']]);
                    return redirect()->to($url);
                }
                if(!$arguments['record']['model_id'] && !$arguments['record']['collection']){
                    // Use Filament's URL generation for FolderResource
                    $url = \App\Filament\Resources\MediaManager\FolderResource::getUrl('index', ['model_type' => $arguments['record']['model_type']]);
                    return redirect()->to($url);
                }
                else if(!$arguments['record']['model_id']){
                    // Use Filament's URL generation for FolderResource
                    $url = \App\Filament\Resources\MediaManager\FolderResource::getUrl('index', [
                        'model_type' => $arguments['record']['model_type'],
                        'collection' => $arguments['record']['collection']
                    ]);
                    return redirect()->to($url);
                }
                else {
                    // Use Filament's URL generation for MediaResource
                    $url = \App\Filament\Resources\MediaManager\MediaResource::getUrl('index', ['folder_id' => $arguments['record']['id']]);
                    return redirect()->to($url);
                }
            })
            ->view('filament-media-manager::pages.folder-action', ['item' => $item]);
    }
}
