<?php

namespace App\Observers;

use App\Models\MediaManager\Folder;
use App\Models\User;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // Create personal folder for new user
        if ($user->team_id) {
            Folder::createPersonalFolder($user->id, $user->team_id);
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        // If user's team changed, update their personal folder
        if ($user->isDirty('team_id') && $user->team_id) {
            $personalFolder = Folder::personal()
                ->where('owner_id', $user->id)
                ->first();

            if ($personalFolder) {
                $personalFolder->update(['team_id' => $user->team_id]);
            } else {
                // Create personal folder if it doesn't exist
                Folder::createPersonalFolder($user->id, $user->team_id);
            }
        }
    }

    /**
     * Handle the User "deleting" event.
     */
    public function deleting(User $user): void
    {
        // Mark user's personal folders as deleted
        $personalFolders = Folder::personal()
            ->where('owner_id', $user->id)
            ->get();

        foreach ($personalFolders as $folder) {
            $folder->markAsDeletedUser();
        }

        // Mark other folders created by this user
        $createdFolders = Folder::where('created_by', $user->id)
            ->where('is_personal', false)
            ->get();

        foreach ($createdFolders as $folder) {
            $folder->update([
                'description' => ($folder->description ?: '') . " [Created by deleted user: {$user->name}]"
            ]);
        }
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        // Additional cleanup if needed
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        // Restore user's folders if user is restored
        $deletedFolders = Folder::deletedUsers()
            ->where('owner_id', $user->id)
            ->get();

        foreach ($deletedFolders as $folder) {
            $folder->update([
                'name' => str_replace('-deleted', '', $folder->name),
                'is_deleted_user' => false,
                'description' => str_replace(' [User deleted]', '', $folder->description ?: ''),
            ]);
        }
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        // When user is permanently deleted, we keep the folders marked as deleted
        // They can be cleaned up manually by super admins
    }
}
