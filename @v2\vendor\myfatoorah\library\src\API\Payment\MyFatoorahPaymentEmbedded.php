<?php namespace MyFatoorah\Library\API\Payment; use MyFatoorah\Library\API\MyFatoorahList; class  MyFatoorahPaymentEmbedded extends MyFatoorahPayment{protected static $checkoutGateways;public function getCheckoutGateways($invoiceAmount,$currencyIso,$isApRegistered){if(!empty(self::$checkoutGateways)){return self::$checkoutGateways;}$gateways=$this->initiatePayment($invoiceAmount,$currencyIso);$mfListObj=new MyFatoorahList($this->config);$allRates=$mfListObj->getCurrencyRates();$currencyRate=MyFatoorahList::getOneCurrencyRate($currencyIso,$allRates);self::$checkoutGateways=['all'=>[],'cards'=>[],'form'=>[],'ap'=>[],'gp'=>[]];foreach($gateways as $gateway){$gateway->PaymentTotalAmount=$this->getPaymentTotalAmount($gateway,$allRates,$currencyRate);$gateway->GatewayData=['GatewayTotalAmount'=>number_format($gateway->PaymentTotalAmount,2),'GatewayCurrency'=>$gateway->PaymentCurrencyIso,'GatewayTransCurrency'=>self::getTranslatedCurrency($gateway->PaymentCurrencyIso),];self::$checkoutGateways=$this->addGatewayToCheckout($gateway,self::$checkoutGateways,$isApRegistered);}if($isApRegistered){self::$checkoutGateways['ap']=$this->getOneEmbeddedGateway(self::$checkoutGateways['ap'],$currencyIso,$allRates);}return self::$checkoutGateways;}private function getPaymentTotalAmount($paymentMethod,$allRates,$currencyRate){$dbTrucVal=((int)($paymentMethod->TotalAmount*1000))/1000;if($paymentMethod->PaymentCurrencyIso==$paymentMethod->CurrencyIso){return $this->roundUp($dbTrucVal,2);}$dueVal=($currencyRate==1)?$dbTrucVal:round($paymentMethod->TotalAmount/$currencyRate,3);$baseTotalAmount=$this->roundUp($dueVal,2);$paymentCurrencyRate=MyFatoorahList::getOneCurrencyRate($paymentMethod->PaymentCurrencyIso,$allRates);if($paymentCurrencyRate!=1){$paymentTotalAmount=$baseTotalAmount*$paymentCurrencyRate;return $this->roundUp($paymentTotalAmount,2);}return $baseTotalAmount;}private function roundUp($number,$decimalPlaces){$multi=pow(10,$decimalPlaces);$nrAsStr=(string)($number*$multi);return ceil((float) $nrAsStr)/$multi;}private function getOneEmbeddedGateway($gateways,$displayCurrency,$allRates){$displayCurrencyIndex=array_search($displayCurrency,array_column($gateways,'PaymentCurrencyIso'));if($displayCurrencyIndex){return $gateways[$displayCurrencyIndex];}$defCurKey=array_search('1',array_column($allRates,'Value'));$defaultCurrency=$allRates[$defCurKey]->Text;$defaultCurrencyIndex=array_search($defaultCurrency,array_column($gateways,'PaymentCurrencyIso'));if($defaultCurrencyIndex){return $gateways[$defaultCurrencyIndex];}if(isset($gateways[0])){return $gateways[0];}return[];}public static function getTranslatedCurrency($currency){$currencies=['KWD'=>['en'=>'KD','ar'=>'د.ك'],'SAR'=>['en'=>'SR','ar'=>'ريال'],'BHD'=>['en'=>'BD','ar'=>'د.ب'],'EGP'=>['en'=>'LE','ar'=>'ج.م'],'QAR'=>['en'=>'QR','ar'=>'ر.ق'],'OMR'=>['en'=>'OR','ar'=>'ر.ع'],'JOD'=>['en'=>'JD','ar'=>'د.أ'],'AED'=>['en'=>'AED','ar'=>'د'],'USD'=>['en'=>'USD','ar'=>'دولار'],'EUR'=>['en'=>'EUR','ar'=>'يورو']];return $currencies[$currency]??['en'=>'','ar'=>''];}}