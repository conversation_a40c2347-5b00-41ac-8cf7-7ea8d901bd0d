<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-chart-bar-square class="w-5 h-5 text-primary-600 mr-2" />
                📊 สรุปตารางการทำแบบฝึกหัด
            </div>
        </x-slot>

        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700">
                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-600 dark:text-gray-300">วันที่</th>
                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-600 dark:text-gray-300">วิชา</th>
                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-600 dark:text-gray-300">เวลาสะสม</th>
                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-600 dark:text-gray-300">ผลคะแนน</th>
                        <th class="px-4 py-3 text-left text-sm font-medium text-gray-600 dark:text-gray-300">สถานะการทบทวน</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                    @foreach($this->getActivitySummary() as $activity)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            <td class="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">{{ $activity['date'] }}</td>
                            <td class="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-2
                                        @if($activity['subject'] === 'คณิตศาสตร์') bg-blue-500
                                        @elseif($activity['subject'] === 'วิทยาศาสตร์') bg-green-500
                                        @elseif($activity['subject'] === 'ภาษาไทย') bg-purple-500
                                        @elseif($activity['subject'] === 'ภาษาอังกฤษ') bg-yellow-500
                                        @else bg-gray-500
                                        @endif">
                                    </div>
                                    {{ $activity['subject'] }}
                                </div>
                            </td>
                            <td class="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">{{ $activity['time_spent'] }}</td>
                            <td class="px-4 py-3 text-sm text-gray-700 dark:text-gray-300">
                                <span class="font-medium">{{ $activity['score'] }}</span>
                            </td>
                            <td class="px-4 py-3 text-sm">
                                <span class="
                                    @if($activity['review_color'] === 'green') text-green-600 dark:text-green-400
                                    @elseif($activity['review_color'] === 'red') text-red-500 dark:text-red-400
                                    @endif">
                                    {{ $activity['review_label'] }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
