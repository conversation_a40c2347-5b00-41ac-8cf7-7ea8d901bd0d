<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Book;
use Illuminate\Http\Request;

class BookController extends Controller
{
    public function index()
    {
        $books = Book::where('is_active', true)
            ->with(['subject', 'team'])
            ->paginate(12);

        return view('frontend.books.index', compact('books'));
    }

    public function show(Book $book)
    {
        // Check if book is active
        if (!$book->is_active) {
            abort(404);
        }

        // Load relationships
        $book->load(['subject', 'team', 'lessons' => function ($query) {
            $query->where('is_active', true)->orderBy('sort_order');
        }]);

        return view('frontend.books.show', compact('book'));
    }
}
