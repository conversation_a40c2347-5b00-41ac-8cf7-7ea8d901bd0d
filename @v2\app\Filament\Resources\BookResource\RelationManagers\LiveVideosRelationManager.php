<?php

namespace App\Filament\Resources\BookResource\RelationManagers;

use App\Models\LiveVideo;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Facades\Filament;

class LiveVideosRelationManager extends RelationManager
{
    protected static string $relationship = 'liveVideos';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255)
                    ->columnSpan(2),

                Forms\Components\Textarea::make('description')
                    ->rows(3)
                    ->columnSpanFull(),

                Forms\Components\Select::make('user_id')
                    ->label('Teacher')
                    ->relationship('user', 'name')
                    ->required()
                    ->columnSpan(1),

                Forms\Components\Toggle::make('is_active')
                    ->label('Active')
                    ->default(true)
                    ->columnSpan(1),

                Forms\Components\DateTimePicker::make('scheduled_start_time')
                    ->label('Scheduled Start')
                    ->required()
                    ->columnSpan(1),

                Forms\Components\DateTimePicker::make('scheduled_end_time')
                    ->label('Scheduled End')
                    ->required()
                    ->columnSpan(1),

                Forms\Components\Select::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'live' => 'Live',
                        'ended' => 'Ended',
                        'cancelled' => 'Cancelled',
                    ])
                    ->default('scheduled')
                    ->required()
                    ->columnSpan(1),

                Forms\Components\Toggle::make('is_recording_enabled')
                    ->label('Enable Recording')
                    ->default(true)
                    ->columnSpan(1),

                Forms\Components\Toggle::make('is_public')
                    ->label('Public Access')
                    ->default(false)
                    ->columnSpan(1),

                Forms\Components\TextInput::make('access_password')
                    ->label('Access Password')
                    ->password()
                    ->maxLength(255)
                    ->columnSpan(1),
            ])
            ->columns(2);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('title')
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->label('Teacher')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('scheduled_start_time')
                    ->label('Scheduled Start')
                    ->dateTime('M j, Y g:i A')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'scheduled' => 'warning',
                        'live' => 'success',
                        'ended' => 'gray',
                        'cancelled' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\IconColumn::make('is_recording_enabled')
                    ->label('Recording')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_public')
                    ->label('Public')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'scheduled' => 'Scheduled',
                        'live' => 'Live',
                        'ended' => 'Ended',
                        'cancelled' => 'Cancelled',
                    ]),

                Tables\Filters\TernaryFilter::make('is_public')
                    ->label('Public Access'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['team_id'] = Filament::getTenant()->id;
                        $data['liveable_type'] = $this->getOwnerRecord()::class;
                        $data['liveable_id'] = $this->getOwnerRecord()->id;
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('start_live')
                    ->label('Start')
                    ->icon('heroicon-o-play')
                    ->color('success')
                    ->visible(fn (LiveVideo $record): bool => $record->status === 'scheduled')
                    ->url(fn (LiveVideo $record): string => route('live-videos.stream', $record))
                    ->openUrlInNewTab(),

                Tables\Actions\Action::make('join_live')
                    ->label('Join')
                    ->icon('heroicon-o-video-camera')
                    ->color('success')
                    ->visible(fn (LiveVideo $record): bool => $record->status === 'live')
                    ->url(fn (LiveVideo $record): string => route('live-videos.stream', $record))
                    ->openUrlInNewTab(),

                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('scheduled_start_time', 'desc');
    }
}
