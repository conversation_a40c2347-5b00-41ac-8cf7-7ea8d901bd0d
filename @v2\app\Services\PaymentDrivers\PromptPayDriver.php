<?php

namespace App\Services\PaymentDrivers;

use TomatoPHP\FilamentPayments\Services\Contracts\PaymentDriverInterface;
use TomatoPHP\FilamentPayments\Models\Payment;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class PromptPayDriver implements PaymentDriverInterface
{
    public function pay(Payment $payment): array
    {
        // Generate PromptPay QR code
        $promptPayId = config('services.promptpay.id', '0123456789'); // Your PromptPay ID
        $amount = $payment->amount;
        
        // Generate PromptPay payload
        $payload = $this->generatePromptPayPayload($promptPayId, $amount, $payment->uuid);
        
        // Generate QR code
        $qrCode = QrCode::format('svg')
            ->size(300)
            ->generate($payload);

        $promptPayDetails = [
            'promptpay_id' => $promptPayId,
            'amount' => $amount,
            'reference' => $payment->uuid,
            'qr_code' => $qrCode,
            'payload' => $payload,
        ];

        // Update payment status to pending
        $payment->update([
            'status' => 'pending',
            'gateway_response' => json_encode($promptPayDetails),
        ]);

        return [
            'success' => true,
            'payment_url' => route('payment.promptpay.qr', $payment->uuid),
            'message' => 'Please scan the QR code with your mobile banking app to complete payment.',
            'promptpay_details' => $promptPayDetails,
        ];
    }

    public function verify(Payment $payment): array
    {
        // In a real implementation, you would verify with PromptPay API
        // For now, we'll assume manual verification or webhook confirmation
        
        return [
            'success' => $payment->status === 'completed',
            'message' => $payment->status === 'completed' 
                ? 'Payment verified successfully' 
                : 'Payment verification pending',
        ];
    }

    public function refund(Payment $payment, float $amount = null): array
    {
        $refundAmount = $amount ?? $payment->amount;
        
        // PromptPay refunds typically need to be processed manually
        $payment->update([
            'status' => 'refund_pending',
            'refunded_amount' => $refundAmount,
            'refunded_at' => now(),
        ]);

        return [
            'success' => true,
            'message' => 'Refund request submitted. Amount will be processed within 1-2 business days.',
            'refund_amount' => $refundAmount,
        ];
    }

    public function webhook(array $data): array
    {
        // Handle PromptPay webhooks if available from your payment processor
        return [
            'success' => true,
            'message' => 'Webhook processed',
        ];
    }

    public function getPaymentMethods(): array
    {
        return [
            'promptpay' => [
                'name' => 'PromptPay QR Code',
                'description' => 'Pay instantly using PromptPay QR code with your mobile banking app',
                'icon' => 'heroicon-o-qr-code',
                'currencies' => ['THB'],
            ],
        ];
    }

    public function isAvailable(): bool
    {
        return !empty(config('services.promptpay.id'));
    }

    public function getName(): string
    {
        return 'PromptPay';
    }

    public function getSlug(): string
    {
        return 'promptpay';
    }

    /**
     * Generate PromptPay payload for QR code
     */
    private function generatePromptPayPayload(string $promptPayId, float $amount, string $reference): string
    {
        // This is a simplified PromptPay payload generation
        // In production, you should use a proper PromptPay library
        
        $payload = '';
        
        // Payload Format Indicator
        $payload .= '000201';
        
        // Point of Initiation Method
        $payload .= '010212';
        
        // Merchant Account Information
        $payload .= '29' . sprintf('%02d', strlen($promptPayId) + 26) . '0016A000000677010111';
        $payload .= '01' . sprintf('%02d', strlen($promptPayId)) . $promptPayId;
        
        // Transaction Currency (THB = 764)
        $payload .= '5303764';
        
        // Transaction Amount
        if ($amount > 0) {
            $amountStr = number_format($amount, 2, '.', '');
            $payload .= '54' . sprintf('%02d', strlen($amountStr)) . $amountStr;
        }
        
        // Country Code
        $payload .= '5802TH';
        
        // Additional Data Field Template
        if ($reference) {
            $additionalData = '01' . sprintf('%02d', strlen($reference)) . $reference;
            $payload .= '62' . sprintf('%02d', strlen($additionalData)) . $additionalData;
        }
        
        // CRC (simplified - in production use proper CRC16 calculation)
        $payload .= '6304';
        $crc = $this->calculateCRC16($payload);
        $payload .= strtoupper(dechex($crc));
        
        return $payload;
    }

    /**
     * Calculate CRC16 for PromptPay payload
     */
    private function calculateCRC16(string $data): int
    {
        // Simplified CRC16 calculation
        // In production, use a proper CRC16 implementation
        $crc = 0xFFFF;
        
        for ($i = 0; $i < strlen($data); $i++) {
            $crc ^= ord($data[$i]) << 8;
            for ($j = 0; $j < 8; $j++) {
                if ($crc & 0x8000) {
                    $crc = ($crc << 1) ^ 0x1021;
                } else {
                    $crc = $crc << 1;
                }
                $crc &= 0xFFFF;
            }
        }
        
        return $crc;
    }
}
