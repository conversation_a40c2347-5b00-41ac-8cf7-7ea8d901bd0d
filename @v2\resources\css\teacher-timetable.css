/* Teacher Timetable Specific Styles */

/* Form Input Styles */
.input-field {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    background-color: white;
    font-size: 0.875rem;
}

.input-field:focus {
    border-color: #7c3aed;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
    outline: none;
}

.input-field:hover {
    border-color: #c4b5fd;
}

/* Button Styles */
.btn {
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #6d28d9 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

.btn-outline {
    border: 1px solid #e0e0e0;
    color: #666;
    background-color: white;
}

.btn-outline:hover {
    background-color: #f5f5f5;
    border-color: #d1d5db;
}

/* Tab Styles */
.tab {
    position: relative;
    cursor: pointer;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    color: #666;
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab:hover {
    color: #7c3aed;
    background-color: #faf5ff;
}

.tab-active {
    color: #7c3aed;
    border-bottom-color: #7c3aed;
    background-color: #faf5ff;
}

/* Card Styles */
.card {
    border-radius: 12px;
    border: none;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Media Item Styles */
.media-item {
    transition: all 0.2s ease;
    border-radius: 10px;
    border: 1px solid #f0f0f0;
    cursor: pointer;
}

.media-item:hover {
    border-color: #c4b5fd;
    background-color: #faf5ff;
    transform: scale(1.02);
}

.media-item.selected {
    background-color: #f3e8ff;
    border-color: #7c3aed;
    box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

/* Tag Styles */
.tag {
    border-radius: 20px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tag:hover {
    transform: scale(1.05);
}

/* Modal Styles */
.modal {
    transition: opacity 0.3s ease;
    backdrop-filter: blur(4px);
}

.modal-content {
    border-radius: 16px;
    border: none;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* Material Card Styles */
.material-card {
    transition: all 0.3s ease;
    cursor: pointer;
}

.material-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.material-icon {
    transition: all 0.2s ease;
}

.material-card:hover .material-icon {
    transform: scale(1.1);
}

/* Preview Section */
.preview-section {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
}

/* Form Section */
.form-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Loading States */
.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Success Animation */
.success-bounce {
    animation: successBounce 0.6s ease-in-out;
}

@keyframes successBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide In Animation */
.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #c5c5c5;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #a5a5a5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tab {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .input-field {
        padding: 0.625rem 0.875rem;
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 0.625rem 1rem;
        font-size: 0.875rem;
    }
    
    .material-card {
        padding: 0.75rem;
    }
    
    .modal-content {
        margin: 1rem;
        max-height: 90vh;
        overflow-y: auto;
    }
}

@media (max-width: 640px) {
    .tab {
        padding: 0.375rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .material-card {
        padding: 0.5rem;
    }
    
    .material-card .material-icon {
        width: 2rem;
        height: 2rem;
    }
    
    .tag {
        font-size: 0.625rem;
        padding: 0.125rem 0.5rem;
    }
}

/* Focus States */
.focus-ring:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Error States */
.error-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 60;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.success {
    background-color: #10b981;
    color: white;
}

.notification.error {
    background-color: #ef4444;
    color: white;
}

.notification.info {
    background-color: #3b82f6;
    color: white;
}

/* Form Validation */
.input-error {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-success {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Loading Spinner */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
