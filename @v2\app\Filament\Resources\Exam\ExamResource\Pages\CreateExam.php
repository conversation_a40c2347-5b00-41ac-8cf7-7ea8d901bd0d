<?php

namespace App\Filament\Resources\Exam\ExamResource\Pages;

use App\Filament\Resources\Exam\ExamResource;
use App\Models\Exam\Question;
use App\Models\Exam\Choice;
use Filament\Resources\Pages\CreateRecord;
use Filament\Facades\Filament;

class CreateExam extends CreateRecord
{
    protected static string $resource = ExamResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Automatically set team_id and user_id
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $data['team_id'] = Filament::getTenant()->id;
        }

        $data['user_id'] = auth()->id();

        // Remove questions_data from the main data as it will be handled separately
        unset($data['questions_data']);

        return $data;
    }

    protected function afterCreate(): void
    {
        $questionsData = $this->form->getState()['questions_data'] ?? [];

        foreach ($questionsData as $index => $questionBlock) {
            if ($questionBlock['type'] !== 'question') continue;

            $questionData = $questionBlock['data'];

            $question = Question::create([
                'team_id' => $this->record->team_id,
                'exam_id' => $this->record->id,
                'user_id' => $this->record->user_id,
                'subject_id' => $this->record->subject_id,
                'question_text' => $questionData['question_text'],
                'question_type' => $questionData['question_type'],
                'points' => $questionData['points'],
                'difficulty_level' => $questionData['difficulty_level'],
                'explanation' => $questionData['explanation'] ?? null,
                'tags' => $questionData['tags'] ?? [],
                'sort_order' => $index + 1,
                'is_active' => true,
            ]);

            // Create choices for multiple choice questions
            if ($questionData['question_type'] === 'multiple_choice' && isset($questionData['choices'])) {
                foreach ($questionData['choices'] as $choiceIndex => $choiceData) {
                    Choice::create([
                        'question_id' => $question->id,
                        'choice_text' => $choiceData['choice_text'],
                        'is_correct' => $choiceData['is_correct'] ?? false,
                        'sort_order' => $choiceIndex + 1,
                    ]);
                }
            }
        }
    }
}
