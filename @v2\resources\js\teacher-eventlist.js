// Teacher Event List JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle (inherited from layout)
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeMenu = document.getElementById('close-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('active');
        });
    }

    if (closeMenu && mobileMenu) {
        closeMenu.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
        });
    }

    // Form elements
    const assignmentForm = document.getElementById('assignmentForm');
    const classSelect = document.getElementById('classSelect');
    const subjectSelect = document.getElementById('subjectSelect');
    const bookSelect = document.getElementById('bookSelect');
    const lessonSelect = document.getElementById('lessonSelect');
    const lessonContainer = document.getElementById('lessonSelectContainer');

    // Subject and book data
    const subjectBooks = {
        'ภาษาไทย': [
            'หนังสือเรียนภาษาไทย ป.5',
            'หนังสือเรียนภาษาไทย ป.6',
            'หนังสือเรียนภาษาไทย ม.1',
            'หนังสือเรียนภาษาไทย ม.2'
        ],
        'คณิตศาสตร์': [
            'หนังสือเรียนคณิตศาสตร์ ป.5',
            'หนังสือเรียนคณิตศาสตร์ ป.6',
            'หนังสือเรียนคณิตศาสตร์ ม.1',
            'หนังสือเรียนคณิตศาสตร์ ม.2'
        ],
        'วิทยาศาสตร์': [
            'หนังสือเรียนวิทยาศาสตร์ ป.5',
            'หนังสือเรียนวิทยาศาสตร์ ป.6',
            'หนังสือเรียนวิทยาศาสตร์ ม.1',
            'หนังสือเรียนวิทยาศาสตร์ ม.2'
        ],
        'สังคมศึกษา': [
            'หนังสือเรียนสังคมศึกษา ป.5',
            'หนังสือเรียนสังคมศึกษา ป.6',
            'หนังสือเรียนสังคมศึกษา ม.1',
            'หนังสือเรียนสังคมศึกษา ม.2'
        ],
        'ภาษาอังกฤษ': [
            'หนังสือเรียนภาษาอังกฤษ ป.5',
            'หนังสือเรียนภาษาอังกฤษ ป.6',
            'หนังสือเรียนภาษาอังกฤษ ม.1',
            'หนังสือเรียนภาษาอังกฤษ ม.2'
        ]
    };

    const bookLessons = {
        'หนังสือเรียนคณิตศาสตร์ ป.5': [
            'บทที่ 1 - พื้นฐานการคำนวณ',
            'บทที่ 2 - เศษส่วนและทศนิยม',
            'บทที่ 3 - รูปเรขาคณิต',
            'บทที่ 4 - การวัด',
            'บทที่ 5 - สถิติและความน่าจะเป็น'
        ],
        'หนังสือเรียนภาษาไทย ป.5': [
            'บทที่ 1 - การอ่านและการเขียน',
            'บทที่ 2 - วรรณคดีไทย',
            'บทที่ 3 - ไวยากรณ์',
            'บทที่ 4 - การเขียนเรียงความ'
        ],
        'หนังสือเรียนวิทยาศาสตร์ ป.5': [
            'บทที่ 1 - สิ่งมีชีวิตและสิ่งไม่มีชีวิต',
            'บทที่ 2 - แรงและการเคลื่อนที่',
            'บทที่ 3 - แสงและเสียง',
            'บทที่ 4 - สารและสมบัติ'
        ]
    };

    // Subject selection change handler
    if (subjectSelect && bookSelect) {
        subjectSelect.addEventListener('change', function() {
            const selectedSubject = this.value;
            
            // Clear book options
            bookSelect.innerHTML = '<option value="ไม่ระบุ">ไม่ระบุ</option>';
            
            if (selectedSubject && subjectBooks[selectedSubject]) {
                subjectBooks[selectedSubject].forEach(book => {
                    const option = document.createElement('option');
                    option.value = book;
                    option.textContent = book;
                    bookSelect.appendChild(option);
                });
            }
            
            // Hide lesson container
            if (lessonContainer) {
                lessonContainer.style.display = 'none';
            }
        });
    }

    // Book selection change handler
    if (bookSelect && lessonSelect && lessonContainer) {
        bookSelect.addEventListener('change', function() {
            const selectedBook = this.value;
            
            if (selectedBook !== 'ไม่ระบุ' && bookLessons[selectedBook]) {
                // Clear lesson options
                lessonSelect.innerHTML = '<option value="" disabled selected>เลือกบทเรียน</option>';
                
                // Add lesson options
                bookLessons[selectedBook].forEach(lesson => {
                    const option = document.createElement('option');
                    option.value = lesson;
                    option.textContent = lesson;
                    lessonSelect.appendChild(option);
                });
                
                // Show lesson container
                lessonContainer.style.display = 'block';
            } else {
                // Hide lesson container
                lessonContainer.style.display = 'none';
            }
        });
    }

    // Checkbox animation
    const checkboxItems = document.querySelectorAll('.checkbox-item');
    checkboxItems.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const label = this.nextElementSibling;
            if (this.checked) {
                label.classList.add('success-bounce');
                setTimeout(() => {
                    label.classList.remove('success-bounce');
                }, 600);
            }
        });
    });

    // Form submission
    if (assignmentForm) {
        assignmentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const selectedActivities = [];
            
            checkboxItems.forEach(checkbox => {
                if (checkbox.checked) {
                    const label = checkbox.nextElementSibling;
                    const activityName = label.querySelector('span').textContent;
                    selectedActivities.push(activityName);
                }
            });
            
            // Validate form
            if (selectedActivities.length === 0) {
                showNotification('กรุณาเลือกกิจกรรมอย่างน้อย 1 กิจกรรม', 'error');
                return;
            }
            
            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>กำลังมอบหมาย...';
            submitButton.disabled = true;
            
            // Simulate API call
            setTimeout(() => {
                // Reset button
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
                
                // Show success message
                showNotification('มอบหมายกิจกรรมเรียบร้อยแล้ว', 'success');
                
                // Reset form
                this.reset();
                
                // Hide lesson container
                if (lessonContainer) {
                    lessonContainer.style.display = 'none';
                }
                
                // Uncheck all activities
                checkboxItems.forEach(checkbox => {
                    checkbox.checked = false;
                    const label = checkbox.nextElementSibling;
                    label.classList.remove('bg-indigo-50', 'border-indigo-500');
                });
                
                // Add new activity card to the list
                addNewActivityCard(selectedActivities);
            }, 2000);
        });
    }

    // Activity card button handlers
    const activityCards = document.querySelectorAll('.activity-card');
    activityCards.forEach(card => {
        const buttons = card.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                const action = this.textContent.trim();
                const cardTitle = card.querySelector('h3').textContent;
                
                switch (action) {
                    case 'ดูรายละเอียด':
                        showActivityDetails(cardTitle);
                        break;
                    case 'ปิดกิจกรรม':
                        closeActivity(card);
                        break;
                    case 'เก็บข้อมูล':
                        exportActivityData(cardTitle);
                        break;
                }
            });
        });
    });

    function showActivityDetails(title) {
        // Create modal for activity details
        const modal = document.createElement('div');
        modal.className = 'modal-overlay fixed inset-0 z-50 flex items-center justify-center';
        
        modal.innerHTML = `
            <div class="modal-content bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">${title}</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">จำนวนนักเรียนที่ส่ง</h4>
                            <p class="text-2xl font-bold text-blue-600">22/38 คน</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">คะแนนเฉลี่ย</h4>
                            <p class="text-2xl font-bold text-green-600">78.5 คะแนน</p>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">นักเรียนที่ยังไม่ส่งงาน</h4>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">น.ส.สมใจ ใจดี</span>
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">นายสมชาย ดีใจ</span>
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">น.ส.สมหวัง รักเรียน</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal functionality
        const closeButton = modal.querySelector('.close-modal');
        closeButton.addEventListener('click', function() {
            document.body.removeChild(modal);
        });
        
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    function closeActivity(card) {
        if (confirm('คุณต้องการปิดกิจกรรมนี้หรือไม่?')) {
            card.style.transition = 'all 0.5s ease';
            card.style.transform = 'scale(0.8)';
            card.style.opacity = '0.5';
            
            setTimeout(() => {
                const header = card.querySelector('.bg-indigo-100, .bg-blue-100, .bg-purple-100');
                const statusBadge = card.querySelector('.bg-indigo-600, .bg-blue-600, .bg-purple-600');
                
                if (header) {
                    header.className = header.className.replace(/bg-(indigo|blue|purple)-100/, 'bg-gray-100');
                }
                
                if (statusBadge) {
                    statusBadge.className = statusBadge.className.replace(/bg-(indigo|blue|purple)-600/, 'bg-gray-600');
                    statusBadge.textContent = 'เสร็จสิ้น';
                }
                
                card.style.transform = 'scale(1)';
                card.style.opacity = '1';
                
                showNotification('ปิดกิจกรรมเรียบร้อยแล้ว', 'success');
            }, 500);
        }
    }

    function exportActivityData(title) {
        showNotification('กำลังเตรียมข้อมูลสำหรับดาวน์โหลด...', 'info');
        
        // Simulate data export
        setTimeout(() => {
            showNotification('ดาวน์โหลดข้อมูลเรียบร้อยแล้ว', 'success');
        }, 2000);
    }

    function addNewActivityCard(activities) {
        const container = document.querySelector('.grid.grid-cols-1.md\\:grid-cols-2');
        if (!container) return;
        
        const newCard = document.createElement('div');
        newCard.className = 'activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow card-fade-in';
        
        const currentDate = new Date();
        const dueDate = new Date(currentDate.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days later
        
        newCard.innerHTML = `
            <div class="bg-green-100 px-4 py-3 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="font-bold text-green-800">กิจกรรมใหม่</h3>
                    <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">กำลังดำเนินการ</span>
                </div>
            </div>
            <div class="p-4">
                <div class="flex items-center mb-2">
                    <i class="far fa-calendar-alt text-green-500 mr-2"></i>
                    <span class="text-sm">วันที่สอน: ${currentDate.toLocaleDateString('th-TH')}</span>
                </div>
                <div class="flex items-center mb-3">
                    <i class="far fa-calendar-check text-green-500 mr-2"></i>
                    <span class="text-sm">กำหนดส่ง: ${dueDate.toLocaleDateString('th-TH')} (16:00 น.)</span>
                </div>
                <div class="mb-3">
                    <h4 class="text-sm font-medium mb-2">รายการกิจกรรม:</h4>
                    <div class="flex flex-wrap gap-2">
                        ${activities.map(activity => `<span class="bg-green-50 text-green-700 text-xs px-2 py-1 rounded-full">${activity}</span>`).join('')}
                    </div>
                </div>
                <div class="border-t border-gray-100 pt-3 mb-3">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-sm font-medium">
                            <i class="fas fa-users text-green-500 mr-1"></i>
                            จำนวนนักเรียนในห้อง: 0 คน
                        </span>
                        <span class="text-sm font-medium text-green-600">ส่งแล้ว: 0 คน</span>
                    </div>
                    <div class="progress-bar mb-2">
                        <div class="progress-fill" style="width: 0%; background: linear-gradient(90deg, #10B981 0%, #34D399 100%);"></div>
                    </div>
                </div>
                <div class="flex flex-wrap gap-2">
                    <button class="bg-green-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-green-700 transition-colors flex items-center">
                        <i class="fas fa-eye mr-1"></i>
                        ดูรายละเอียด
                    </button>
                    <button class="bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center">
                        <i class="fas fa-times-circle mr-1"></i>
                        ปิดกิจกรรม
                    </button>
                    <button class="bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center">
                        <i class="fas fa-download mr-1"></i>
                        เก็บข้อมูล
                    </button>
                </div>
            </div>
        `;
        
        container.insertBefore(newCard, container.firstChild);
        
        // Add event listeners to new card buttons
        const buttons = newCard.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                const action = this.textContent.trim();
                const cardTitle = newCard.querySelector('h3').textContent;
                
                switch (action) {
                    case 'ดูรายละเอียด':
                        showActivityDetails(cardTitle);
                        break;
                    case 'ปิดกิจกรรม':
                        closeActivity(newCard);
                        break;
                    case 'เก็บข้อมูล':
                        exportActivityData(cardTitle);
                        break;
                }
            });
        });
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300`;
        
        switch (type) {
            case 'success':
                notification.classList.add('bg-green-500', 'text-white');
                break;
            case 'error':
                notification.classList.add('bg-red-500', 'text-white');
                break;
            case 'warning':
                notification.classList.add('bg-yellow-500', 'text-white');
                break;
            default:
                notification.classList.add('bg-blue-500', 'text-white');
        }
        
        notification.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }
        }, 5000);
    }

    // Add animation classes to existing elements
    const formSection = document.querySelector('.lg\\:col-span-1');
    const cardsSection = document.querySelector('.lg\\:col-span-2');
    
    if (formSection) {
        formSection.classList.add('form-slide-in');
    }
    
    if (cardsSection) {
        cardsSection.classList.add('card-fade-in');
    }
});
