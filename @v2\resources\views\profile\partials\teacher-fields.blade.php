<!-- Teacher-specific Fields -->

<!-- Professional Information -->
<div class="form-section">
    <h2 class="section-title">Professional Information</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="employee_id" class="form-label">Employee ID</label>
            <input type="text" id="employee_id" name="employee_id" class="form-input" 
                   value="{{ old('employee_id', $profile->employee_id) }}" 
                   placeholder="Enter your employee ID">
            @error('employee_id')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="qualification" class="form-label required">Qualification</label>
            <input type="text" id="qualification" name="qualification" class="form-input" 
                   value="{{ old('qualification', $profile->qualification) }}" 
                   placeholder="e.g., Bachelor's in Education, Master's in Mathematics" required>
            @error('qualification')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="years_of_experience" class="form-label required">Years of Experience</label>
            <input type="number" id="years_of_experience" name="years_of_experience" class="form-input" 
                   value="{{ old('years_of_experience', $profile->years_of_experience) }}" 
                   min="0" max="50" placeholder="0" required>
            @error('years_of_experience')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<!-- Teaching Subjects -->
<div class="form-section">
    <h2 class="section-title">Teaching Subjects</h2>
    <div class="form-group">
        <label class="form-label required">Subjects You Teach</label>
        <div class="subjects-container">
            @php
                $subjects = old('subjects', $profile->subjects ?? []);
            @endphp
            
            @if(is_array($subjects) && count($subjects) > 0)
                @foreach($subjects as $subject)
                    <span class="subject-tag">
                        {{ $subject }}
                        <button type="button" class="subject-remove" onclick="removeSubject('{{ $subject }}')">×</button>
                    </span>
                    <input type="hidden" name="subjects[]" value="{{ $subject }}">
                @endforeach
            @endif
            
            <input type="text" class="subject-input" placeholder="Add subject and press Enter">
        </div>
        <div class="mt-2 text-sm text-gray-600">
            <p>Common subjects: Mathematics, English, Science, History, Geography, Physics, Chemistry, Biology, Art, Music, Physical Education</p>
        </div>
        @error('subjects')
            <div class="error-message">{{ $message }}</div>
        @enderror
        @error('subjects.*')
            <div class="error-message">{{ $message }}</div>
        @enderror
    </div>
</div>

<style>
.subjects-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    min-height: 50px;
    align-items: center;
    background: white;
}

.subject-tag {
    background: #4299e1;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.subject-remove {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.subject-remove:hover {
    background: rgba(255, 255, 255, 0.2);
}

.subject-input {
    flex: 1;
    min-width: 200px;
    border: none;
    outline: none;
    padding: 0.5rem;
    font-size: 0.875rem;
    background: transparent;
}

.subject-input::placeholder {
    color: #a0aec0;
}
</style>
