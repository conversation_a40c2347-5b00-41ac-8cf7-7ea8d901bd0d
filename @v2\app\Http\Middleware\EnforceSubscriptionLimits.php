<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnforceSubscriptionLimits
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        if (!$user || !$user->team) {
            return $next($request);
        }

        $team = $user->team;
        
        // Check if team has an active subscription
        $activeSubscription = $team->subscriptions()
            ->where('starts_at', '<=', now())
            ->where(function ($query) {
                $query->whereNull('ends_at')
                      ->orWhere('ends_at', '>', now());
            })
            ->where(function ($query) {
                $query->whereNull('canceled_at')
                      ->orWhere('canceled_at', '>', now());
            })
            ->first();

        if (!$activeSubscription) {
            // No active subscription - allow basic access but show upgrade notice
            session()->flash('subscription_warning', 'Your team does not have an active subscription. Some features may be limited.');
            return $next($request);
        }

        $plan = $activeSubscription->plan;
        
        // Check user limits based on plan
        if ($this->isSchoolPlan($plan->slug)) {
            $this->enforceSchoolLimits($team, $plan);
        } elseif ($this->isTeacherPlan($plan->slug)) {
            $this->enforceTeacherLimits($team, $plan);
        }

        return $next($request);
    }

    private function isSchoolPlan(string $planSlug): bool
    {
        return str_contains($planSlug, 'school');
    }

    private function isTeacherPlan(string $planSlug): bool
    {
        return str_contains($planSlug, 'teacher');
    }

    private function enforceSchoolLimits($team, $plan): void
    {
        $studentCount = $team->users()->whereHas('roles', function ($query) {
            $query->where('name', 'student');
        })->count();

        $parentCount = $team->users()->whereHas('roles', function ($query) {
            $query->where('name', 'parent');
        })->count();

        // Determine limits based on plan
        $studentLimit = $this->getStudentLimit($plan->slug);
        $parentLimit = $studentLimit; // 1 parent per student

        if ($studentCount >= $studentLimit) {
            session()->flash('subscription_limit', "Your school has reached the student limit of {$studentLimit}. Please upgrade your plan to add more students.");
        }

        if ($parentCount >= $parentLimit) {
            session()->flash('subscription_limit', "Your school has reached the parent limit of {$parentLimit}. Please upgrade your plan to add more parents.");
        }
    }

    private function enforceTeacherLimits($team, $plan): void
    {
        $studentCount = $team->users()->whereHas('roles', function ($query) {
            $query->where('name', 'student');
        })->count();

        $studentLimit = 50; // Individual teacher limit

        if ($studentCount >= $studentLimit) {
            session()->flash('subscription_limit', "You have reached the student limit of {$studentLimit}. Please upgrade to a school plan for more students.");
        }
    }

    private function getStudentLimit(string $planSlug): int
    {
        switch ($planSlug) {
            case 'school-basic':
            case 'school-basic-annual':
                return 100;
            case 'school-premium':
                return 500;
            default:
                return 100; // Default school limit
        }
    }
}
