<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG Demo - Development Testing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8" x-data="svgDemo()">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">🎨 SVG Animation Demo</h1>
                        <p class="text-gray-600">Browse and test all available SVG animations and graphics</p>
                    </div>
                    <a href="{{ route('test.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Controls -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" x-model="animationsEnabled" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm font-medium text-gray-700">Enable Animations</span>
                        </label>
                        <button @click="toggleAll()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">
                            <span x-text="allEnabled ? 'Disable All' : 'Enable All'"></span>
                        </button>
                    </div>
                    <div class="text-sm text-gray-500">
                        <span x-text="enabledCount"></span> of <span x-text="svgs.length"></span> enabled
                    </div>
                </div>
            </div>

            <!-- SVG Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <template x-for="svg in svgs" :key="svg.name">
                    <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                        <!-- SVG Preview -->
                        <div class="h-32 flex items-center justify-center mb-4 bg-gray-50 rounded-lg">
                            <div x-show="svg.enabled && animationsEnabled" x-html="svg.content" class="w-16 h-16"></div>
                            <div x-show="!svg.enabled || !animationsEnabled" class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- SVG Info -->
                        <div class="space-y-3">
                            <div>
                                <h3 class="font-semibold text-gray-900" x-text="svg.name"></h3>
                                <p class="text-sm text-gray-500" x-text="svg.description"></p>
                            </div>

                            <!-- Controls -->
                            <div class="flex items-center justify-between">
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="svg.enabled" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-700">Enable</span>
                                </label>
                                <button @click="copyCode(svg)" class="text-blue-600 hover:text-blue-800 text-sm">
                                    Copy Code
                                </button>
                            </div>

                            <!-- Usage Example -->
                            <div class="text-xs bg-gray-50 p-2 rounded font-mono" x-show="svg.enabled">
                                <div class="text-gray-600 mb-1">Usage:</div>
                                <div x-text="`<div class='${svg.cssClass}'></div>`"></div>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Code Modal -->
            <div x-show="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="showModal = false">
                <div class="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">SVG Code</h3>
                        <button @click="showModal = false" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto" x-text="modalContent"></pre>
                    <div class="mt-4 flex justify-end">
                        <button @click="copyToClipboard(modalContent)" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            Copy to Clipboard
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function svgDemo() {
            return {
                animationsEnabled: true,
                showModal: false,
                modalContent: '',
                svgs: [
                    {
                        name: 'Loading Spinner',
                        description: 'Rotating circle loader',
                        enabled: true,
                        cssClass: 'animate-spin',
                        content: `<svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>`
                    },
                    {
                        name: 'Pulse Animation',
                        description: 'Pulsing circle effect',
                        enabled: true,
                        cssClass: 'animate-pulse',
                        content: `<div class="animate-pulse">
                            <div class="h-8 w-8 bg-blue-600 rounded-full"></div>
                        </div>`
                    },
                    {
                        name: 'Bounce Animation',
                        description: 'Bouncing element',
                        enabled: true,
                        cssClass: 'animate-bounce',
                        content: `<div class="animate-bounce">
                            <svg class="h-8 w-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>`
                    },
                    {
                        name: 'Floating Elements',
                        description: 'Floating background elements',
                        enabled: false,
                        cssClass: 'floating-elements',
                        content: `<div class="relative">
                            <div class="absolute animate-pulse opacity-20">
                                <svg class="h-8 w-8 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            </div>
                        </div>`
                    },
                    {
                        name: 'Progress Bar',
                        description: 'Animated progress indicator',
                        enabled: true,
                        cssClass: 'progress-animation',
                        content: `<div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 75%"></div>
                        </div>`
                    },
                    {
                        name: 'Notification Bell',
                        description: 'Animated notification icon',
                        enabled: true,
                        cssClass: 'animate-pulse',
                        content: `<div class="relative">
                            <svg class="h-8 w-8 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"></path>
                            </svg>
                            <div class="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-ping"></div>
                        </div>`
                    }
                ],

                get enabledCount() {
                    return this.svgs.filter(svg => svg.enabled).length;
                },

                get allEnabled() {
                    return this.svgs.every(svg => svg.enabled);
                },

                toggleAll() {
                    const newState = !this.allEnabled;
                    this.svgs.forEach(svg => svg.enabled = newState);
                },

                copyCode(svg) {
                    this.modalContent = svg.content;
                    this.showModal = true;
                },

                copyToClipboard(text) {
                    navigator.clipboard.writeText(text).then(() => {
                        alert('Code copied to clipboard!');
                        this.showModal = false;
                    });
                }
            }
        }
    </script>
</body>
</html>
