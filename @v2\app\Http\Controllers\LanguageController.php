<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\App;

class LanguageController extends Controller
{
    /**
     * Switch the application language
     */
    public function switch(Request $request, string $locale): RedirectResponse
    {
        // Validate the locale
        $supportedLocales = ['en', 'th'];
        
        if (!in_array($locale, $supportedLocales)) {
            abort(400, 'Unsupported locale');
        }

        // Store the locale in session
        Session::put('locale', $locale);
        
        // Set the application locale
        App::setLocale($locale);

        // If user is authenticated, update their profile language preference
        if (auth()->check() && auth()->user()->profile) {
            auth()->user()->profile->update(['language' => $locale]);
        }

        // Redirect back to the previous page
        return redirect()->back()->with('success', __('Language changed successfully'));
    }
}
