<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Subject;
use Illuminate\Http\Request;

class SubjectController extends Controller
{
    public function index()
    {
        $subjects = Subject::where('is_active', true)
            ->withCount(['courses', 'books'])
            ->paginate(12);

        return view('frontend.subjects.index', compact('subjects'));
    }

    public function show(Subject $subject)
    {
        // Check if subject is active
        if (!$subject->is_active) {
            abort(404);
        }

        // Load related courses and books
        $subject->load([
            'courses' => function ($query) {
                $query->where('is_active', true)->limit(6);
            },
            'books' => function ($query) {
                $query->where('is_active', true)->limit(6);
            }
        ]);

        return view('frontend.subjects.show', compact('subject'));
    }
}
