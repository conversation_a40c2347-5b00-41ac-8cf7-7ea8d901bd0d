<?php

namespace App\Filament\Resources\TeachingScheduleResource\Pages;

use App\Filament\Resources\TeachingScheduleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTeachingSchedules extends ListRecords
{
    protected static string $resource = TeachingScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
