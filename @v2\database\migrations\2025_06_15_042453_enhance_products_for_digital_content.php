<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shop_products', function (Blueprint $table) {
            // Change the existing type enum to include physical and digital
            $table->dropColumn('type');
        });

        Schema::table('shop_products', function (Blueprint $table) {
            // Add new product type enum
            $table->enum('product_type', ['physical', 'digital'])->default('physical')->after('cost');

            // Digital product specific fields
            $table->json('digital_file_types')->nullable()->after('product_type'); // Store allowed file types
            $table->integer('download_limit')->nullable()->after('digital_file_types'); // Max downloads per purchase
            $table->integer('download_expiry_days')->nullable()->after('download_limit'); // Days until download expires
            $table->boolean('requires_license_key')->default(false)->after('download_expiry_days');
            $table->text('license_terms')->nullable()->after('requires_license_key');
            $table->decimal('file_size_mb', 8, 2)->nullable()->after('license_terms'); // File size in MB
            $table->string('digital_format')->nullable()->after('file_size_mb'); // PDF, EPUB, etc.
            $table->string('version')->nullable()->after('digital_format'); // Version for digital products
            $table->date('last_updated')->nullable()->after('version'); // When digital content was last updated
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shop_products', function (Blueprint $table) {
            // Remove digital product fields
            $table->dropColumn([
                'product_type', 
                'digital_file_types',
                'download_limit',
                'download_expiry_days',
                'requires_license_key',
                'license_terms',
                'file_size_mb',
                'digital_format',
                'version',
                'last_updated'
            ]);
        });

        Schema::table('shop_products', function (Blueprint $table) {
            // Restore original type column
            $table->enum('type', ['deliverable', 'downloadable'])->nullable()->after('cost');
        });
    }
};
