<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-calendar-days class="w-5 h-5 text-primary-600 mr-2" />
                ปฏิทินการสอน
            </div>
        </x-slot>

        @php
            $calendarData = $this->getCalendarData();
        @endphp

        <div class="flex flex-col md:flex-row gap-6">
            <!-- Calendar -->
            <div class="w-full md:w-2/3">
                <!-- Calendar Header -->
                <div class="flex justify-between items-center mb-4">
                    <button 
                        wire:click="previousMonth"
                        class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200"
                    >
                        <x-heroicon-o-chevron-left class="w-5 h-5" />
                    </button>
                    <h3 class="font-medium text-lg text-gray-900 dark:text-white">{{ $calendarData['month_name'] }}</h3>
                    <button 
                        wire:click="nextMonth"
                        class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200"
                    >
                        <x-heroicon-o-chevron-right class="w-5 h-5" />
                    </button>
                </div>

                <!-- Calendar Grid -->
                <div class="grid grid-cols-7 gap-1">
                    <!-- Day Headers -->
                    @foreach($calendarData['day_names'] as $dayName)
                        <div class="text-center font-medium text-gray-600 dark:text-gray-400 py-2">{{ $dayName }}</div>
                    @endforeach
                    
                    <!-- Calendar Days -->
                    @foreach($calendarData['days'] as $day)
                        <div class="border rounded-md p-1 h-24 overflow-hidden hover:overflow-y-auto transition-all
                            {{ $day['is_current_month'] ? 'text-gray-900 dark:text-white' : 'text-gray-400 dark:text-gray-600' }}
                            {{ $day['is_today'] ? 'bg-primary-50 dark:bg-primary-900/20 font-medium' : 'bg-white dark:bg-gray-800' }}
                            border-gray-200 dark:border-gray-700">
                            
                            <div class="text-sm">{{ $day['date'] }}</div>
                            
                            @foreach($day['events'] as $event)
                                <div class="text-xs px-1 py-0.5 mb-1 rounded truncate
                                    @if($event['color'] === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200
                                    @elseif($event['color'] === 'green') bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                    @elseif($event['color'] === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200
                                    @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200
                                    @endif">
                                    {{ $event['subject'] }} {{ $event['class'] }} ({{ $event['time'] }})
                                </div>
                            @endforeach
                        </div>
                    @endforeach
                </div>
            </div>
            
            <!-- Today's Activities Sidebar -->
            <div class="w-full md:w-1/3 bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                <h3 class="font-medium text-lg mb-3 flex items-center text-gray-900 dark:text-white">
                    <x-heroicon-o-list-bullet class="w-5 h-5 text-primary-600 mr-2" />
                    กิจกรรมวันนี้
                </h3>
                
                <div class="space-y-4">
                    @foreach($this->getTodayActivities() as $activity)
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-3 shadow-sm">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="font-medium text-gray-900 dark:text-white">{{ $activity['subject'] }}</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $activity['class'] }}</p>
                                </div>
                                <span class="text-sm px-2 py-1 rounded
                                    @if($activity['color'] === 'blue') bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200
                                    @elseif($activity['color'] === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200
                                    @endif">
                                    {{ $activity['time'] }}
                                </span>
                            </div>
                            
                            <div class="mt-2">
                                <p class="text-sm text-gray-700 dark:text-gray-300">
                                    <span class="font-medium">หนังสือ:</span> {{ $activity['book'] }}
                                </p>
                                <p class="text-sm text-gray-700 dark:text-gray-300">
                                    <span class="font-medium">บทเรียน:</span> {{ $activity['lesson'] }}
                                </p>
                            </div>
                            
                            <div class="mt-3 flex flex-wrap gap-2">
                                @foreach($activity['resources'] as $resource)
                                    <button class="text-xs px-2 py-1 rounded flex items-center transition-colors
                                        @if($resource['type'] === 'pdf') bg-blue-500 hover:bg-blue-600 text-white
                                        @elseif($resource['type'] === 'worksheet') bg-green-500 hover:bg-green-600 text-white
                                        @elseif($resource['type'] === 'powerpoint') bg-purple-500 hover:bg-purple-600 text-white
                                        @elseif($resource['type'] === 'video') bg-red-500 hover:bg-red-600 text-white
                                        @elseif($resource['type'] === 'homework') bg-yellow-500 hover:bg-yellow-600 text-white
                                        @endif">
                                        @if($resource['type'] === 'pdf')
                                            <x-heroicon-o-document class="w-3 h-3 mr-1" />
                                        @elseif($resource['type'] === 'worksheet')
                                            <x-heroicon-o-document-text class="w-3 h-3 mr-1" />
                                        @elseif($resource['type'] === 'powerpoint')
                                            <x-heroicon-o-presentation-chart-bar class="w-3 h-3 mr-1" />
                                        @elseif($resource['type'] === 'video')
                                            <x-heroicon-o-play class="w-3 h-3 mr-1" />
                                        @elseif($resource['type'] === 'homework')
                                            <x-heroicon-o-plus class="w-3 h-3 mr-1" />
                                        @endif
                                        {{ $resource['name'] }}
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
