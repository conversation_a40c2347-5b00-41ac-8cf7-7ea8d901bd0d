<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('live_videos', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete(); // Teacher/Creator

            // Polymorphic relationship to Course or Book
            // $table->morphs('liveable'); // Creates liveable_id and liveable_type

            $table->unsignedBigInteger('liveable_id')->nullable();
            $table->string('liveable_type')->nullable();

            $table->string('title');
            $table->text('description')->nullable();

            // Live streaming details
            $table->string('stream_key')->unique()->nullable(); // Unique stream key
            $table->string('stream_url')->nullable(); // RTMP or WebRTC URL
            $table->string('watch_url')->nullable(); // URL for viewers
            $table->json('stream_settings')->nullable(); // Additional streaming settings

            // Scheduling
            $table->dateTime('scheduled_start_time')->nullable();
            $table->dateTime('scheduled_end_time')->nullable();
            $table->dateTime('actual_start_time')->nullable();
            $table->dateTime('actual_end_time')->nullable();

            // Status and recording
            $table->enum('status', ['scheduled', 'live', 'ended', 'cancelled'])->default('scheduled');
            $table->boolean('is_recording_enabled')->default(true);
            $table->string('recording_path')->nullable(); // Path to recorded video
            $table->foreignId('recorded_media_id')->nullable()->constrained('media')->nullOnDelete();

            // Access control
            $table->boolean('is_public')->default(false);
            $table->json('allowed_roles')->nullable(); // Roles that can access
            $table->string('access_password')->nullable(); // Optional password protection

            // Viewer statistics
            $table->integer('max_viewers')->default(0);
            $table->integer('total_views')->default(0);
            $table->json('viewer_analytics')->nullable(); // Store viewer data

            // Additional metadata
            $table->json('metadata')->nullable(); // Additional data
            $table->boolean('is_active')->default(true);

            $table->timestamps();

            // Indexes for performance
            $table->index(['team_id', 'status']);
            $table->index(['user_id', 'status']);
            $table->index(['scheduled_start_time', 'status']);
            $table->index(['status', 'is_active']);
        }); 
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('live_videos');
    }
};
