/* Login Page Specific Styles */

/* Main Container */
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

/* Card Styles */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 420px;
    padding: 2.5rem 2rem;
    position: relative;
    z-index: 2;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header */
.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: #718096;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* Social Login Buttons */
.social-login-section {
    margin-bottom: 1.5rem;
}

.social-button {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
}

.social-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.social-button:hover::before {
    left: 100%;
}

.social-button:hover {
    border-color: #cbd5e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.social-button:active {
    transform: translateY(0);
}

/* Google Button */
.social-button.google:hover {
    border-color: #ea4335;
    color: #ea4335;
}

/* Microsoft Button */
.social-button.microsoft:hover {
    border-color: #0078d4;
    color: #0078d4;
}

/* Apple Button */
.social-button.apple:hover {
    border-color: #000000;
    color: #000000;
}

/* Phone Button */
.social-button.phone:hover {
    border-color: #38a169;
    color: #38a169;
}

/* LINE Button */
.social-button.line:hover {
    border-color: #00c300;
    color: #00c300;
}

/* Social Icons */
.social-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

/* Divider */
.divider {
    display: flex;
    align-items: center;
    margin: 1.5rem 0;
    color: #a0aec0;
    font-size: 0.8rem;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e2e8f0;
}

.divider span {
    padding: 0 1rem;
    background: rgba(255, 255, 255, 0.95);
}

/* Form Styles */
.login-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.5rem;
    font-size: 0.8rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-input::placeholder {
    color: #a0aec0;
}

/* Remember Me & Forgot Password */
.form-options {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.8rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remember-me input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #4f46e5;
}

.forgot-password {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-password:hover {
    color: #7c3aed;
    text-decoration: underline;
}

/* Submit Button */
.submit-button {
    width: 100%;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.submit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-button:hover::before {
    left: 100%;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
}

.submit-button:active {
    transform: translateY(0);
}

.submit-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Footer Links */
.login-footer {
    text-align: center;
    color: #718096;
    font-size: 0.8rem;
}

.login-footer a {
    color: #4f46e5;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.login-footer a:hover {
    color: #7c3aed;
    text-decoration: underline;
}

/* Loading State */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Error States */
.form-input.error {
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.error-message {
    color: #e53e3e;
    font-size: 0.7rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Success States */
.form-input.success {
    border-color: #38a169;
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.success-message {
    color: #38a169;
    font-size: 0.7rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Responsive Design */
@media (max-width: 640px) {
    .login-container {
        padding: 1rem;
    }
    
    .login-card {
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }
    
    .login-title {
        font-size: 1.5rem;
    }
    
    .social-button {
        padding: 0.625rem;
        font-size: 0.75rem;
    }
    
    .form-input {
        padding: 0.625rem;
    }
    
    .submit-button {
        padding: 0.625rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .login-card,
    .social-button,
    .submit-button,
    .form-input {
        animation: none;
        transition: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .login-card {
        background: white;
        border: 2px solid #000;
    }
    
    .social-button {
        border-color: #000;
    }
    
    .form-input {
        border-color: #000;
    }
}
