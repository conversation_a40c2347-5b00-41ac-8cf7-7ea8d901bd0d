/* Register Page Specific Styles */

/* Main Container */
.register-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
    overflow: hidden;
}

.register-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Card Styles */
.register-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    max-width: 480px;
    padding: 3rem 2rem;
    position: relative;
    z-index: 2;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header */
.register-header {
    text-align: center;
    margin-bottom: 2rem;
}

.register-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.5rem;
}

.register-subtitle {
    color: #718096;
    font-size: 1rem;
    line-height: 1.5;
}

/* Social Login Buttons */
.social-login-section {
    margin-bottom: 2rem;
}

.social-button {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    color: #4a5568;
    font-weight: 500;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
    margin-bottom: 0.75rem;
    position: relative;
    overflow: hidden;
}

.social-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
}

.social-button:hover::before {
    left: 100%;
}

.social-button:hover {
    border-color: #cbd5e0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.social-button:active {
    transform: translateY(0);
}

/* Google Button */
.social-button.google:hover {
    border-color: #ea4335;
    color: #ea4335;
}

/* Microsoft Button */
.social-button.microsoft:hover {
    border-color: #0078d4;
    color: #0078d4;
}

/* Apple Button */
.social-button.apple:hover {
    border-color: #000000;
    color: #000000;
}

/* Phone Button */
.social-button.phone:hover {
    border-color: #38a169;
    color: #38a169;
}

/* LINE Button */
.social-button.line:hover {
    border-color: #00c300;
    color: #00c300;
}

/* Social Icons */
.social-icon {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

/* Divider */
.divider {
    display: flex;
    align-items: center;
    margin: 2rem 0;
    color: #a0aec0;
    font-size: 0.875rem;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e2e8f0;
}

.divider span {
    padding: 0 1rem;
    background: rgba(255, 255, 255, 0.95);
}

/* Form Styles */
.register-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
    color: #a0aec0;
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    cursor: pointer;
}

/* Submit Button */
.submit-button {
    width: 100%;
    padding: 0.875rem 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.submit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-button:hover::before {
    left: 100%;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.submit-button:active {
    transform: translateY(0);
}

.submit-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Footer Links */
.register-footer {
    text-align: center;
    color: #718096;
    font-size: 0.875rem;
}

.register-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.register-footer a:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* Loading State */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Phone Input Specific */
.phone-input-group {
    display: flex;
    gap: 0.5rem;
}

.country-select {
    width: 120px;
    flex-shrink: 0;
}

.phone-number-input {
    flex: 1;
}

/* Error States */
.form-input.error {
    border-color: #e53e3e;
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.error-message {
    color: #e53e3e;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Success States */
.form-input.success {
    border-color: #38a169;
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.success-message {
    color: #38a169;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Responsive Design */
@media (max-width: 640px) {
    .register-container {
        padding: 1rem;
    }
    
    .register-card {
        padding: 2rem 1.5rem;
        border-radius: 16px;
    }
    
    .register-title {
        font-size: 1.75rem;
    }
    
    .social-button {
        padding: 0.75rem;
        font-size: 0.8rem;
    }
    
    .form-input {
        padding: 0.75rem;
    }
    
    .submit-button {
        padding: 0.75rem;
    }
}

@media (max-width: 480px) {
    .phone-input-group {
        flex-direction: column;
    }
    
    .country-select {
        width: 100%;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .register-card,
    .social-button,
    .submit-button,
    .form-input {
        animation: none;
        transition: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .register-card {
        background: white;
        border: 2px solid #000;
    }
    
    .social-button {
        border-color: #000;
    }
    
    .form-input {
        border-color: #000;
    }
}
