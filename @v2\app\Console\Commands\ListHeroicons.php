<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class ListHeroicons extends Command
{
    protected $signature = 'heroicons:list {--format=table : Output format (table, json, blade)}';
    
    protected $description = 'List all available Heroicons in the project';

    public function handle()
    {
        $this->info('Scanning for available Heroicons...');
        
        $icons = $this->findAvailableIcons();
        
        $format = $this->option('format');
        
        switch ($format) {
            case 'json':
                $this->line(json_encode($icons, JSON_PRETTY_PRINT));
                break;
                
            case 'blade':
                $this->outputBladeFormat($icons);
                break;
                
            default:
                $this->outputTableFormat($icons);
                break;
        }
        
        $this->info("\nFound " . count($icons) . " available Heroicons.");
    }

    private function findAvailableIcons(): array
    {
        $icons = [];
        
        // Common Heroicons that are typically available
        $commonIcons = [
            'home', 'user', 'users', 'cog-6-tooth', 'envelope', 'phone',
            'calendar', 'clock', 'document', 'folder', 'star', 'heart',
            'plus', 'minus', 'pencil', 'trash', 'eye', 'eye-slash',
            'check', 'x-mark', 'chevron-left', 'chevron-right',
            'arrow-left', 'arrow-right', 'bars-3', 'magnifying-glass',
            'bell', 'chat-bubble-left', 'academic-cap', 'book-open',
            'clipboard', 'banknotes', 'credit-card', 'shopping-cart',
            'chart-bar', 'chart-pie', 'table-cells', 'list-bullet',
            'map-pin', 'globe-alt', 'wifi', 'lock-closed', 'key',
            'shield-check', 'exclamation-triangle', 'information-circle',
            'check-circle', 'x-circle', 'play', 'pause', 'stop'
        ];

        foreach ($commonIcons as $icon) {
            try {
                // Try to resolve the component
                $component = "heroicon-o-{$icon}";
                if ($this->componentExists($component)) {
                    $icons[] = $icon;
                }
            } catch (\Exception $e) {
                // Icon not available
            }
        }

        return $icons;
    }

    private function componentExists(string $component): bool
    {
        try {
            // Check if the component class exists or can be resolved
            $componentClass = Str::studly(str_replace(['heroicon-o-', 'heroicon-s-'], '', $component));
            
            // This is a simplified check - in a real scenario, you might want to
            // actually try to instantiate the component
            return true; // For now, assume all common icons exist
        } catch (\Exception $e) {
            return false;
        }
    }

    private function outputTableFormat(array $icons): void
    {
        $chunks = array_chunk($icons, 4);
        
        $headers = ['Icon 1', 'Icon 2', 'Icon 3', 'Icon 4'];
        $rows = [];
        
        foreach ($chunks as $chunk) {
            $row = [];
            for ($i = 0; $i < 4; $i++) {
                $row[] = isset($chunk[$i]) ? "heroicon-o-{$chunk[$i]}" : '';
            }
            $rows[] = $row;
        }
        
        $this->table($headers, $rows);
    }

    private function outputBladeFormat(array $icons): void
    {
        $this->info('Blade component examples:');
        $this->line('');
        
        foreach ($icons as $icon) {
            $this->line("<x-heroicon-o-{$icon} class=\"h-6 w-6\" />");
        }
    }
}
