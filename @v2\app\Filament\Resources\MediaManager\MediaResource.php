<?php

namespace App\Filament\Resources\MediaManager;

use Filament\Facades\Filament;
use TomatoPHP\FilamentMediaManager\Models\Folder;
use App\Filament\Resources\MediaManager\MediaResource\Pages;
use TomatoPHP\FilamentMediaManager\Resources\MediaResource\RelationManagers;
use TomatoPHP\FilamentMediaManager\Models\Media;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MediaResource extends Resource
{

    protected static bool $isScopedToTenant = false;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    public static function getModel(): string
    {
        return config('filament-media-manager.model.media'); // TODO: Change the autogenerated stub
    }

    public static function getPluralLabel(): ?string
    {
        return trans('filament-media-manager::messages.media.title');
    }

    public static function getLabel(): ?string
    {
        return trans('filament-media-manager::messages.media.single'); // TODO: Change the autogenerated stub
    }

    /**
     * @param string|null $breadcrumb
     */
    public static function setBreadcrumb(?string $breadcrumb): void
    {
        self::$breadcrumb = $breadcrumb;
    }

    public static function form(Form $form): Form
    {
        return $form;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                if(request()->has('folder_id') && !empty('folder_id')){
                    $folder = Folder::find(request()->get('folder_id'));
                    if($folder){
                        $query->where('collection_name', $folder->collection);
                    }
                }
            })
            ->emptyState(fn()=>view('filament-media-manager::pages.media'))
            ->content(function () {
                return view('filament-media-manager::pages.media');
            })
            ->columns([
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\ImageColumn::make('image')
                        ->width('250px')
                        ->height('250px')
                        ->square()
                        ->label(trans('filament-media-manager::messages.media.columns.image'))
                        ->default(function (Media $media) {
                            return $media->getUrl();
                        }),
                ]),
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\TextColumn::make('model.name')
                        ->label(trans('filament-media-manager::messages.media.columns.model'))
                        ->searchable(),
                    Tables\Columns\TextColumn::make('collection_name')
                        ->label(trans('filament-media-manager::messages.media.columns.collection_name'))
                        ->badge()
                        ->icon('heroicon-o-folder')
                        ->searchable(),
                ]),
                Tables\Columns\Layout\Stack::make([
                    Tables\Columns\TextColumn::make('name')
                        ->label(trans('filament-media-manager::messages.media.columns.name'))
                        ->searchable(),
                    Tables\Columns\TextColumn::make('file_name')
                        ->label(trans('filament-media-manager::messages.media.columns.file_name'))
                        ->searchable(),
                    Tables\Columns\TextColumn::make('mime_type')
                        ->label(trans('filament-media-manager::messages.media.columns.mime_type'))
                        ->searchable(),
                    Tables\Columns\TextColumn::make('disk')
                        ->label(trans('filament-media-manager::messages.media.columns.disk'))
                        ->searchable(),
                    Tables\Columns\TextColumn::make('conversions_disk')
                        ->label(trans('filament-media-manager::messages.media.columns.conversions_disk'))
                        ->searchable(),
                    Tables\Columns\TextColumn::make('size')
                        ->label(trans('filament-media-manager::messages.media.columns.size'))
                        ->numeric()
                        ->sortable(),
                    Tables\Columns\TextColumn::make('order_column')
                        ->label(trans('filament-media-manager::messages.media.columns.order_column'))
                        ->numeric()
                        ->sortable(),
                    Tables\Columns\TextColumn::make('created_at')
                        ->dateTime()
                        ->sortable()
                        ->toggleable(isToggledHiddenByDefault: true),
                    Tables\Columns\TextColumn::make('updated_at')
                        ->dateTime()
                        ->sortable()
                        ->toggleable(isToggledHiddenByDefault: true),
                ])
            ])
            ->contentGrid([
                'md' => 3,
                'xl' => 4,
            ])
            ->defaultSort('order_column', 'asc')
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->defaultPaginationPageOption(12)
            ->paginationPageOptions([
                "12",
                "24",
                "48",
                "96",
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMedia::route('/')
        ];
    }
}
