<?php

namespace App\Console\Commands;

use App\Models\Team;
use App\Models\MediaManager\Folder;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CreateTeamFolders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'media:create-team-folders {--force : Force recreation of existing folders}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create physical and database folders for all teams';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        
        $teams = Team::all();
        
        if ($teams->isEmpty()) {
            $this->info('No teams found.');
            return;
        }

        $this->info("Creating folders for {$teams->count()} teams...");

        foreach ($teams as $team) {
            $this->createTeamFolders($team, $force);
        }

        $this->info('Team folders creation completed!');
    }

    /**
     * Create folders for a specific team.
     */
    protected function createTeamFolders(Team $team, bool $force = false)
    {
        $this->line("Processing team: {$team->name} (ID: {$team->id})");

        // Create physical directory for the team
        $teamPath = "teams/{$team->id}";
        
        if (!Storage::disk('public')->exists($teamPath)) {
            Storage::disk('public')->makeDirectory($teamPath);
            $this->info("  ✓ Created physical directory: {$teamPath}");
        } else {
            $this->comment("  - Physical directory already exists: {$teamPath}");
        }

        // Check if main folder already exists
        $mainFolder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
            ->where('team_id', $team->id)
            ->where('parent_id', null)
            ->where('is_personal', false)
            ->first();

        if ($mainFolder && !$force) {
            $this->comment("  - Database folder already exists for team: {$team->name}");
            return;
        }

        if ($mainFolder && $force) {
            $this->warn("  ! Deleting existing folders for team: {$team->name}");
            Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                ->where('team_id', $team->id)
                ->delete();
        }

        // Create main team folder in database
        $mainFolder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->create([
            'name' => $team->name,
            'collection' => "team_{$team->id}_main",
            'description' => "Main folder for {$team->name}",
            'icon' => 'heroicon-o-building-office',
            'color' => '#3B82F6',
            'is_personal' => false,
            'is_public' => false,
            'team_id' => $team->id,
            'created_by' => 1, // Assume admin user ID 1
            'owner_id' => 1,
            'is_hidden' => false,
            'is_favorite' => false,
            'has_user_access' => false,
        ]);

        $this->info("  ✓ Created main folder: {$team->name}");

        // Create subdirectories for different content types
        $subfolders = [
            [
                'name' => 'Products',
                'collection' => "team_{$team->id}_products",
                'description' => 'Product images and files',
                'icon' => 'heroicon-o-shopping-bag',
                'color' => '#10B981',
            ],
            [
                'name' => 'Avatars',
                'collection' => "team_{$team->id}_avatars",
                'description' => 'User profile images',
                'icon' => 'heroicon-o-user-circle',
                'color' => '#8B5CF6',
            ],
            [
                'name' => 'Documents',
                'collection' => "team_{$team->id}_documents",
                'description' => 'General documents and files',
                'icon' => 'heroicon-o-document',
                'color' => '#F59E0B',
            ],
            [
                'name' => 'Media',
                'collection' => "team_{$team->id}_media",
                'description' => 'General media files',
                'icon' => 'heroicon-o-photo',
                'color' => '#EF4444',
            ],
        ];

        foreach ($subfolders as $subfolder) {
            // Create physical subdirectory
            $subPath = "{$teamPath}/{$subfolder['name']}";
            if (!Storage::disk('public')->exists($subPath)) {
                Storage::disk('public')->makeDirectory($subPath);
                $this->info("    ✓ Created subdirectory: {$subfolder['name']}");
            }
            
            // Create database folder record
            Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->create([
                'name' => $subfolder['name'],
                'collection' => $subfolder['collection'],
                'description' => $subfolder['description'],
                'icon' => $subfolder['icon'],
                'color' => $subfolder['color'],
                'is_personal' => false,
                'is_public' => false,
                'team_id' => $team->id,
                'parent_id' => $mainFolder->id,
                'created_by' => 1,
                'owner_id' => 1,
                'is_hidden' => false,
                'is_favorite' => false,
                'has_user_access' => false,
            ]);

            $this->info("    ✓ Created subfolder: {$subfolder['name']}");
        }
    }
}
