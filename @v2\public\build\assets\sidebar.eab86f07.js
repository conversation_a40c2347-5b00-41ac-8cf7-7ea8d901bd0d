document.addEventListener("DOMContentLoaded",function(){const e=document.querySelector(".fi-sidebar"),s=document.querySelector(".fi-sidebar-nav");if(!e||!s)return;let t=localStorage.getItem("sidebar-collapsed")==="true";t&&e.classList.add("fi-sidebar-collapsed");function a(){t=!t,e.classList.toggle("fi-sidebar-collapsed",t),localStorage.setItem("sidebar-collapsed",t)}const o=document.createElement("button");o.innerHTML=`
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    `,o.className="fi-sidebar-toggle-btn p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",o.onclick=a;const r=e.querySelector(".fi-sidebar-header")||s;r&&r.insertBefore(o,r.firstChild),e.addEventListener("mouseenter",function(){t&&e.classList.add("fi-sidebar-hover-expanded")}),e.addEventListener("mouseleave",function(){e.classList.remove("fi-sidebar-hover-expanded")})});
