document.addEventListener("DOMContentLoaded",function(){m(),u(),h(),f()});function m(){document.querySelectorAll(".particle").forEach((e,a)=>{const r=Math.random()*5,o=6+Math.random()*4,s=4+Math.random()*8;e.style.animationDelay=`${r}s`,e.style.animationDuration=`${o}s`,e.style.width=`${s}px`,e.style.height=`${s}px`;const l=["#06b6d4","#3b82f6","#8b5cf6","#ec4899","#10b981"],c=l[Math.floor(Math.random()*l.length)];e.style.background=`${c}33`}),document.querySelectorAll('[data-type="orbital-system"]').forEach(e=>{e.addEventListener("mouseenter",()=>{e.style.animationPlayState="paused"}),e.addEventListener("mouseleave",()=>{e.style.animationPlayState="running"})}),document.querySelectorAll(".pulse-glow-svg").forEach(e=>{e.addEventListener("click",()=>{e.style.animation="none",setTimeout(()=>{e.style.animation="pulseGlowSvg 2s ease-in-out infinite"},100)})})}function u(){const i={threshold:.1,rootMargin:"0px 0px -50px 0px"},t=new IntersectionObserver(e=>{e.forEach(a=>{if(a.isIntersecting){const r=a.target;r.classList.contains("fade-on-scroll")&&r.classList.add("fade-in"),r.querySelector(".draw-svg")&&r.querySelectorAll(".draw-svg").forEach((s,l)=>{setTimeout(()=>{s.style.animation="drawPath 3s ease-in-out forwards"},l*200)}),r.querySelector(".morph-shape")&&r.querySelectorAll(".morph-shape").forEach(s=>{s.style.animation="morphShape 4s ease-in-out infinite"})}})},i);document.querySelectorAll('.fade-on-scroll, [class*="animated-svg"]').forEach(e=>t.observe(e))}function h(){let i=0,t=0;document.addEventListener("mousemove",a=>{i=a.clientX,t=a.clientY,document.querySelectorAll(".magnetic-field").forEach(o=>{const s=o.getBoundingClientRect(),l=s.left+s.width/2,c=s.top+s.height/2,d=(i-l)*.02,y=(t-c)*.02;o.style.transform=`translate(${d}px, ${y}px)`})}),document.querySelectorAll(".hover-scale, .hover-lift").forEach(a=>{a.addEventListener("mouseenter",()=>{a.style.transform="scale(1.05) translateY(-2px)",a.style.transition="transform 0.3s ease"}),a.addEventListener("mouseleave",()=>{a.style.transform="scale(1) translateY(0)"})}),document.querySelectorAll("button, .btn-primary, .btn-secondary").forEach(a=>{a.addEventListener("click",function(r){const o=document.createElement("span"),s=this.getBoundingClientRect(),l=Math.max(s.width,s.height),c=r.clientX-s.left-l/2,d=r.clientY-s.top-l/2;o.style.width=o.style.height=l+"px",o.style.left=c+"px",o.style.top=d+"px",o.classList.add("ripple"),this.appendChild(o),setTimeout(()=>{o.remove()},600)})})}function f(){document.addEventListener("visibilitychange",()=>{const i=document.querySelectorAll('[class*="animation"], [class*="spin"], [class*="pulse"]');document.hidden?i.forEach(t=>{t.style.animationPlayState="paused"}):i.forEach(t=>{t.style.animationPlayState="running"})}),navigator.hardwareConcurrency&&navigator.hardwareConcurrency<4&&document.querySelectorAll(".kaleidoscope-svg, .dna-helix, .spiral-svg").forEach(t=>{t.style.animationDuration="8s"}),window.matchMedia("(prefers-reduced-motion: reduce)").matches&&document.querySelectorAll('[class*="animation"], [class*="spin"], [class*="pulse"]').forEach(t=>{t.style.animation="none"})}function g(i,t={}){const n=document.createElement("div");n.className="particle";const e=t.size||4+Math.random()*8,a=t.color||"#06b6d4",r=t.delay||Math.random()*5,o=t.duration||6+Math.random()*4;return n.style.width=`${e}px`,n.style.height=`${e}px`,n.style.background=`${a}33`,n.style.left=`${Math.random()*100}%`,n.style.animationDelay=`${r}s`,n.style.animationDuration=`${o}s`,i.appendChild(n),n}function E(i,t={}){const n=document.createElementNS("http://www.w3.org/2000/svg","svg");n.setAttribute("class","pulse-glow-svg"),n.setAttribute("viewBox","0 0 100 100"),n.style.width=t.size||"64px",n.style.height=t.size||"64px";const e=document.createElementNS("http://www.w3.org/2000/svg","circle");return e.setAttribute("cx","50"),e.setAttribute("cy","50"),e.setAttribute("r","30"),e.setAttribute("fill",t.color||"#06b6d4"),e.setAttribute("opacity","0.7"),n.appendChild(e),i.appendChild(n),n}window.AnimatedSVG={createFloatingParticle:g,createPulsingOrb:E,initializeAnimatedSVGs:m,initializeScrollAnimations:u,initializeMouseInteractions:h};const v=`
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
`,p=document.createElement("style");p.textContent=v;document.head.appendChild(p);
