document.addEventListener("DOMContentLoaded",function(){const i=document.getElementById("menu-toggle"),c=document.getElementById("mobile-menu"),m=document.getElementById("close-menu");i&&c&&i.addEventListener("click",function(){c.classList.add("active")}),m&&c&&m.addEventListener("click",function(){c.classList.remove("active")}),document.addEventListener("click",function(e){c&&c.classList.contains("active")&&!c.contains(e.target)&&!i.contains(e.target)&&c.classList.remove("active")});const p=document.getElementById("current-month"),h=document.getElementById("prev-month"),v=document.getElementById("next-month"),r=document.getElementById("calendar-days");let a=new Date;const g=["\u0E21\u0E01\u0E23\u0E32\u0E04\u0E21","\u0E01\u0E38\u0E21\u0E20\u0E32\u0E1E\u0E31\u0E19\u0E18\u0E4C","\u0E21\u0E35\u0E19\u0E32\u0E04\u0E21","\u0E40\u0E21\u0E29\u0E32\u0E22\u0E19","\u0E1E\u0E24\u0E29\u0E20\u0E32\u0E04\u0E21","\u0E21\u0E34\u0E16\u0E38\u0E19\u0E32\u0E22\u0E19","\u0E01\u0E23\u0E01\u0E0E\u0E32\u0E04\u0E21","\u0E2A\u0E34\u0E07\u0E2B\u0E32\u0E04\u0E21","\u0E01\u0E31\u0E19\u0E22\u0E32\u0E22\u0E19","\u0E15\u0E38\u0E25\u0E32\u0E04\u0E21","\u0E1E\u0E24\u0E28\u0E08\u0E34\u0E01\u0E32\u0E22\u0E19","\u0E18\u0E31\u0E19\u0E27\u0E32\u0E04\u0E21"];function d(){const e=a.getFullYear(),t=a.getMonth();p.textContent=`${g[t]} ${e+543}`,r.innerHTML="";const n=new Date(e,t,1).getDay(),o=new Date(e,t+1,0).getDate();for(let s=0;s<n;s++){const l=document.createElement("div");r.appendChild(l)}for(let s=1;s<=o;s++){const l=document.createElement("div");l.className="calendar-day",l.textContent=s;const u=new Date;e===u.getFullYear()&&t===u.getMonth()&&s===u.getDate()&&l.classList.add("active"),[7,15,22,28].includes(s)&&l.classList.add("has-event"),l.addEventListener("click",function(){document.querySelectorAll(".calendar-day").forEach(x=>x.classList.remove("active")),this.classList.add("active"),f(s)}),r.appendChild(l)}}function f(e){const t=document.getElementById("event-list"),o={7:[{type:"live-class",title:"Live Class: \u0E04\u0E13\u0E34\u0E15\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C - \u0E40\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E40\u0E28\u0E29\u0E2A\u0E48\u0E27\u0E19",time:"17:00 - 18:00 \u0E19.",color:"blue"},{type:"activity-box",title:"\u0E01\u0E25\u0E48\u0E2D\u0E07\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21: \u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C - \u0E01\u0E32\u0E23\u0E17\u0E14\u0E25\u0E2D\u0E07\u0E40\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E41\u0E2A\u0E07",time:"\u0E08\u0E31\u0E14\u0E2A\u0E48\u0E07\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49",color:"green"}],15:[{type:"exam",title:"\u0E2A\u0E2D\u0E1A\u0E01\u0E25\u0E32\u0E07\u0E20\u0E32\u0E04: \u0E20\u0E32\u0E29\u0E32\u0E44\u0E17\u0E22",time:"09:00 - 11:00 \u0E19.",color:"red"}],22:[{type:"workshop",title:"Workshop: \u0E01\u0E32\u0E23\u0E40\u0E02\u0E35\u0E22\u0E19\u0E42\u0E1B\u0E23\u0E41\u0E01\u0E23\u0E21",time:"14:00 - 16:00 \u0E19.",color:"purple"}],28:[{type:"field-trip",title:"\u0E17\u0E31\u0E28\u0E19\u0E28\u0E36\u0E01\u0E29\u0E32: \u0E1E\u0E34\u0E1E\u0E34\u0E18\u0E20\u0E31\u0E13\u0E11\u0E4C\u0E27\u0E34\u0E17\u0E22\u0E32\u0E28\u0E32\u0E2A\u0E15\u0E23\u0E4C",time:"08:00 - 17:00 \u0E19.",color:"orange"}]}[e]||[];if(o.length===0){t.innerHTML='<p class="text-gray-500 text-center py-4">\u0E44\u0E21\u0E48\u0E21\u0E35\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21\u0E43\u0E19\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49</p>';return}t.innerHTML=o.map(s=>`
            <div class="flex items-start p-3 rounded-lg bg-${s.color}-50 mb-2">
                <div class="bg-${s.color}-100 p-2 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-${s.color}-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <h5 class="font-medium text-${s.color}-800">${s.title}</h5>
                    <p class="text-sm text-${s.color}-600">${s.time}</p>
                </div>
            </div>
        `).join("")}h&&h.addEventListener("click",function(){a.setMonth(a.getMonth()-1),d()}),v&&v.addEventListener("click",function(){a.setMonth(a.getMonth()+1),d()}),d(),document.querySelectorAll(".subject-card").forEach(e=>{e.addEventListener("click",function(){const t=this.querySelector("h4").textContent;y(t)})});function y(e){const t=document.createElement("div");t.className="modal",t.style.display="flex",t.innerHTML=`
            <div class="modal-content">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">${e}</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">\u0E04\u0E27\u0E32\u0E21\u0E01\u0E49\u0E32\u0E27\u0E2B\u0E19\u0E49\u0E32\u0E25\u0E48\u0E32\u0E2A\u0E38\u0E14</h4>
                        <div class="progress-bar mb-2">
                            <div class="progress-fill bg-blue-500" style="width: 75%"></div>
                        </div>
                        <p class="text-sm text-gray-600">75% \u0E02\u0E2D\u0E07\u0E41\u0E1A\u0E1A\u0E1D\u0E36\u0E01\u0E2B\u0E31\u0E14\u0E17\u0E31\u0E49\u0E07\u0E2B\u0E21\u0E14</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">\u0E04\u0E30\u0E41\u0E19\u0E19\u0E40\u0E09\u0E25\u0E35\u0E48\u0E22</h4>
                        <p class="text-2xl font-bold text-green-600">92/100</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">\u0E41\u0E1A\u0E1A\u0E1D\u0E36\u0E01\u0E2B\u0E31\u0E14\u0E17\u0E35\u0E48\u0E41\u0E19\u0E30\u0E19\u0E33</h4>
                        <ul class="space-y-2">
                            <li class="flex justify-between items-center">
                                <span>\u0E1A\u0E17\u0E17\u0E35\u0E48 5: \u0E40\u0E28\u0E29\u0E2A\u0E48\u0E27\u0E19</span>
                                <button class="text-blue-600 hover:text-blue-800">\u0E40\u0E23\u0E34\u0E48\u0E21\u0E17\u0E33</button>
                            </li>
                            <li class="flex justify-between items-center">
                                <span>\u0E1A\u0E17\u0E17\u0E35\u0E48 6: \u0E17\u0E28\u0E19\u0E34\u0E22\u0E21</span>
                                <button class="text-blue-600 hover:text-blue-800">\u0E40\u0E23\u0E34\u0E48\u0E21\u0E17\u0E33</button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        `,document.body.appendChild(t),t.querySelector(".close-modal").addEventListener("click",function(){document.body.removeChild(t)}),t.addEventListener("click",function(o){o.target===t&&document.body.removeChild(t)})}document.querySelectorAll(".action-button").forEach(e=>{e.addEventListener("click",function(t){t.stopPropagation();const n=this.textContent.trim(),o=this.innerHTML;this.innerHTML='<svg class="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>\u0E01\u0E33\u0E25\u0E31\u0E07\u0E42\u0E2B\u0E25\u0E14...',this.disabled=!0,setTimeout(()=>{this.innerHTML=o,this.disabled=!1,b(`${n} \u0E2A\u0E33\u0E40\u0E23\u0E47\u0E08!`,"success")},1500)})});function b(e,t="info"){const n=document.createElement("div");switch(n.className="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full",t){case"success":n.classList.add("bg-green-500","text-white");break;case"error":n.classList.add("bg-red-500","text-white");break;default:n.classList.add("bg-blue-500","text-white")}n.innerHTML=`
            <div class="flex items-center">
                <span>${e}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `,document.body.appendChild(n),setTimeout(()=>{n.classList.remove("translate-x-full")},100),setTimeout(()=>{n.classList.add("translate-x-full"),setTimeout(()=>{n.parentElement&&n.remove()},300)},5e3)}document.querySelectorAll(".progress").forEach(e=>{const t=parseInt(e.parentElement.querySelector(".percentage").textContent),n=2*Math.PI*45,o=n-t/100*n;setTimeout(()=>{e.style.strokeDashoffset=o},500)}),document.querySelectorAll(".card").forEach((e,t)=>{e.classList.add("fade-in"),e.style.animationDelay=`${t*.1}s`})});
