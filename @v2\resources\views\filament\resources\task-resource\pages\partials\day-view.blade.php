@php
    $selectedDay = now()->parse($selectedDate);
    $hours = range(6, 22); // 6 AM to 10 PM
    $currentHour = now()->hour;
    $isToday = $selectedDay->isToday();
@endphp

<div class="max-w-6xl mx-auto">
    <!-- Day Header -->
    <div class="bg-gray-50 dark:bg-gray-700 p-6 text-center border-b">
        <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 {{ $isToday ? 'text-blue-600 dark:text-blue-400' : '' }}">
            {{ $selectedDay->format('l, F j, Y') }}
            @if($isToday)
                <span class="text-sm font-normal text-blue-500 ml-2">(Today)</span>
            @endif
        </h2>
    </div>

    <!-- Time Slots -->
    <div class="divide-y divide-gray-200 dark:divide-gray-600">
        @foreach($hours as $hour)
            @php
                $cellDateTime = $selectedDay->copy()->setHour($hour)->setMinute(0)->setSecond(0);
                $cellDateTimeString = $cellDateTime->format('Y-m-d H:i:s');
                $isCurrentHour = $isToday && $hour === $currentHour;

                // Get tasks for this time slot and sort by start time
                $cellTasks = $tasks->filter(function($task) use ($cellDateTime) {
                    return $task->start_datetime->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_datetime');

                // Get teaching schedules for this time slot and sort by start time
                $cellSchedules = $teachingSchedules->filter(function($schedule) use ($cellDateTime) {
                    return $schedule->start_time->format('Y-m-d H') === $cellDateTime->format('Y-m-d H');
                })->sortBy('start_time');
            @endphp

            <div class="flex day-view-hour {{ $isCurrentHour ? 'bg-blue-50 dark:bg-blue-900' : '' }}">
                <!-- Time Label -->
                <div class="w-24 flex-shrink-0 bg-gray-50 dark:bg-gray-700 p-4 text-center border-r">
                    <span class="text-sm font-medium {{ $isCurrentHour ? 'text-blue-600 dark:text-blue-400 font-bold' : 'text-gray-900 dark:text-gray-100' }}">
                        {{ sprintf('%02d:00', $hour) }}
                    </span>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {{ $cellDateTime->format('A') }}
                    </div>
                </div>

                <!-- Content Area -->
                <div class="flex-1 p-4 drop-zone min-h-[100px] relative" data-datetime="{{ $cellDateTimeString }}">

                    <!-- Current time indicator -->
                    @if($isCurrentHour)
                        <div class="current-time-indicator" style="top: {{ (now()->minute / 60) * 100 }}%"></div>
                    @endif
                    <div class="space-y-3">
                        <!-- Tasks -->
                        @foreach($cellTasks as $task)
                            <div
                                class="draggable-item p-4 rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                style="border-left-color: {{ $task->priority_color }}; background-color: {{ $task->color }}10"
                                draggable="true"
                                data-id="{{ $task->id }}"
                                data-type="task"
                                onclick="showTaskDetails({{ $task->id }}, 'task')"
                            >
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-2">
                                            <h4 class="font-semibold text-gray-900 dark:text-gray-100">
                                                {{ $task->title }}
                                            </h4>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full {{ $task->priority === 'urgent' ? 'bg-red-100 text-red-800' : ($task->priority === 'high' ? 'bg-orange-100 text-orange-800' : ($task->priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800')) }}">
                                                {{ ucfirst($task->priority) }}
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                            <span class="font-mono font-medium">{{ $task->time_range }}</span>
                                            @if($task->location)
                                                <span class="ml-2">📍 {{ $task->location }}</span>
                                            @endif
                                        </p>
                                        @if($task->description)
                                            <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                                                {{ Str::limit($task->description, 120) }}
                                            </p>
                                        @endif
                                        @if($task->assignedUser)
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                👤 Assigned to: {{ $task->assignedUser->name }}
                                            </p>
                                        @endif
                                        @if($task->user && $task->assigned_to && $task->user_id !== $task->assigned_to)
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                📝 By: {{ $task->user->name }}
                                            </p>
                                        @endif
                                    </div>
                                    <div class="flex flex-col items-center gap-2 ml-4">
                                        @if($task->has_alert)
                                            <x-heroicon-o-bell class="w-4 h-4 text-blue-500" title="Has Alert" />
                                        @endif
                                        @if($task->is_recurring)
                                            <x-heroicon-o-arrow-path class="w-4 h-4 text-purple-500" title="Recurring" />
                                        @endif
                                        <span class="px-2 py-1 text-xs rounded-full {{ $task->status === 'completed' ? 'bg-green-100 text-green-800' : ($task->status === 'in_progress' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                                            {{ ucfirst(str_replace('_', ' ', $task->status)) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        <!-- Teaching Schedules -->
                        @foreach($cellSchedules as $schedule)
                            <div
                                class="draggable-item p-4 rounded-lg border-l-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
                                style="border-left-color: #10B981; background-color: #10B98115"
                                draggable="true"
                                data-id="{{ $schedule->id }}"
                                data-type="schedule"
                                onclick="showTaskDetails({{ $schedule->id }}, 'schedule')"
                            >
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-2">
                                            <h4 class="font-semibold text-gray-900 dark:text-gray-100">
                                                📚 {{ $schedule->subject->name }}
                                            </h4>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                                Class
                                            </span>
                                        </div>
                                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                            <span class="font-mono font-medium">{{ $schedule->time_range }}</span>
                                        </p>
                                        <div class="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                                            <span>🏫 {{ $schedule->classroom->room_name }}</span>
                                            <span>👨‍🏫 {{ $schedule->teacher->name }}</span>
                                        </div>
                                        @if($schedule->lesson)
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                Lesson: {{ $schedule->lesson->title }}
                                            </p>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                                            {{ ucfirst($schedule->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach

                        <!-- Empty state -->
                        @if($cellTasks->isEmpty() && $cellSchedules->isEmpty())
                            <div class="h-20 flex items-center justify-center text-gray-400 dark:text-gray-600 border-2 border-dashed border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 cursor-pointer group relative"
                                 {{-- onclick="createTaskAtTime('{{ $cellDateTimeString }}')" --}}
                                 onclick="showCreateOptions('{{ $cellDateTimeString }}')"
                                 title="Left click: Add Task | Right click: More options">
                                <div class="text-center">
                                    <span class="text-sm group-hover:font-medium">Drop tasks here</span>
                                    <div class="text-xs mt-1 group-hover:font-medium">or click to add new</div>
                                </div>
                                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <span class="text-xs">⋮</span>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>
</div>
