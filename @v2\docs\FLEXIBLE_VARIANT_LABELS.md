# Flexible Variant Labels Guide

## Overview

The product variants system has been updated to use generic option1, option2, option3 fields in the database while allowing teams to set their own custom labels for display. This provides maximum flexibility for different product types and team preferences.

## Changes Made

### 1. Database Structure

#### Before (Fixed Labels):
```json
{
    "color": "Black",
    "size": "L",
    "sku_suffix": "-BLK-L",
    "price_adjustment": 0.00,
    "stock_quantity": 25,
    "is_available": true
}
```

#### After (Generic Options):
```json
{
    "option1": "Black",
    "option2": "L", 
    "option3": "",
    "sku_suffix": "-BLK-L",
    "price_adjustment": 0.00,
    "stock_quantity": 25,
    "is_available": true
}
```

### 2. Custom Label Fields

#### New Database Columns:
- `variant_option1_label` - Team-defined label for option1 (e.g., "Color", "Material", "Style")
- `variant_option2_label` - Team-defined label for option2 (e.g., "Size", "Capacity", "Version")
- `variant_option3_label` - Team-defined label for option3 (e.g., "Finish", "Grade", "Type")

### 3. Form Interface

#### Label Configuration:
```php
Forms\Components\Group::make()
    ->schema([
        Forms\Components\TextInput::make('variant_option1_label')
            ->label('Option 1 Label')
            ->placeholder('e.g., Color, Material, Style')
            ->helperText('What does Option 1 represent? (e.g., Color)'),

        Forms\Components\TextInput::make('variant_option2_label')
            ->label('Option 2 Label')
            ->placeholder('e.g., Size, Capacity, Version')
            ->helperText('What does Option 2 represent? (e.g., Size)'),

        Forms\Components\TextInput::make('variant_option3_label')
            ->label('Option 3 Label (Optional)')
            ->placeholder('e.g., Finish, Grade, Type')
            ->helperText('Optional third variant option'),
    ])
```

#### Dynamic Variant Form:
```php
Forms\Components\TextInput::make('option1')
    ->label(function (Forms\Get $get) {
        return $get('../../variant_option1_label') ?: 'Option 1';
    })
    ->required()
    ->placeholder('e.g., Red, Blue, Black'),

Forms\Components\TextInput::make('option2')
    ->label(function (Forms\Get $get) {
        return $get('../../variant_option2_label') ?: 'Option 2';
    })
    ->required()
    ->placeholder('e.g., S, M, L, XL'),
```

## Benefits

### 1. Team Flexibility

#### Custom Terminology:
- **Fashion Team**: Color, Size, Material
- **Electronics Team**: Model, Storage, Color
- **Furniture Team**: Finish, Size, Style
- **Software Team**: Version, License Type, Platform

#### Industry-Specific Labels:
- **Automotive**: Color, Engine, Trim
- **Food & Beverage**: Flavor, Size, Package Type
- **Books**: Format, Language, Edition
- **Cosmetics**: Shade, Size, Finish

### 2. Database Consistency

#### Standardized Structure:
- All products use the same option1, option2, option3 fields
- No schema changes needed for different product types
- Consistent querying and filtering across all variants

#### Scalable Design:
- Add new product categories without database changes
- Teams can adapt labels to their specific needs
- Maintains data integrity across different use cases

## Examples

### 1. Clothing Store

#### Label Configuration:
- **Option 1 Label**: "Color"
- **Option 2 Label**: "Size"
- **Option 3 Label**: null

#### Sample Variants:
```json
[
    {
        "option1": "Black",
        "option2": "M",
        "option3": "",
        "sku_suffix": "-BLK-M",
        "price_adjustment": 0.00,
        "stock_quantity": 25,
        "is_available": true
    },
    {
        "option1": "Red",
        "option2": "XXL",
        "option3": "",
        "sku_suffix": "-RED-XXL",
        "price_adjustment": 5.00,
        "stock_quantity": 10,
        "is_available": true
    }
]
```

#### Form Display:
- **Color**: Black, Red, Blue
- **Size**: S, M, L, XL, XXL

### 2. Electronics Store

#### Label Configuration:
- **Option 1 Label**: "Model"
- **Option 2 Label**: "Storage"
- **Option 3 Label**: "Color"

#### Sample Variants:
```json
[
    {
        "option1": "iPhone 15",
        "option2": "128GB",
        "option3": "Space Black",
        "sku_suffix": "-IP15-128-BLK",
        "price_adjustment": 0.00,
        "stock_quantity": 15,
        "is_available": true
    },
    {
        "option1": "iPhone 15 Pro",
        "option2": "256GB",
        "option3": "Titanium",
        "sku_suffix": "-IP15P-256-TIT",
        "price_adjustment": 200.00,
        "stock_quantity": 8,
        "is_available": true
    }
]
```

#### Form Display:
- **Model**: iPhone 15, iPhone 15 Pro
- **Storage**: 128GB, 256GB, 512GB
- **Color**: Space Black, Titanium, Blue

### 3. Furniture Store

#### Label Configuration:
- **Option 1 Label**: "Material"
- **Option 2 Label**: "Size"
- **Option 3 Label**: "Finish"

#### Sample Variants:
```json
[
    {
        "option1": "Oak",
        "option2": "Queen",
        "option3": "Natural",
        "sku_suffix": "-OAK-Q-NAT",
        "price_adjustment": 0.00,
        "stock_quantity": 5,
        "is_available": true
    },
    {
        "option1": "Walnut",
        "option2": "King",
        "option3": "Dark Stain",
        "sku_suffix": "-WAL-K-DRK",
        "price_adjustment": 150.00,
        "stock_quantity": 3,
        "is_available": true
    }
]
```

#### Form Display:
- **Material**: Oak, Walnut, Pine
- **Size**: Twin, Queen, King
- **Finish**: Natural, Dark Stain, White

### 4. Software Company

#### Label Configuration:
- **Option 1 Label**: "License Type"
- **Option 2 Label**: "Platform"
- **Option 3 Label**: null

#### Sample Variants:
```json
[
    {
        "option1": "Personal",
        "option2": "Windows",
        "option3": "",
        "sku_suffix": "-PERS-WIN",
        "price_adjustment": 0.00,
        "stock_quantity": 999,
        "is_available": true
    },
    {
        "option1": "Commercial",
        "option2": "Mac",
        "option3": "",
        "sku_suffix": "-COMM-MAC",
        "price_adjustment": 50.00,
        "stock_quantity": 999,
        "is_available": true
    }
]
```

#### Form Display:
- **License Type**: Personal, Commercial, Enterprise
- **Platform**: Windows, Mac, Linux

## Table Display

### Dynamic Variant Display

#### Code Implementation:
```php
Tables\Columns\TextColumn::make('product_variants')
    ->label('Variants')
    ->formatStateUsing(function (?array $state): string {
        if (!$state || empty($state)) return 'None';
        
        $variants = [];
        foreach ($state as $variant) {
            $variantParts = [];
            
            if (isset($variant['option1']) && !empty($variant['option1'])) {
                $variantParts[] = $variant['option1'];
            }
            if (isset($variant['option2']) && !empty($variant['option2'])) {
                $variantParts[] = $variant['option2'];
            }
            if (isset($variant['option3']) && !empty($variant['option3'])) {
                $variantParts[] = $variant['option3'];
            }
            
            if (!empty($variantParts)) {
                $variants[] = implode('/', $variantParts);
            }
        }
        
        $count = count($variants);
        if ($count === 0) return 'None';
        if ($count <= 2) return implode(', ', $variants);
        
        return implode(', ', array_slice($variants, 0, 2)) . ' +' . ($count - 2) . ' more';
    })
```

#### Display Examples:

**Clothing:**
- "Black/M, Red/L +3 more"

**Electronics:**
- "iPhone 15/128GB/Black, iPhone 15 Pro/256GB/Titanium +5 more"

**Furniture:**
- "Oak/Queen/Natural, Walnut/King/Dark +2 more"

**Software:**
- "Personal/Windows, Commercial/Mac"

## Migration & Data Updates

### Automatic Conversion

#### Seeder Updates:
The seeder automatically converts existing color/size data to the new option1/option2/option3 structure:

```php
// Old structure
'color' => 'Black',
'size' => 'L'

// New structure
'option1' => 'Black',
'option2' => 'L',
'option3' => ''
```

#### Label Assignment:
```php
$product->update([
    'product_variants' => $variantData['variants'],
    'variant_option1_label' => $variantData['option1_label'],
    'variant_option2_label' => $variantData['option2_label'],
    'variant_option3_label' => $variantData['option3_label'] ?? null,
]);
```

## Technical Implementation

### Database Schema:
```sql
-- Variant data (JSON)
product_variants: [
    {
        "option1": "value1",
        "option2": "value2", 
        "option3": "value3",
        "sku_suffix": "-VAL1-VAL2",
        "price_adjustment": 0.00,
        "stock_quantity": 25,
        "is_available": true
    }
]

-- Label configuration (strings)
variant_option1_label: "Color"
variant_option2_label: "Size"
variant_option3_label: "Material"
```

### Form Validation:
- **Option 1 & 2**: Required for each variant
- **Option 3**: Optional
- **Labels**: Team can set custom labels for their products
- **Dynamic Labels**: Form fields update based on label configuration

## User Workflow

### Setting Up Variant Labels:
1. **Define Option 1**: Enter what Option 1 represents (e.g., "Color")
2. **Define Option 2**: Enter what Option 2 represents (e.g., "Size")
3. **Define Option 3**: Optionally define third option (e.g., "Material")
4. **Form Updates**: Variant form fields now show custom labels

### Adding Variants:
1. **Add Variant**: Click "Add Product Variant"
2. **Enter Values**: Fill in values using team's custom labels
3. **Set Pricing**: Add price adjustments for premium options
4. **Manage Stock**: Set individual stock quantities
5. **Toggle Availability**: Enable/disable specific variants

This flexible system allows teams to adapt the variant structure to their specific product types and terminology while maintaining a consistent database structure!
