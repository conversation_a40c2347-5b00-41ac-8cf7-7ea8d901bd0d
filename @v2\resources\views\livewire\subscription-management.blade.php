<div class="space-y-6">
    <!-- Current Subscription -->
    @if($currentSubscription)
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-lg font-semibold text-blue-900 dark:text-blue-100">
                        Current Plan: {{ $currentSubscription->plan->name ?? 'Unknown Plan' }}
                    </h4>
                    <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        @if($currentSubscription->ends_at)
                            @if($currentSubscription->ends_at->isPast())
                                <span class="text-red-600 dark:text-red-400">Expired on {{ $currentSubscription->ends_at->format('M j, Y') }}</span>
                            @else
                                Expires on {{ $currentSubscription->ends_at->format('M j, Y') }}
                            @endif
                        @else
                            Active subscription
                        @endif
                    </p>
                    @if($currentSubscription->trial_ends_at && $currentSubscription->trial_ends_at->isFuture())
                        <p class="text-sm text-green-600 dark:text-green-400 mt-1">
                            Trial ends on {{ $currentSubscription->trial_ends_at->format('M j, Y') }}
                        </p>
                    @endif
                </div>
                <div class="flex space-x-3">
                    @if($currentSubscription->ends_at && $currentSubscription->ends_at->isPast())
                        <button wire:click="renewSubscription"
                                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Renew Subscription
                        </button>
                    @else
                        <button wire:click="renewSubscription"
                                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Extend Subscription
                        </button>
                        <button wire:click="cancelSubscription"
                                wire:confirm="Are you sure you want to cancel your subscription?"
                                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Cancel
                        </button>
                    @endif
                </div>
            </div>
        </div>
    @else
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h4 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                        No Active Subscription
                    </h4>
                    <p class="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                        Choose a plan below to get started with premium features.
                    </p>
                </div>
            </div>
        </div>
    @endif

    <!-- Available Plans -->
    <div>
        <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Available Plans
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach($availablePlans as $plan)
                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-6 {{ $selectedPlan && $selectedPlan->id === $plan->id ? 'ring-2 ring-blue-500 border-blue-500' : '' }}">
                    <div class="text-center">
                        <h5 class="text-xl font-semibold text-gray-900 dark:text-gray-100">
                            {{ $plan->name }}
                        </h5>
                        <div class="mt-4">
                            <span class="text-3xl font-bold text-gray-900 dark:text-gray-100">
                                ${{ number_format($plan->price, 2) }}
                            </span>
                            <span class="text-gray-600 dark:text-gray-400">
                                /{{ $plan->interval }}
                            </span>
                        </div>
                        @if($plan->description)
                            <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
                                {{ $plan->description }}
                            </p>
                        @endif
                        @if($plan->trial_period_days)
                            <p class="mt-2 text-sm text-green-600 dark:text-green-400">
                                {{ $plan->trial_period_days }} days free trial
                            </p>
                        @endif
                    </div>

                    <div class="mt-6">
                        @if($currentSubscription && $currentSubscription->plan_id === $plan->id)
                            <button disabled
                                    class="w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-lg font-medium cursor-not-allowed">
                                Current Plan
                            </button>
                        @else
                            <button wire:click="selectPlan({{ $plan->id }})"
                                    class="w-full {{ $selectedPlan && $selectedPlan->id === $plan->id ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-600 hover:bg-gray-700' }} text-white py-2 px-4 rounded-lg font-medium transition-colors">
                                {{ $selectedPlan && $selectedPlan->id === $plan->id ? 'Selected' : 'Select Plan' }}
                            </button>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Subscribe Button -->
    @if($selectedPlan)
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        Subscribe to {{ $selectedPlan->name }}
                    </h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        ${{ number_format($selectedPlan->price, 2) }} per {{ $selectedPlan->interval }}
                        @if($selectedPlan->trial_period_days)
                            - {{ $selectedPlan->trial_period_days }} days free trial
                        @endif
                    </p>
                </div>
                <div class="flex space-x-3">
                    <button wire:click="$set('selectedPlan', null)"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                        Cancel
                    </button>
                    <button wire:click="subscribeToPlan"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        {{ $selectedPlan->price > 0 ? 'Subscribe & Pay' : 'Subscribe Free' }}
                    </button>
                </div>
            </div>
        </div>
    @endif
</div>
