<?php namespace MyFatoorah\Library\API\Payment; use MyFatoorah\Library\MyFatoorah; use Exception; class  MyFatoorahPayment extends MyFatoorah{public static $pmCachedFile=__DIR__.'/mf-methods.json';public function initiatePayment($invoiceAmount=0,$currencyIso='',$isCached=false){$postFields=['InvoiceAmount'=>$invoiceAmount,'CurrencyIso'=>$currencyIso,];$json=$this->callAPI("$this->apiURL/v2/InitiatePayment",$postFields,null,'Initiate Payment');$paymentMethods=($json->Data->PaymentMethods)??[];if(!empty($paymentMethods)&&$isCached){file_put_contents(self::$pmCachedFile,json_encode($paymentMethods));}return $paymentMethods;}public function getCachedVendorGateways(){if(file_exists(self::$pmCachedFile)){$cache=file_get_contents(self::$pmCachedFile);return($cache)?json_decode($cache):[];}else{return $this->initiatePayment(0,'',true);}}public function getCachedCheckoutGateways($isApRegistered=false){$gateways=$this->getCachedVendorGateways();$cachedGateways=['all'=>[],'cards'=>[],'form'=>[],'ap'=>[],'gp'=>[]];foreach($gateways as $gateway){$cachedGateways=$this->addGatewayToCheckout($gateway,$cachedGateways,$isApRegistered);}if($isApRegistered){$cachedGateways['ap']=$cachedGateways['ap'][0]??[];}return $cachedGateways;}protected function addGatewayToCheckout($gateway,$checkoutGateways,$isApRegistered){if($gateway->PaymentMethodCode=='gp'){$checkoutGateways['gp']=$gateway;$checkoutGateways['all'][]=$gateway;}elseif($gateway->PaymentMethodCode=='ap'){if($isApRegistered){$checkoutGateways['ap'][]=$gateway;}else{$checkoutGateways['cards'][]=$gateway;}$checkoutGateways['all'][]=$gateway;}elseif($gateway->PaymentMethodCode=='stc'){$checkoutGateways['cards'][]=$gateway;$checkoutGateways['all'][]=$gateway;}else{if($gateway->IsEmbeddedSupported){$checkoutGateways['form'][]=$gateway;$checkoutGateways['all'][]=$gateway;}elseif(!$gateway->IsDirectPayment){$checkoutGateways['cards'][]=$gateway;$checkoutGateways['all'][]=$gateway;}}return $checkoutGateways;}public function getOnePaymentMethod($gateway,$searchKey='PaymentMethodId',$invoiceAmount=0,$currencyIso=''){$paymentMethods=$this->initiatePayment($invoiceAmount,$currencyIso);$paymentMethod=null;foreach($paymentMethods as $pm){if($pm->$searchKey==$gateway){$paymentMethod=$pm;break;}}if(!isset($paymentMethod)){throw new Exception('Please contact Account Manager to enable the used payment method in your account');}return $paymentMethod;}public function getInvoiceURL($curlData,$gatewayId=0,$orderId=null,$sessionId=null,$ntfOption='Lnk'){$this->log('------------------------------------------------------------');$curlData['CustomerReference']=$curlData['CustomerReference']?? $orderId;if(!empty($sessionId)){$curlData['SessionId']=$sessionId;$data=$this->executePayment($curlData);return['invoiceURL'=>$data->PaymentURL,'invoiceId'=>$data->InvoiceId];}elseif($gatewayId=='myfatoorah'||empty($gatewayId)){if(empty($curlData['NotificationOption'])){$curlData['NotificationOption']=$ntfOption;}$data=$this->sendPayment($curlData);return['invoiceURL'=>$data->InvoiceURL,'invoiceId'=>$data->InvoiceId];}else{$curlData['PaymentMethodId']=$gatewayId;$data=$this->executePayment($curlData);return['invoiceURL'=>$data->PaymentURL,'invoiceId'=>$data->InvoiceId];}}public function sendPayment($curlData){$this->preparePayment($curlData);$json=$this->callAPI("$this->apiURL/v2/SendPayment",$curlData,$curlData['CustomerReference'],'Send Payment');return $json->Data;}public function executePayment($curlData){$this->preparePayment($curlData);$json=$this->callAPI("$this->apiURL/v2/ExecutePayment",$curlData,$curlData['CustomerReference'],'Execute Payment');return $json->Data;}private function preparePayment(&$curlData){$curlData['CustomerReference']=$curlData['CustomerReference']?? null;$curlData['SourceInfo']=$curlData['SourceInfo']?? 'MyFatoorah PHP Library '.$this->version;if(!empty($curlData['CustomerName'])){$curlData['CustomerName']=preg_replace('/[^\p{L}\p{N}\s]/u','',$curlData['CustomerName']);}if(!empty($curlData['InvoiceItems'])){foreach($curlData['InvoiceItems']as &$item){$item['ItemName']=strip_tags($item['ItemName']);}}if(empty($curlData['CustomerEmail'])){$curlData['CustomerEmail']=null;}}public function getEmbeddedSession($userDefinedField='',$logId=null){$curlData=['CustomerIdentifier'=>$userDefinedField];return $this->InitiateSession($curlData,$logId);}public function InitiateSession($curlData,$logId=null){$json=$this->callAPI("$this->apiURL/v2/InitiateSession",$curlData,$logId,'Initiate Session');return $json->Data;}public function registerApplePayDomain($url){$domainName=['DomainName'=>parse_url($url,PHP_URL_HOST)];return $this->callAPI("$this->apiURL/v2/RegisterApplePayDomain",$domainName,'','Register Apple Pay Domain');}}