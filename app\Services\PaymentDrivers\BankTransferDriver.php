<?php

namespace App\Services\PaymentDrivers;

use TomatoPHP\FilamentPayments\Services\Contracts\PaymentDriverInterface;
use TomatoPHP\FilamentPayments\Models\Payment;

class BankTransferDriver implements PaymentDriverInterface
{
    public function pay(Payment $payment): array
    {
        // Generate bank transfer instructions
        $bankDetails = [
            'bank_name' => 'Bangkok Bank',
            'account_name' => config('app.name'),
            'account_number' => '123-456-7890',
            'swift_code' => 'BKKBTHBK',
            'reference' => $payment->uuid,
        ];

        // Update payment status to pending
        $payment->update([
            'status' => 'pending',
            'gateway_response' => json_encode($bankDetails),
        ]);

        return [
            'success' => true,
            'payment_url' => route('payment.bank-transfer.instructions', $payment->uuid),
            'message' => 'Please complete the bank transfer using the provided details.',
            'bank_details' => $bankDetails,
        ];
    }

    public function verify(Payment $payment): array
    {
        // In a real implementation, you would verify the payment with your bank
        // For now, we'll assume manual verification by admin
        
        return [
            'success' => $payment->status === 'completed',
            'message' => $payment->status === 'completed' 
                ? 'Payment verified successfully' 
                : 'Payment verification pending',
        ];
    }

    public function refund(Payment $payment, float $amount = null): array
    {
        $refundAmount = $amount ?? $payment->amount;
        
        // Create refund record
        $payment->update([
            'status' => 'refunded',
            'refunded_amount' => $refundAmount,
            'refunded_at' => now(),
        ]);

        return [
            'success' => true,
            'message' => 'Refund processed. Amount will be transferred back to your account within 3-5 business days.',
            'refund_amount' => $refundAmount,
        ];
    }

    public function webhook(array $data): array
    {
        // Handle bank transfer webhooks if available
        return [
            'success' => true,
            'message' => 'Webhook processed',
        ];
    }

    public function getPaymentMethods(): array
    {
        return [
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'description' => 'Transfer money directly from your bank account',
                'icon' => 'heroicon-o-building-library',
                'currencies' => ['THB', 'USD'],
            ],
        ];
    }

    public function isAvailable(): bool
    {
        return true;
    }

    public function getName(): string
    {
        return 'Bank Transfer';
    }

    public function getSlug(): string
    {
        return 'bank-transfer';
    }
}
