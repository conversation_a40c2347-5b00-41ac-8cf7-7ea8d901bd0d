<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-bolt class="w-5 h-5 text-primary-600 mr-2" />
                เข้าถึงด่วน
            </div>
        </x-slot>

        <div class="space-y-3">
            @foreach($this->getQuickActions() as $action)
                <a 
                    href="{{ $action['url'] }}" 
                    class="w-full flex items-center justify-between p-3 rounded-lg transition-colors
                        @if($action['color'] === 'primary') bg-primary-50 hover:bg-primary-100 text-primary-700 dark:bg-primary-900/20 dark:hover:bg-primary-900/30 dark:text-primary-300
                        @elseif($action['color'] === 'success') bg-success-50 hover:bg-success-100 text-success-700 dark:bg-success-900/20 dark:hover:bg-success-900/30 dark:text-success-300
                        @elseif($action['color'] === 'info') bg-info-50 hover:bg-info-100 text-info-700 dark:bg-info-900/20 dark:hover:bg-info-900/30 dark:text-info-300
                        @elseif($action['color'] === 'warning') bg-warning-50 hover:bg-warning-100 text-warning-700 dark:bg-warning-900/20 dark:hover:bg-warning-900/30 dark:text-warning-300
                        @endif"
                >
                    <span class="flex items-center">
                        @if($action['icon'] === 'heroicon-o-academic-cap')
                            <x-heroicon-o-academic-cap class="w-5 h-5 mr-2" />
                        @elseif($action['icon'] === 'heroicon-o-clipboard-document-list')
                            <x-heroicon-o-clipboard-document-list class="w-5 h-5 mr-2" />
                        @elseif($action['icon'] === 'heroicon-o-chart-bar')
                            <x-heroicon-o-chart-bar class="w-5 h-5 mr-2" />
                        @elseif($action['icon'] === 'heroicon-o-calendar-days')
                            <x-heroicon-o-calendar-days class="w-5 h-5 mr-2" />
                        @elseif($action['icon'] === 'heroicon-o-users')
                            <x-heroicon-o-users class="w-5 h-5 mr-2" />
                        @elseif($action['icon'] === 'heroicon-o-cog-6-tooth')
                            <x-heroicon-o-cog-6-tooth class="w-5 h-5 mr-2" />
                        @else
                            <x-heroicon-o-document class="w-5 h-5 mr-2" />
                        @endif
                        {{ $action['label'] }}
                    </span>
                    <x-heroicon-o-chevron-right class="w-4 h-4" />
                </a>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
