<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use TomatoPHP\FilamentSubscriptions\Models\Plan;

class PricingController extends Controller
{
    public function index()
    {
        // Get all active plans, grouped by type
        $individualPlans = Plan::where('is_active', true)
            ->where('slug', 'like', 'individual%')
            ->orderBy('sort_order')
            ->get();

        $schoolPlans = Plan::where('is_active', true)
            ->where('slug', 'like', 'school%')
            ->orderBy('sort_order')
            ->get();

        $additionalPlans = Plan::where('is_active', true)
            ->where('slug', 'like', 'additional%')
            ->orderBy('sort_order')
            ->get();

        return view('pricing.index', compact('individualPlans', 'schoolPlans', 'additionalPlans'));
    }

    public function selectPlan(Request $request, $planSlug)
    {
        $plan = Plan::where('slug', $planSlug)->where('is_active', true)->firstOrFail();
        
        // Store selected plan in session
        session(['selected_plan' => $plan->slug]);
        
        // If user is not authenticated, redirect to registration with plan
        if (!auth()->check()) {
            return redirect()->route('register', ['plan' => $plan->slug]);
        }

        // If user is authenticated, redirect to subscription checkout
        return redirect()->route('subscription.checkout', ['plan' => $plan->slug]);
    }
}
