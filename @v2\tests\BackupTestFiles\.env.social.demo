# Demo Configuration for Social Authentication
# This file shows different configuration scenarios

# ========================================
# Scenario 1: All Providers Enabled (Default)
# ========================================
SOCIAL_AUTH_ENABLED=true
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=true
APPLE_LOGIN_ENABLED=true
LINE_LOGIN_ENABLED=true
PHONE_LOGIN_ENABLED=true

# ========================================
# Scenario 2: Only Google and Phone Login
# ========================================
# SOCIAL_AUTH_ENABLED=true
# GOOGLE_LOGIN_ENABLED=true
# MICROSOFT_LOGIN_ENABLED=false
# APPLE_LOGIN_ENABLED=false
# LINE_LOGIN_ENABLED=false
# PHONE_LOGIN_ENABLED=true

# ========================================
# Scenario 3: Only Traditional Email/Password Login
# ========================================
# SOCIAL_AUTH_ENABLED=false
# GOOGLE_LOGIN_ENABLED=false
# MICROSOFT_LOGIN_ENABLED=false
# APPLE_LOGIN_ENABLED=false
# LINE_LOGIN_ENABLED=false
# PHONE_LOGIN_ENABLED=false

# ========================================
# Scenario 4: Asian Market Focus (LINE + Phone)
# ========================================
# SOCIAL_AUTH_ENABLED=true
# GOOGLE_LOGIN_ENABLED=false
# MICROSOFT_LOGIN_ENABLED=false
# APPLE_LOGIN_ENABLED=false
# LINE_LOGIN_ENABLED=true
# PHONE_LOGIN_ENABLED=true

# ========================================
# Scenario 5: Western Market Focus (Google + Apple + Microsoft)
# ========================================
# SOCIAL_AUTH_ENABLED=true
# GOOGLE_LOGIN_ENABLED=true
# MICROSOFT_LOGIN_ENABLED=true
# APPLE_LOGIN_ENABLED=true
# LINE_LOGIN_ENABLED=false
# PHONE_LOGIN_ENABLED=false

# ========================================
# Provider Credentials (Required when enabled)
# ========================================

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
GOOGLE_REDIRECT_URI=http://127.0.0.1:8000/auth/google/callback

# Microsoft OAuth
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
MICROSOFT_REDIRECT_URI=http://127.0.0.1:8000/auth/microsoft/callback

# Apple Sign In
APPLE_CLIENT_ID=your.apple.client.id.here
APPLE_TEAM_ID=YOUR_TEAM_ID
APPLE_KEY_ID=YOUR_KEY_ID
APPLE_PRIVATE_KEY_PATH=storage/app/private/apple_private_key.p8
APPLE_REDIRECT_URI=http://127.0.0.1:8000/auth/apple/callback

# LINE Login
LINE_CLIENT_ID=your_line_channel_id_here
LINE_CLIENT_SECRET=your_line_channel_secret_here
LINE_REDIRECT_URI=http://127.0.0.1:8000/auth/line/callback

# SMS Provider for Phone Auth
SMS_PROVIDER=twilio
TWILIO_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

# ========================================
# Usage Instructions
# ========================================
# 1. Copy the desired scenario configuration to your .env file
# 2. Add the required provider credentials for enabled providers
# 3. The login/register pages will automatically show only enabled providers
# 4. No code changes required - just environment variable changes
# 5. Restart your application after changing .env variables

# ========================================
# Testing
# ========================================
# - Visit /login or /register to see the enabled providers
# - Disabled providers will not appear in the UI
# - Phone authentication uses OTP code: 123456 for testing
# - Social providers will redirect to their respective OAuth flows
