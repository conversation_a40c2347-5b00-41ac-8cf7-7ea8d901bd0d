[2025-07-16 09:08:13] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'uat_edu' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = 'uat_edu' and table_name = 'app_settings' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Providers\\Filament\\AdminPanelProvider.php(95): Filament\\Panel->plugins(Array)
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#48 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'uat_edu', Object(SensitiveParameterValue), Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'uat_edu', '9ol.0p;/', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'uat_edu', '9ol.0p;/', Array)
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(344): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(361): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\MySqlBuilder.php(45): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(358): Illuminate\\Database\\Schema\\MySqlBuilder->hasTable('app_settings')
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\cwsps154\\app-settings\\src\\helper.php(18): Illuminate\\Support\\Facades\\Facade::__callStatic('hasTable', Array)
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\cwsps154\\app-settings\\src\\AppSettingsPlugin.php(39): get_settings('app.app_logo')
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(17): CWSPS154\\AppSettings\\AppSettingsPlugin->register(Object(Filament\\Panel))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\Panel\\Concerns\\HasPlugins.php(29): Filament\\Panel->plugin(Object(CWSPS154\\AppSettings\\AppSettingsPlugin))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Providers\\Filament\\AdminPanelProvider.php(95): Filament\\Panel->plugins(Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\PanelProvider.php(15): App\\Providers\\Filament\\AdminPanelProvider->panel(Object(Filament\\Panel))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(236): Filament\\PanelProvider->Filament\\{closure}()
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\Facades\\Filament.php(140): value(Object(Closure))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1411): Filament\\Facades\\Filament::Filament\\Facades\\{closure}(Object(Filament\\PanelRegistry), Object(Illuminate\\Foundation\\Application))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1329): Illuminate\\Container\\Container->fireCallbackArray(Object(Filament\\PanelRegistry), Array)
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(833): Illuminate\\Container\\Container->fireResolvingCallbacks('Filament\\\\PanelR...', Object(Filament\\PanelRegistry))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('Filament\\\\PanelR...', Array, true)
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('Filament\\\\PanelR...', Array)
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('Filament\\\\PanelR...', Array)
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(124): Illuminate\\Foundation\\Application->make('Filament\\\\PanelR...', Array)
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\FilamentManager.php(51): app('Filament\\\\PanelR...')
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\src\\FilamentServiceProvider.php(48): Filament\\FilamentManager->__construct()
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(931): Filament\\FilamentServiceProvider->Filament\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(815): Illuminate\\Container\\Container->build(Object(Closure))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1048): Illuminate\\Container\\Container->resolve('filament', Array, true)
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(751): Illuminate\\Foundation\\Application->resolve('filament', Array)
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1030): Illuminate\\Container\\Container->make('filament', Array)
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1559): Illuminate\\Foundation\\Application->make('filament')
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(237): Illuminate\\Container\\Container->offsetGet('filament')
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(208): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('filament')
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(352): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\routes\\web.php(13): Illuminate\\Support\\Facades\\Facade::__callStatic('getPanels', Array)
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(511): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(467): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteRegistrar.php(199): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\filament\\filament\\routes\\web.php(12): Illuminate\\Routing\\RouteRegistrar->group(Object(Closure))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(195): require('C:\\\\Projects\\\\Web...')
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\spatie\\laravel-package-tools\\src\\PackageServiceProvider.php(152): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('C:\\\\Projects\\\\Web...')
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Spatie\\LaravelPackageTools\\PackageServiceProvider->boot()
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(690): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1121): Illuminate\\Container\\Container->call(Array)
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1102): Illuminate\\Foundation\\Application->bootProvider(Object(Filament\\FilamentServiceProvider))
#59 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Filament\\FilamentServiceProvider), 'Filament\\\\Filame...')
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1101): array_walk(Array, Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(316): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#66 {main}
"} 
[2025-07-16 13:14:53] production.ERROR: Route [privacy] not defined. {"view":{"view":"C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\resources\\views\\layouts\\frontend.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#2604</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [privacy] not defined. at C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:516)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('privacy', Array, true)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\resources\\views\\layouts\\frontend.blade.php(352): route('privacy')
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\resources\\views\\homepage.blade.php(689): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(70): Illuminate\\View\\View->render()
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Http\\Middleware\\ClearTeamSettingsCache.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ClearTeamSettingsCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Http\\Middleware\\SetLocale.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#72 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [privacy] not defined. at C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:516)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(853): Illuminate\\Routing\\UrlGenerator->route('privacy', Array, true)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\storage\\framework\\views\\2a8df3256b477c2fb42a7c16091ab451.php(393): route('privacy')
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\storage\\framework\\views\\28055e5abf101dbb26b93a3745df4996.php(689): Illuminate\\View\\View->render()
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('C:\\\\Projects\\\\Web...')
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Projects\\\\Web...', Array)
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(74): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('C:\\\\Projects\\\\Web...', Array)
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('C:\\\\Projects\\\\Web...', Array)
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(191): Illuminate\\View\\View->getContents()
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(160): Illuminate\\View\\View->renderContents()
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(70): Illuminate\\View\\View->render()
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(920): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(887): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Http\\Middleware\\ClearTeamSettingsCache.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ClearTeamSettingsCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Http\\Middleware\\SetLocale.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#72 {main}
"} 
[2025-07-16 13:24:57] production.ERROR: Method App\Http\Controllers\HomepageController::privacy does not exist. {"exception":"[object] (BadMethodCallException(code: 0): Method App\\Http\\Controllers\\HomepageController::privacy does not exist. at C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php:68)
[stacktrace]
#0 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Illuminate\\Routing\\Controller->__call('privacy', Array)
#1 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('privacy', Array)
#2 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(264): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\HomepageController), 'privacy')
#3 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(210): Illuminate\\Routing\\Route->runController()
#4 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Http\\Middleware\\ClearTeamSettingsCache.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\ClearTeamSettingsCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\app\\Http\\Middleware\\SetLocale.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#18 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#25 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#26 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#27 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#28 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#29 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#49 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#50 C:\\Projects\\Web\\htdocs\\edu-v2\\@v2\\server.php(19): require_once('C:\\\\Projects\\\\Web...')
#51 {main}
"} 
