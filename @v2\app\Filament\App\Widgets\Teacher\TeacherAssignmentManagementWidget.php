<?php

namespace App\Filament\App\Widgets\Teacher;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TeacherAssignmentManagementWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.teacher.teacher-assignment-management';
    
    protected int | string | array $columnSpan = 'full';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('teacher');
    }

    public function getAssignments(): array
    {
        // Placeholder data - replace with actual assignment data
        return [
            [
                'date' => '25/05/2566',
                'subject' => 'คณิตศาสตร์',
                'lesson' => 'สมการเชิงเส้น',
                'grade' => 'ม.1',
                'class' => '1',
                'total_students' => 30,
                'completed' => 15,
                'status' => 'in-progress',
                'status_label' => 'อยู่ระหว่างดำเนินการ',
                'status_color' => 'yellow'
            ],
            [
                'date' => '23/05/2566',
                'subject' => 'ภาษาไทย',
                'lesson' => 'คำราชาศัพท์',
                'grade' => 'ม.1',
                'class' => '2',
                'total_students' => 28,
                'completed' => 28,
                'status' => 'completed',
                'status_label' => 'เสร็จสิ้น',
                'status_color' => 'green'
            ],
            [
                'date' => '18/05/2566',
                'subject' => 'วิทยาศาสตร์',
                'lesson' => 'แรงและการเคลื่อนที่',
                'grade' => 'ม.2',
                'class' => '3',
                'total_students' => 32,
                'completed' => 25,
                'status' => 'overdue',
                'status_label' => 'เลยกำหนด',
                'status_color' => 'red'
            ],
        ];
    }

    public function getAssignmentTypes(): array
    {
        return [
            ['value' => 'homework', 'label' => 'การบ้าน'],
            ['value' => 'quiz', 'label' => 'แบบทดสอบ'],
            ['value' => 'project', 'label' => 'โครงงาน'],
        ];
    }

    public function getSubjects(): array
    {
        return [
            ['value' => 'thai', 'label' => 'ภาษาไทย'],
            ['value' => 'math', 'label' => 'คณิตศาสตร์'],
            ['value' => 'science', 'label' => 'วิทยาศาสตร์'],
            ['value' => 'social', 'label' => 'สังคมศึกษา'],
            ['value' => 'english', 'label' => 'ภาษาอังกฤษ'],
        ];
    }

    public function getClasses(): array
    {
        return [
            ['value' => 'm1-1', 'label' => 'ม.1/1'],
            ['value' => 'm1-2', 'label' => 'ม.1/2'],
            ['value' => 'm2-1', 'label' => 'ม.2/1'],
            ['value' => 'm2-2', 'label' => 'ม.2/2'],
            ['value' => 'm3-1', 'label' => 'ม.3/1'],
            ['value' => 'm3-2', 'label' => 'ม.3/2'],
        ];
    }

    public function getStatusOptions(): array
    {
        return [
            ['value' => 'assigned', 'label' => 'มอบหมายแล้ว'],
            ['value' => 'in-progress', 'label' => 'อยู่ระหว่างดำเนินการ'],
            ['value' => 'completed', 'label' => 'เสร็จสิ้น'],
            ['value' => 'overdue', 'label' => 'เลยกำหนด'],
        ];
    }
}
