<?php

namespace App\Filament\Resources\Blog;

use App\Filament\Resources\Blog\PostResource\Pages;
use App\Models\Blog\Post;
use App\Models\Blog\Author;
use App\Models\Blog\Category;
use Filament\Forms;
use Filament\Forms\Components\SpatieTagsInput;
use Filament\Forms\Form;
use Filament\Infolists\Components;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Pages\Page;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class PostResource extends Resource
{
    protected static ?string $model = Post::class;

    protected static ?string $slug = 'blog/posts';

    protected static ?string $recordTitleAttribute = 'title';

    protected static ?string $navigationGroup = 'Content';

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?int $navigationSort = 0;

    /**
     * Hide the default navigation item since we're using custom ones
     */
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'blogPosts';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    /**
     * Modify the eloquent query to filter by post type from URL
     */
    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Filter by type if specified in URL
        if ($type = request()->get('type')) {
            $query->where('type', $type);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->live(onBlur: true)
                            ->maxLength(255)
                            ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) => $operation === 'create' ? $set('slug', Str::slug($state)) : null),

                        Forms\Components\TextInput::make('slug')
                            ->disabled()
                            ->dehydrated()
                            ->required()
                            ->maxLength(255)
                            ->unique(Post::class, 'slug', ignoreRecord: true),

                        Forms\Components\Select::make('type')
                            ->label('Post Type')
                            ->options(function () {
                                $types = [];
                                foreach (config('post-types.types', []) as $key => $config) {
                                    if ($config['enabled'] ?? true) {
                                        $types[$key] = $config['name'];
                                    }
                                }
                                return $types;
                            })
                            ->default(config('post-types.default_type', 'blog'))
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                // Reset category when type changes
                                $set('blog_category_id', null);
                            }),

                        Forms\Components\MarkdownEditor::make('content')
                            ->required()
                            ->columnSpan('full'),

                        Forms\Components\Select::make('blog_author_id')
                            ->label('Author')
                            ->relationship('author', 'name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->createOptionForm(static::getBlogAuthorCreateForm())
                            ->createOptionUsing(function (array $data): int {
                                $author = \App\Models\Blog\Author::create([
                                    'name' => $data['name'],
                                    'email' => $data['email'],
                                    'bio' => $data['bio'] ?? null,
                                    'github_handle' => $data['github_handle'] ?? null,
                                    'twitter_handle' => $data['twitter_handle'] ?? null,
                                    'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                                ]);

                                \Filament\Notifications\Notification::make()
                                    ->title('Author created successfully!')
                                    ->success()
                                    ->send();

                                return $author->id;
                            })
                            ->suffixAction(
                                Forms\Components\Actions\Action::make('openAuthorManager')
                                    ->icon('heroicon-o-cog-6-tooth')
                                    ->tooltip('Manage Authors')
                                    ->url(fn (): string => \App\Filament\Resources\Blog\AuthorResource::getUrl('index', panel: 'backend'))
                                    ->openUrlInNewTab()
                            ),

                        Forms\Components\Select::make('blog_category_id')
                            ->label('Category')
                            ->relationship(
                                'category',
                                'name',
                                fn (Builder $query, Forms\Get $get) => $query
                                    ->where('team_id', \Filament\Facades\Filament::getTenant()?->id)
                                    ->where('type', $get('type') ?? config('post-types.default_type', 'blog'))
                            )
                            ->searchable()
                            ->preload()
                            ->required()
                            ->createOptionForm(static::getBlogCategoryCreateForm())
                            ->createOptionUsing(function (array $data, Forms\Get $get): int {
                                $category = \App\Models\Blog\Category::create([
                                    'name' => $data['name'],
                                    'slug' => $data['slug'],
                                    'description' => $data['description'] ?? null,
                                    'is_visible' => $data['is_visible'] ?? true,
                                    'type' => $get('type') ?? config('post-types.default_type', 'blog'),
                                    'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                                ]);

                                \Filament\Notifications\Notification::make()
                                    ->title('Category created successfully!')
                                    ->success()
                                    ->send();

                                return $category->id;
                            })
                            ->suffixAction(
                                Forms\Components\Actions\Action::make('openCategoryManager')
                                    ->icon('heroicon-o-cog-6-tooth')
                                    ->tooltip('Manage Categories')
                                    ->url(fn (): string => \App\Filament\Resources\Blog\CategoryResource::getUrl('index', panel: 'backend'))
                                    ->openUrlInNewTab()
                            ),

                        Forms\Components\DatePicker::make('published_at')
                            ->label('Published Date'),

                        SpatieTagsInput::make('tags'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Image')
                    ->schema([
                        Forms\Components\FileUpload::make('image')
                            ->image()
                            ->hiddenLabel(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('Image'),

                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('Type')
                    ->badge()
                    ->formatStateUsing(function (string $state): string {
                        $config = config("post-types.types.{$state}", []);
                        return $config['name'] ?? ucfirst($state);
                    })
                    ->colors([
                        'primary' => 'blog',
                        'success' => 'news',
                        'warning' => 'knowledge',
                    ])
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('author.name')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->getStateUsing(fn (Post $record): string => $record->published_at?->isPast() ? 'Published' : 'Draft')
                    ->colors([
                        'success' => 'Published',
                    ]),

                Tables\Columns\TextColumn::make('category.name')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('published_at')
                    ->label('Published Date')
                    ->date(),

                Tables\Columns\TextColumn::make('comments.customer.name')
                    ->label('Comment Authors')
                    ->listWithLineBreaks()
                    ->limitList(2)
                    ->expandableLimitedList(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('Post Type')
                    ->options(function () {
                        $types = [];
                        foreach (config('post-types.types', []) as $key => $config) {
                            if ($config['enabled'] ?? true) {
                                $types[$key] = $config['name'];
                            }
                        }
                        return $types;
                    }),

                Tables\Filters\Filter::make('published_at')
                    ->form([
                        Forms\Components\DatePicker::make('published_from')
                            ->placeholder(fn ($state): string => 'Dec 18, ' . now()->subYear()->format('Y')),
                        Forms\Components\DatePicker::make('published_until')
                            ->placeholder(fn ($state): string => now()->format('M d, Y')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['published_from'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('published_at', '>=', $date),
                            )
                            ->when(
                                $data['published_until'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('published_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['published_from'] ?? null) {
                            $indicators['published_from'] = 'Published from ' . Carbon::parse($data['published_from'])->toFormattedDateString();
                        }
                        if ($data['published_until'] ?? null) {
                            $indicators['published_until'] = 'Published until ' . Carbon::parse($data['published_until'])->toFormattedDateString();
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\EditAction::make(),

                Tables\Actions\DeleteAction::make(),
            ])
            ->groupedBulkActions([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function () {
                        Notification::make()
                            ->title('Now, now, don\'t be cheeky, leave some records for others to play with!')
                            ->warning()
                            ->send();
                    }),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make()
                    ->schema([
                        Components\Split::make([
                            Components\Grid::make(2)
                                ->schema([
                                    Components\Group::make([
                                        Components\TextEntry::make('title'),
                                        Components\TextEntry::make('slug'),
                                        Components\TextEntry::make('published_at')
                                            ->badge()
                                            ->date()
                                            ->color('success'),
                                    ]),
                                    Components\Group::make([
                                        Components\TextEntry::make('author.name'),
                                        Components\TextEntry::make('category.name'),
                                        Components\SpatieTagsEntry::make('tags'),
                                    ]),
                                ]),
                            Components\ImageEntry::make('image')
                                ->hiddenLabel()
                                ->grow(false),
                        ])->from('lg'),
                    ]),
                Components\Section::make('Content')
                    ->schema([
                        Components\TextEntry::make('content')
                            ->prose()
                            ->markdown()
                            ->hiddenLabel(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            Pages\ViewPost::class,
            Pages\EditPost::class,
            Pages\ManagePostComments::class,
        ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPosts::route('/'),
            'create' => Pages\CreatePost::route('/create'),
            'comments' => Pages\ManagePostComments::route('/{record}/comments'),
            'edit' => Pages\EditPost::route('/{record}/edit'),
            'view' => Pages\ViewPost::route('/{record}'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            PostResource\Widgets\BlogAuthorCategoryStats::class,
        ];
    }

    /** @return Builder<Post> */
    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['author', 'category']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['title', 'slug', 'author.name', 'category.name'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        /** @var Post $record */
        $details = [];

        if ($record->author) {
            $details['Author'] = $record->author->name;
        }

        if ($record->category) {
            $details['Category'] = $record->category->name;
        }

        return $details;
    }

    /**
     * Get blog author creation form schema
     */
    public static function getBlogAuthorCreateForm(): array
    {
        return [
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255)
                        ->placeholder('Author full name'),

                    Forms\Components\TextInput::make('email')
                        ->label('Email address')
                        ->required()
                        ->maxLength(255)
                        ->email()
                        ->unique(\App\Models\Blog\Author::class, 'email')
                        ->placeholder('<EMAIL>'),

                    Forms\Components\TextInput::make('github_handle')
                        ->label('GitHub handle')
                        ->maxLength(255)
                        ->placeholder('username'),

                    Forms\Components\TextInput::make('twitter_handle')
                        ->label('Twitter handle')
                        ->maxLength(255)
                        ->placeholder('@username'),

                    Forms\Components\MarkdownEditor::make('bio')
                        ->label('Biography')
                        ->columnSpan('full')
                        ->placeholder('Brief biography of the author...'),
                ]),
        ];
    }

    /**
     * Get blog category creation form schema
     */
    public static function getBlogCategoryCreateForm(): array
    {
        return [
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255)
                        ->live(onBlur: true)
                        ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                            if ($operation !== 'create') {
                                return;
                            }
                            $set('slug', Str::slug($state));
                        })
                        ->placeholder('Category name'),

                    Forms\Components\TextInput::make('slug')
                        // ->disabled()
                        ->dehydrated()
                        ->required()
                        ->maxLength(255)
                        ->unique(\App\Models\Blog\Category::class, 'slug'),

                    Forms\Components\MarkdownEditor::make('description')
                        ->columnSpan('full')
                        ->placeholder('Brief description of the category...'),

                    Forms\Components\Toggle::make('is_visible')
                        ->label('Visible to customers')
                        ->default(true)
                        ->columnSpan('full'),
                ]),
        ];
    }
}
