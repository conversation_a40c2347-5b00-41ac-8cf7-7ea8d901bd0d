@extends('layouts.frontend')

@section('title', 'SVG Animation Demo - EduNest')

@push('styles')
    @vite('resources/css/layout.css')
    <style>
        .demo-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .demo-card:hover {
            border-color: #06b6d4;
            transform: translateY(-2px);
        }
        .code-block {
            background: #1f2937;
            color: #e5e7eb;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #06b6d4;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
@endpush

@section('content')

<!-- Page Header -->
<section class="relative pt-20 pb-12 bg-gradient-to-r from-cyan-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                SVG <span class="gradient-text">Animation Demo</span>
            </h1>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
                Preview all available SVG animations and icons. Copy the code you like!
            </p>
            
            <!-- Page Toggle -->
            <div class="flex items-center justify-center space-x-4 mb-4">
                <span class="text-sm font-medium text-gray-700">Demo Page:</span>
                <label class="toggle-switch">
                    <input type="checkbox" id="pageToggle" checked>
                    <span class="slider"></span>
                </label>
                <span class="text-sm text-gray-500">Enable/Disable this demo page</span>
            </div>
        </div>
    </div>
</section>

<div id="demoContent" class="container mx-auto px-4 py-8">

    <!-- Built-in Animated SVGs -->
    <section class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Built-in Animated SVGs</h2>
        
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            <!-- Floating Particles -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 relative overflow-hidden">
                    @include('components.animated-svg', ['type' => 'floating-particles'])
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-gray-500 text-sm">Floating Particles</span>
                    </div>
                </div>
                <h3 class="font-semibold mb-2">Floating Particles</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'floating-particles'
])
                </div>
            </div>

            <!-- Wave Divider -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'wave-divider', 'color' => 'rgba(6, 182, 212, 0.3)'])
                </div>
                <h3 class="font-semibold mb-2">Wave Divider</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'wave-divider',
    'color' => 'rgba(6, 182, 212, 0.3)'
])
                </div>
            </div>

            <!-- Pulsing Orb -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'pulsing-orb', 'color' => '#06b6d4'])
                </div>
                <h3 class="font-semibold mb-2">Pulsing Orb</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'pulsing-orb',
    'color' => '#06b6d4'
])
                </div>
            </div>

            <!-- Orbital System -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'orbital-system', 'color' => '#3b82f6'])
                </div>
                <h3 class="font-semibold mb-2">Orbital System</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'orbital-system',
    'color' => '#3b82f6'
])
                </div>
            </div>

            <!-- Morphing Blob -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'morphing-blob', 'color' => 'rgba(139, 92, 246, 0.3)'])
                </div>
                <h3 class="font-semibold mb-2">Morphing Blob</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'morphing-blob',
    'color' => 'rgba(139, 92, 246, 0.3)'
])
                </div>
            </div>

            <!-- DNA Helix -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'dna-helix', 'color' => '#06b6d4'])
                </div>
                <h3 class="font-semibold mb-2">DNA Helix</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'dna-helix',
    'color' => '#06b6d4'
])
                </div>
            </div>

            <!-- Geometric Spinner -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'geometric-spinner', 'color' => '#8b5cf6'])
                </div>
                <h3 class="font-semibold mb-2">Geometric Spinner</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'geometric-spinner',
    'color' => '#8b5cf6'
])
                </div>
            </div>

            <!-- Heartbeat -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'heartbeat', 'color' => '#ec4899'])
                </div>
                <h3 class="font-semibold mb-2">Heartbeat Monitor</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'heartbeat',
    'color' => '#ec4899'
])
                </div>
            </div>

            <!-- Liquid Loading -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'liquid-loading', 'color' => '#10b981'])
                </div>
                <h3 class="font-semibold mb-2">Liquid Loading</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'liquid-loading',
    'color' => '#10b981'
])
                </div>
            </div>

        </div>
    </section>

    <!-- More Animations (Second Row) -->
    <section class="mb-12">
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            <!-- Kaleidoscope -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'kaleidoscope', 'color' => '#f59e0b'])
                </div>
                <h3 class="font-semibold mb-2">Kaleidoscope</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'kaleidoscope',
    'color' => '#f59e0b'
])
                </div>
            </div>

            <!-- Magnetic Field -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'magnetic-field', 'color' => '#06b6d4'])
                </div>
                <h3 class="font-semibold mb-2">Magnetic Field</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'magnetic-field',
    'color' => '#06b6d4'
])
                </div>
            </div>

            <!-- Pendulum -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'pendulum', 'color' => '#3b82f6'])
                </div>
                <h3 class="font-semibold mb-2">Pendulum</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'pendulum',
    'color' => '#3b82f6'
])
                </div>
            </div>

            <!-- Glitch Text -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'glitch-text', 'text' => 'DEMO', 'color' => '#ef4444'])
                </div>
                <h3 class="font-semibold mb-2">Glitch Text</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'glitch-text',
    'text' => 'DEMO',
    'color' => '#ef4444'
])
                </div>
            </div>

            <!-- Breathing Circle -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'breathing-circle', 'color' => '#8b5cf6'])
                </div>
                <h3 class="font-semibold mb-2">Breathing Circle</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'breathing-circle',
    'color' => '#8b5cf6'
])
                </div>
            </div>

            <!-- Elastic Bounce -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', ['type' => 'elastic-bounce', 'color' => '#06b6d4'])
                </div>
                <h3 class="font-semibold mb-2">Elastic Bounce</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'elastic-bounce',
    'color' => '#06b6d4'
])
                </div>
            </div>

        </div>
    </section>

    <!-- Custom Icon Components -->
    <section class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Custom Icon Components</h2>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">

            @php
                // Dynamically scan for icon files
                $iconPath = resource_path('views/components/svg/icons');
                $iconFiles = [];
                $animations = ['pulse-glow-svg', 'breathing-svg', 'elastic-bounce', 'heartbeat-svg', 'orbital-animation'];
                $colors = ['#06b6d4', '#10b981', '#8b5cf6', '#ec4899', '#f59e0b', '#3b82f6'];

                if (is_dir($iconPath)) {
                    $files = scandir($iconPath);
                    foreach ($files as $file) {
                        if (pathinfo($file, PATHINFO_EXTENSION) === 'php') {
                            $iconName = pathinfo($file, PATHINFO_FILENAME);
                            $iconFiles[] = $iconName;
                        }
                    }
                }
            @endphp

            @foreach($iconFiles as $index => $iconName)
            @php
                $animation = $animations[$index % count($animations)];
                $color = $colors[$index % count($colors)];
                $displayName = ucwords(str_replace(['-', '_'], ' ', $iconName));
                $iconPath = 'components.svg.icons.' . $iconName;
                $iconExists = view()->exists($iconPath);
            @endphp

            @if($iconExists)
            <!-- {{ $displayName }} Icon -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', [
                        'type' => 'custom-icon',
                        'icon' => $iconName,
                        'size' => '16',
                        'animation' => $animation,
                        'color' => $color
                    ])
                </div>
                <h3 class="font-semibold mb-2">{{ $displayName }} Icon</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'custom-icon',
    'icon' => '{{ $iconName }}',
    'size' => '16',
    'animation' => '{{ $animation }}',
    'color' => '{{ $color }}'
])
                </div>
            </div>
            @endif
            @endforeach

        </div>
    </section>

    <!-- External SVG Files -->
    <section class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">External SVG Files</h2>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">

            @php
                // Dynamically scan for external SVG files
                $svgPath = public_path('images/svg');
                $svgFiles = [];

                if (is_dir($svgPath)) {
                    $files = scandir($svgPath);
                    foreach ($files as $file) {
                        if (pathinfo($file, PATHINFO_EXTENSION) === 'svg') {
                            $svgFiles[] = $file;
                        }
                    }
                }

                if (empty($svgFiles)) {
                    $svgFiles = ['example.svg']; // Placeholder for demo
                }
            @endphp

            @if(count($svgFiles) === 1 && $svgFiles[0] === 'example.svg')
            <!-- Placeholder when no external SVGs exist -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg border-2 border-dashed border-gray-300">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        <p class="text-sm">No external SVGs found</p>
                    </div>
                </div>
                <h3 class="font-semibold mb-2">Add External SVGs</h3>
                <div class="code-block text-xs">
// 1. Save SVG to: public/images/svg/filename.svg
// 2. Use with external-svg type
@include('components.animated-svg', [
    'type' => 'external-svg',
    'file' => 'filename.svg',
    'size' => '16',
    'animation' => 'breathing-svg'
])
                </div>
            </div>
            @else
            @foreach($svgFiles as $index => $svgFile)
            @php
                $animation = $animations[$index % count($animations)];
                $displayName = ucwords(str_replace(['-', '_', '.svg'], [' ', ' ', ''], $svgFile));
            @endphp

            <!-- {{ $displayName }} SVG -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', [
                        'type' => 'external-svg',
                        'file' => $svgFile,
                        'size' => '16',
                        'animation' => $animation
                    ])
                </div>
                <h3 class="font-semibold mb-2">{{ $displayName }}</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'external-svg',
    'file' => '{{ $svgFile }}',
    'size' => '16',
    'animation' => '{{ $animation }}'
])
                </div>
            </div>
            @endforeach
            @endif

        </div>
    </section>

    <!-- Inline Custom SVGs -->
    <section class="mb-12">
        <h2 class="text-3xl font-bold text-gray-900 mb-8">Inline Custom SVGs</h2>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">

            <!-- Star Icon -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', [
                        'type' => 'inline-custom',
                        'size' => '16',
                        'viewBox' => '0 0 24 24',
                        'color' => '#f59e0b',
                        'animation' => 'pulse-glow-svg',
                        'svgContent' => '<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>'
                    ])
                </div>
                <h3 class="font-semibold mb-2">Star Icon</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'inline-custom',
    'size' => '16',
    'viewBox' => '0 0 24 24',
    'color' => '#f59e0b',
    'animation' => 'pulse-glow-svg',
    'svgContent' => '<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>'
])
                </div>
            </div>

            <!-- Heart Icon -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', [
                        'type' => 'inline-custom',
                        'size' => '16',
                        'viewBox' => '0 0 24 24',
                        'color' => '#ec4899',
                        'animation' => 'heartbeat-svg',
                        'svgContent' => '<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>'
                    ])
                </div>
                <h3 class="font-semibold mb-2">Heart Icon</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'inline-custom',
    'size' => '16',
    'viewBox' => '0 0 24 24',
    'color' => '#ec4899',
    'animation' => 'heartbeat-svg',
    'svgContent' => '<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>'
])
                </div>
            </div>

            <!-- Lightning Icon -->
            <div class="demo-card bg-white rounded-xl p-6 shadow-lg">
                <div class="h-32 bg-gray-50 rounded-lg mb-4 flex items-center justify-center">
                    @include('components.animated-svg', [
                        'type' => 'inline-custom',
                        'size' => '16',
                        'viewBox' => '0 0 24 24',
                        'color' => '#eab308',
                        'animation' => 'glitch-svg',
                        'svgContent' => '<polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>'
                    ])
                </div>
                <h3 class="font-semibold mb-2">Lightning Icon</h3>
                <div class="code-block text-xs">
@include('components.animated-svg', [
    'type' => 'inline-custom',
    'size' => '16',
    'viewBox' => '0 0 24 24',
    'color' => '#eab308',
    'animation' => 'glitch-svg',
    'svgContent' => '<polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"/>'
])
                </div>
            </div>

        </div>
    </section>

    <!-- Instructions for Adding New Icons -->
    <section class="mb-12">
        <div class="bg-blue-50 rounded-xl p-8">
            <h2 class="text-2xl font-bold text-blue-900 mb-4">How to Add Your Own Icons</h2>

            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold text-blue-800 mb-3">Method 1: Custom Icon Component</h3>
                    <ol class="list-decimal list-inside space-y-2 text-blue-700">
                        <li>Download SVG from any source (Heroicons, Feather, etc.)</li>
                        <li>Create file: <code class="bg-blue-100 px-2 py-1 rounded">resources/views/components/svg/icons/your-icon.blade.php</code></li>
                        <li>Paste SVG code with color variable: <code class="bg-blue-100 px-2 py-1 rounded">stroke="{{ $color ?? 'currentColor' }}"</code></li>
                        <li>Use in pages with the custom-icon type</li>
                    </ol>
                </div>

                <div>
                    <h3 class="text-lg font-semibold text-blue-800 mb-3">Method 2: External SVG File</h3>
                    <ol class="list-decimal list-inside space-y-2 text-blue-700">
                        <li>Save SVG file to: <code class="bg-blue-100 px-2 py-1 rounded">public/images/svg/filename.svg</code></li>
                        <li>Use with external-svg type</li>
                        <li>Reference by filename</li>
                        <li>Apply filters and animations</li>
                    </ol>
                </div>
            </div>

            <div class="mt-6 p-4 bg-blue-100 rounded-lg">
                <p class="text-blue-800 text-sm">
                    <strong>💡 Tip:</strong> After adding new icons, they will automatically appear in this demo page when you refresh.
                    Click any code block to copy it to your clipboard!
                </p>
            </div>
        </div>
    </section>

</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const pageToggle = document.getElementById('pageToggle');
    const demoContent = document.getElementById('demoContent');
    
    // Load saved state
    const isEnabled = localStorage.getItem('svgDemoEnabled') !== 'false';
    pageToggle.checked = isEnabled;
    demoContent.style.display = isEnabled ? 'block' : 'none';
    
    // Handle toggle
    pageToggle.addEventListener('change', function() {
        const enabled = this.checked;
        localStorage.setItem('svgDemoEnabled', enabled);
        demoContent.style.display = enabled ? 'block' : 'none';
        
        if (!enabled) {
            alert('Demo page disabled. Refresh to see the change.');
        }
    });
    
    // Copy code functionality
    document.querySelectorAll('.code-block').forEach(block => {
        block.addEventListener('click', function() {
            const text = this.textContent.trim();
            navigator.clipboard.writeText(text).then(() => {
                // Show copied feedback
                const original = this.style.backgroundColor;
                this.style.backgroundColor = '#10b981';
                setTimeout(() => {
                    this.style.backgroundColor = original;
                }, 200);
            });
        });
    });
});
</script>
@endpush

@endsection
