<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-clipboard-document-list class="w-5 h-5 text-primary-600 mr-2" />
                การมอบหมาย
            </div>
        </x-slot>

        <!-- Filter Section -->
        <div class="mb-6">
            <div class="flex flex-wrap gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ระดับชั้น/ห้อง</label>
                    <select class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        <option value="">ทั้งหมด</option>
                        @foreach($this->getClasses() as $class)
                            <option value="{{ $class['value'] }}">{{ $class['label'] }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">รายวิชา</label>
                    <select class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        <option value="">ทั้งหมด</option>
                        @foreach($this->getSubjects() as $subject)
                            <option value="{{ $subject['value'] }}">{{ $subject['label'] }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">สถานะ</label>
                    <select class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        <option value="">ทั้งหมด</option>
                        @foreach($this->getStatusOptions() as $status)
                            <option value="{{ $status['value'] }}">{{ $status['label'] }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button class="bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md transition-colors">
                        <x-heroicon-o-magnifying-glass class="w-4 h-4 mr-2 inline" />
                        ค้นหา
                    </button>
                </div>
            </div>
            
            <!-- Assignments Table -->
            <div class="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg shadow">
                <table class="min-w-full">
                    <thead>
                        <tr class="bg-gray-100 dark:bg-gray-700">
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">วันที่</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">วิชา</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">บทเรียน</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">ระดับชั้น</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">ห้องเรียน</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">นักเรียนทั้งหมด</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">ทำแล้วเสร็จ</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">สถานะ</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 dark:text-gray-300 uppercase tracking-wider">จัดการ</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                        @foreach($this->getAssignments() as $assignment)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $assignment['date'] }}</td>
                                <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $assignment['subject'] }}</td>
                                <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $assignment['lesson'] }}</td>
                                <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $assignment['grade'] }}</td>
                                <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $assignment['class'] }}</td>
                                <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $assignment['total_students'] }}</td>
                                <td class="py-3 px-4 text-sm text-gray-900 dark:text-gray-100">{{ $assignment['completed'] }}</td>
                                <td class="py-3 px-4 text-sm">
                                    <span class="px-2 py-1 text-xs rounded-full
                                        @if($assignment['status_color'] === 'yellow') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200
                                        @elseif($assignment['status_color'] === 'green') bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                        @elseif($assignment['status_color'] === 'red') bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200
                                        @endif">
                                        {{ $assignment['status_label'] }}
                                    </span>
                                </td>
                                <td class="py-3 px-4 text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-primary-600 hover:text-primary-800 dark:text-primary-400 dark:hover:text-primary-200">
                                            <x-heroicon-o-pencil class="w-4 h-4" />
                                        </button>
                                        <button class="text-success-600 hover:text-success-800 dark:text-success-400 dark:hover:text-success-200">
                                            <x-heroicon-o-eye class="w-4 h-4" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="mt-4 flex justify-between items-center">
                <div class="text-sm text-gray-600 dark:text-gray-400">แสดง 1-3 จาก 10 รายการ</div>
                <div class="flex space-x-1">
                    <button class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50" disabled>
                        <x-heroicon-o-chevron-left class="w-4 h-4" />
                    </button>
                    <button class="bg-primary-600 text-white px-3 py-1 rounded-md">1</button>
                    <button class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600">2</button>
                    <button class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600">3</button>
                    <button class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-3 py-1 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600">
                        <x-heroicon-o-chevron-right class="w-4 h-4" />
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Add New Assignment Section -->
        <div class="mt-8">
            <h3 class="font-medium text-lg mb-4 text-gray-900 dark:text-white">เพิ่มการมอบหมายใหม่</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-4">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ประเภทการมอบหมาย</label>
                            <select class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                                <option value="">เลือกประเภท</option>
                                @foreach($this->getAssignmentTypes() as $type)
                                    <option value="{{ $type['value'] }}">{{ $type['label'] }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ชื่อการมอบหมาย</label>
                            <input type="text" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">รายวิชา</label>
                            <select class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                                <option value="">เลือกวิชา</option>
                                @foreach($this->getSubjects() as $subject)
                                    <option value="{{ $subject['value'] }}">{{ $subject['label'] }}</option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">กำหนดส่ง</label>
                            <input type="date" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50">
                        </div>

                        <button class="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md transition-colors">
                            <x-heroicon-o-plus class="w-4 h-4 mr-2 inline" />
                            เพิ่มการมอบหมาย
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
