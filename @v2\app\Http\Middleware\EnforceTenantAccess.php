<?php

namespace App\Http\Middleware;

use Closure;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnforceTenantAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();
        
        if (!$user) {
            return $next($request);
        }

        // Get current tenant from Filament
        $currentTenant = Filament::getTenant();
        
        if (!$currentTenant) {
            return $next($request);
        }

        // Super admins (team_id = null) can access any tenant
        if ($user->team_id === null && $user->hasRole('super_admin')) {
            return $next($request);
        }

        // Regular users can only access their own team
        if ($user->team_id !== $currentTenant->id) {
            // Redirect to their own team or show 403
            if ($user->team) {
                return redirect()->route('filament.admin.pages.dashboard', [
                    'tenant' => $user->team->slug
                ]);
            }
            
            abort(403, 'You can only access your own team.');
        }

        return $next($request);
    }
}
