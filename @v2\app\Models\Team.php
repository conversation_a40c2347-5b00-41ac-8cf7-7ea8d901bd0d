<?php

namespace App\Models;

use Filament\Models\Contracts\HasCurrentTenantLabel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use App\Traits\HasSubscriptionLimits;

/**
 * 
 *
 * @property int $id
 * @property string $name
 * @property string|null $slug
 * @property string|null $description
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Blog\Author> $blogAuthors
 * @property-read int|null $blog_authors_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Blog\Category> $blogCategories
 * @property-read int|null $blog_categories_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Blog\Link> $blogLinks
 * @property-read int|null $blog_links_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Blog\Post> $blogPosts
 * @property-read int|null $blog_posts_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop\Brand> $brands
 * @property-read int|null $brands_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop\Category> $categories
 * @property-read int|null $categories_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop\Customer> $customers
 * @property-read int|null $customers_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop\Order> $orders
 * @property-read int|null $orders_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop\Product> $products
 * @property-read int|null $products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User> $users
 * @property-read int|null $users_count
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Team whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Team extends Model implements HasCurrentTenantLabel
{
    use HasFactory, HasSubscriptionLimits;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function getCurrentTenantLabel(): string
    {
        return 'Current School';
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    // Shop relationships
    public function customers(): HasMany
    {
        return $this->hasMany(\App\Models\Shop\Customer::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(\App\Models\Shop\Product::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(\App\Models\Shop\Order::class);
    }

    public function brands(): HasMany
    {
        return $this->hasMany(\App\Models\Shop\Brand::class);
    }

    public function categories(): HasMany
    {
        return $this->hasMany(\App\Models\Shop\Category::class);
    }

    // Blog relationships
    public function blogAuthors(): HasMany
    {
        return $this->hasMany(\App\Models\Blog\Author::class);
    }

    public function blogPosts(): HasMany
    {
        return $this->hasMany(\App\Models\Blog\Post::class);
    }

    public function blogCategories(): HasMany
    {
        return $this->hasMany(\App\Models\Blog\Category::class);
    }

    public function blogLinks(): HasMany
    {
        return $this->hasMany(\App\Models\Blog\Link::class);
    }

    // Academic relationships
    public function subjects(): HasMany
    {
        return $this->hasMany(\App\Models\Subject::class);
    }

    public function classRooms(): HasMany
    {
        return $this->hasMany(\App\Models\ClassRoom::class);
    }

    public function books(): HasMany
    {
        return $this->hasMany(\App\Models\Book::class);
    }

    public function courses(): HasMany
    {
        return $this->hasMany(\App\Models\Course::class);
    }

    public function teachingSchedules(): HasMany
    {
        return $this->hasMany(\App\Models\TeachingSchedule::class);
    }

    public function tasks(): HasMany
    {
        return $this->hasMany(\App\Models\Task::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(\App\Models\Assignment::class);
    }

    public function assignmentSubmissions(): HasMany
    {
        return $this->hasMany(\App\Models\AssignmentSubmission::class);
    }

    public function liveVideos(): HasMany
    {
        return $this->hasMany(\App\Models\LiveVideo::class);
    }

    public function subscriptions(): HasManyThrough
    {
        return $this->hasManyThrough(
            \TomatoPHP\FilamentSubscriptions\Models\Subscription::class,
            \App\Models\User::class,
            'team_id', // Foreign key on users table
            'subscriber_id', // Foreign key on subscriptions table
            'id', // Local key on teams table
            'id' // Local key on users table
        )->where('subscriber_type', 'App\Models\User');
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }

    /**
     * Check if the team can be deleted (no users assigned)
     */
    public function canBeDeleted(): bool
    {
        return $this->users()->count() === 0;
    }

    /**
     * Get the team's admin users
     */
    public function admins()
    {
        return $this->users()->whereHas('roles', function ($query) {
            $query->where('name', 'team_admin');
        });
    }

    /**
     * Scope to only active teams
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
