<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get locale from various sources in order of priority
        $locale = $this->getLocale($request);
        
        // Set the application locale
        App::setLocale($locale);
        
        // Store in session for future requests
        Session::put('locale', $locale);

        return $next($request);
    }

    /**
     * Get the locale from various sources
     */
    private function getLocale(Request $request): string
    {
        $supportedLocales = ['en', 'th'];
        $defaultLocale = config('app.locale', 'en');

        // 1. Check if locale is explicitly set in session
        if (Session::has('locale')) {
            $sessionLocale = Session::get('locale');
            if (in_array($sessionLocale, $supportedLocales)) {
                return $sessionLocale;
            }
        }

        // 2. Check authenticated user's profile language preference
        if (auth()->check() && auth()->user()->profile && auth()->user()->profile->language) {
            $userLocale = auth()->user()->profile->language;
            if (in_array($userLocale, $supportedLocales)) {
                return $userLocale;
            }
        }

        // 3. Check browser's preferred language
        $browserLocale = $request->getPreferredLanguage($supportedLocales);
        if ($browserLocale && in_array($browserLocale, $supportedLocales)) {
            return $browserLocale;
        }

        // 4. Fall back to default locale
        return $defaultLocale;
    }
}
