@extends('layouts.frontend')

@section('title', 'Pricing Plans')

@section('content')
<div class="bg-white">
    <!-- Hero section -->
    <div class="relative bg-gray-900">
        <div class="absolute inset-0">
            <img class="w-full h-full object-cover" src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1471&q=80" alt="">
            <div class="absolute inset-0 bg-gray-900 opacity-75"></div>
        </div>
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">Choose Your Plan</h1>
            <p class="mt-6 text-xl text-gray-300 max-w-3xl">
                Start your educational journey with our flexible pricing plans. Whether you're an individual learner, teacher, or school, we have the perfect plan for you.
            </p>
        </div>
    </div>

    <!-- Individual Plans -->
    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">Individual Plans</h2>
            <p class="mt-4 text-lg text-gray-600">Perfect for students and teachers who want personal access</p>
        </div>

        <div class="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0 xl:grid-cols-2">
            @foreach($individualPlans->where('invoice_interval', 'month') as $plan)
            <div class="border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200 {{ str_contains($plan->slug, 'teacher') ? 'ring-2 ring-blue-500' : '' }}">
                @if(str_contains($plan->slug, 'teacher'))
                <div class="bg-blue-500 text-white text-center py-2 rounded-t-lg">
                    <span class="text-sm font-medium">Most Popular</span>
                </div>
                @endif
                
                <div class="p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $plan->name }}</h3>
                    <p class="mt-4 text-sm text-gray-500">{{ strip_tags(str_replace(['Features:', '•'], ['', ''], $plan->description)) }}</p>
                    <p class="mt-8">
                        <span class="text-4xl font-extrabold text-gray-900">${{ number_format($plan->price, 2) }}</span>
                        <span class="text-base font-medium text-gray-500">/{{ $plan->invoice_interval }}</span>
                    </p>
                    @if($plan->trial_period > 0)
                    <p class="mt-2 text-sm text-green-600 font-medium">{{ $plan->trial_period }}-day free trial</p>
                    @endif
                    <a href="{{ route('pricing.select', $plan->slug) }}" class="mt-8 block w-full bg-blue-600 border border-transparent rounded-md py-2 text-sm font-semibold text-white text-center hover:bg-blue-700">
                        Get Started
                    </a>
                </div>
                
                <div class="pt-6 pb-8 px-6">
                    <h4 class="text-xs font-medium text-gray-900 tracking-wide uppercase">What's included</h4>
                    <ul class="mt-6 space-y-4">
                        @php
                            $features = explode('Features:', $plan->description)[1] ?? '';
                            $featureList = array_filter(array_map('trim', explode('•', $features)));
                        @endphp
                        @foreach($featureList as $feature)
                        <li class="flex space-x-3">
                            <svg class="flex-shrink-0 h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            <span class="text-sm text-gray-500">{{ $feature }}</span>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Annual Plans -->
        @if($individualPlans->where('invoice_interval', 'month')->count() > 0)
        <div class="mt-12 text-center">
            <h3 class="text-xl font-semibold text-gray-900">Save with Annual Plans</h3>
            <p class="mt-2 text-gray-600">Get 2 months free when you pay annually</p>
        </div>

        <div class="mt-8 space-y-4 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto">
            @foreach($individualPlans->where('invoice_interval', 'month')->where('invoice_period', 12) as $plan)
            <div class="border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200 bg-gray-50">
                <div class="p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $plan->name }}</h3>
                    <p class="mt-8">
                        <span class="text-4xl font-extrabold text-gray-900">${{ number_format($plan->price, 2) }}</span>
                        <span class="text-base font-medium text-gray-500">/year</span>
                    </p>
                    <p class="mt-2 text-sm text-green-600 font-medium">Save ${{ number_format(($plan->price / 12 * 14) - $plan->price, 2) }} per year</p>
                    <a href="{{ route('pricing.select', $plan->slug) }}" class="mt-8 block w-full bg-gray-800 border border-transparent rounded-md py-2 text-sm font-semibold text-white text-center hover:bg-gray-900">
                        Choose Annual
                    </a>
                </div>
            </div>
            @endforeach
        </div>
        @endif
    </div>

    <!-- School Plans -->
    <div class="bg-gray-50">
        <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">School Plans</h2>
                <p class="mt-4 text-lg text-gray-600">Comprehensive solutions for educational institutions</p>
            </div>

            <div class="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto">
                @foreach($schoolPlans->where('invoice_interval', 'month')->where('invoice_period', 1) as $plan)
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200 {{ str_contains($plan->slug, 'basic') ? 'ring-2 ring-green-500' : '' }}">
                    @if(str_contains($plan->slug, 'basic'))
                    <div class="bg-green-500 text-white text-center py-2 rounded-t-lg">
                        <span class="text-sm font-medium">Recommended</span>
                    </div>
                    @endif
                    
                    <div class="p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $plan->name }}</h3>
                        <p class="mt-4 text-sm text-gray-500">{{ strip_tags(str_replace(['Features:', '•'], ['', ''], $plan->description)) }}</p>
                        <p class="mt-8">
                            <span class="text-4xl font-extrabold text-gray-900">${{ number_format($plan->price, 2) }}</span>
                            <span class="text-base font-medium text-gray-500">/{{ $plan->invoice_interval }}</span>
                        </p>
                        @if($plan->trial_period > 0)
                        <p class="mt-2 text-sm text-green-600 font-medium">{{ $plan->trial_period }}-day free trial</p>
                        @endif
                        <a href="{{ route('pricing.select', $plan->slug) }}" class="mt-8 block w-full bg-green-600 border border-transparent rounded-md py-2 text-sm font-semibold text-white text-center hover:bg-green-700">
                            Get Started
                        </a>
                    </div>
                    
                    <div class="pt-6 pb-8 px-6">
                        <h4 class="text-xs font-medium text-gray-900 tracking-wide uppercase">What's included</h4>
                        <ul class="mt-6 space-y-4">
                            @php
                                $features = explode('Features:', $plan->description)[1] ?? '';
                                $featureList = array_filter(array_map('trim', explode('•', $features)));
                            @endphp
                            @foreach($featureList as $feature)
                            <li class="flex space-x-3">
                                <svg class="flex-shrink-0 h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm text-gray-500">{{ $feature }}</span>
                            </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Additional Plans -->
            @if($additionalPlans->count() > 0)
            <div class="mt-16">
                <div class="text-center">
                    <h3 class="text-xl font-semibold text-gray-900">Add-on Plans</h3>
                    <p class="mt-2 text-gray-600">Expand your school's capacity</p>
                </div>

                <div class="mt-8 max-w-lg mx-auto">
                    @foreach($additionalPlans as $plan)
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $plan->name }}</h3>
                        <p class="mt-4 text-sm text-gray-500">{{ strip_tags(str_replace(['Features:', '•'], ['', ''], $plan->description)) }}</p>
                        <p class="mt-8">
                            <span class="text-4xl font-extrabold text-gray-900">${{ number_format($plan->price, 2) }}</span>
                            <span class="text-base font-medium text-gray-500">/{{ $plan->invoice_interval }}</span>
                        </p>
                        <a href="{{ route('pricing.select', $plan->slug) }}" class="mt-8 block w-full bg-purple-600 border border-transparent rounded-md py-2 text-sm font-semibold text-white text-center hover:bg-purple-700">
                            Add to Plan
                        </a>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-3xl font-extrabold text-gray-900">Frequently Asked Questions</h2>
        </div>
        <div class="mt-12 max-w-3xl mx-auto">
            <dl class="space-y-6">
                <div>
                    <dt class="text-lg leading-6 font-medium text-gray-900">What's included in the free trial?</dt>
                    <dd class="mt-2 text-base text-gray-500">Individual plans come with a 14-day free trial that includes full access to all features. School plans include a 30-day free trial.</dd>
                </div>
                <div>
                    <dt class="text-lg leading-6 font-medium text-gray-900">Can I change my plan later?</dt>
                    <dd class="mt-2 text-base text-gray-500">Yes, you can upgrade or downgrade your plan at any time. Changes will be prorated and reflected in your next billing cycle.</dd>
                </div>
                <div>
                    <dt class="text-lg leading-6 font-medium text-gray-900">What payment methods do you accept?</dt>
                    <dd class="mt-2 text-base text-gray-500">We accept credit cards, bank transfers, and PromptPay QR code payments for your convenience.</dd>
                </div>
                <div>
                    <dt class="text-lg leading-6 font-medium text-gray-900">How does the parent account system work?</dt>
                    <dd class="mt-2 text-base text-gray-500">Each student account automatically includes one parent account at no extra cost. Parents can monitor their child's progress and receive updates.</dd>
                </div>
            </dl>
        </div>
    </div>
</div>
@endsection
