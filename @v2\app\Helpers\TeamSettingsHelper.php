<?php

if (!function_exists('get_team_settings')) {
    /**
     * Get settings for a specific team
     *
     * @param int|null $teamId
     * @param string|null $key
     * @param bool $fresh Force fresh data (bypass cache)
     * @return mixed
     */
    function get_team_settings($teamId = null, $key = null, $fresh = false)
    {
        $cacheKey = $teamId ? "settings_data.{$teamId}" : 'settings_data.global';

        if ($key) {
            $cacheKey .= ".{$key}";

            if ($fresh) {
                \Illuminate\Support\Facades\Cache::forget($cacheKey);
            }

            return \Illuminate\Support\Facades\Cache::remember($cacheKey, 3600, function () use ($teamId, $key) {
                $setting = \App\Models\AppSettings::where('team_id', $teamId)
                    ->where('key', $key)
                    ->first();

                return $setting ? $setting->value : null;
            });
        }

        $cacheKey .= '.all';

        if ($fresh) {
            \Illuminate\Support\Facades\Cache::forget($cacheKey);
        }

        return \Illuminate\Support\Facades\Cache::remember($cacheKey, 3600, function () use ($teamId) {
            $settings = \App\Models\AppSettings::where('team_id', $teamId)->get();

            $result = [];
            foreach ($settings as $setting) {
                $keys = explode('.', $setting->key);
                $tab = $setting->tab;

                if (!isset($result[$tab])) {
                    $result[$tab] = [];
                }

                $current = &$result[$tab];

                foreach ($keys as $keyPart) {
                    if (!isset($current[$keyPart])) {
                        $current[$keyPart] = [];
                    }
                    $current = &$current[$keyPart];
                }

                $current = $setting->value;
            }

            return $result;
        });
    }
}

if (!function_exists('get_current_team_settings')) {
    /**
     * Get settings for the current team context
     *
     * @param string|null $key
     * @param bool $fresh Force fresh data (bypass cache)
     * @return mixed
     */
    function get_current_team_settings($key = null, $fresh = false)
    {
        $currentTenant = \Filament\Facades\Filament::getTenant();
        $teamId = $currentTenant ? $currentTenant->id : null;

        return get_team_settings($teamId, $key, $fresh);
    }
}

if (!function_exists('set_team_setting')) {
    /**
     * Set a setting for a specific team
     *
     * @param int|null $teamId
     * @param string $tab
     * @param string $key
     * @param mixed $value
     * @return void
     */
    function set_team_setting($teamId, $tab, $key, $value)
    {
        \App\Models\AppSettings::updateOrCreate(
            [
                'tab' => $tab,
                'key' => $key,
                'team_id' => $teamId
            ],
            ['value' => $value]
        );

        // Clear all related cache entries aggressively
        clear_team_settings_cache($teamId);

        // Also clear the original package cache keys
        $originalCacheKey = $teamId ? "settings_data.{$teamId}" : 'settings_data';
        \Illuminate\Support\Facades\Cache::forget("{$originalCacheKey}.{$tab}.{$key}");
        \Illuminate\Support\Facades\Cache::forget("{$originalCacheKey}.all");
        \Illuminate\Support\Facades\Cache::forget('settings_data.all');

        // Clear any potential nested cache keys
        \Illuminate\Support\Facades\Cache::forget("settings_data.{$teamId}.{$key}");
        \Illuminate\Support\Facades\Cache::forget("settings_data.{$teamId}.all");
    }
}

if (!function_exists('clear_team_settings_cache')) {
    /**
     * Clear settings cache for a specific team (aggressive cache clearing)
     *
     * @param int|null $teamId
     * @return void
     */
    function clear_team_settings_cache($teamId = null)
    {
        $cacheKeys = [
            $teamId ? "settings_data.{$teamId}" : 'settings_data.global',
            $teamId ? "settings_data.{$teamId}.all" : 'settings_data.global.all',
            'settings_data.all',
        ];

        // Clear main cache keys
        foreach ($cacheKeys as $key) {
            \Illuminate\Support\Facades\Cache::forget($key);
        }

        // Clear individual setting caches
        $settings = \App\Models\AppSettings::where('team_id', $teamId)->get();
        foreach ($settings as $setting) {
            $individualKeys = [
                $teamId ? "settings_data.{$teamId}.{$setting->key}" : "settings_data.global.{$setting->key}",
                $teamId ? "settings_data.{$teamId}.{$setting->tab}.{$setting->key}" : "settings_data.global.{$setting->tab}.{$setting->key}",
                "settings_data.{$setting->tab}.{$setting->key}",
            ];

            foreach ($individualKeys as $key) {
                \Illuminate\Support\Facades\Cache::forget($key);
            }
        }

        // Clear any potential app-settings plugin cache
        \Illuminate\Support\Facades\Cache::forget('app_settings_cache');
        \Illuminate\Support\Facades\Cache::forget("app_settings_cache_{$teamId}");
    }
}

if (!function_exists('clear_all_settings_cache')) {
    /**
     * Clear all settings cache (nuclear option)
     *
     * @return void
     */
    function clear_all_settings_cache()
    {
        // Get all teams and clear their caches
        $teams = \App\Models\Team::all();
        foreach ($teams as $team) {
            clear_team_settings_cache($team->id);
        }

        // Clear global cache
        clear_team_settings_cache(null);

        // Clear Filament-specific cache
        $filamentCacheKeys = [
            'filament*',
            'livewire*',
            'spatie.permission.cache',
            'spatie.translatable*',
        ];

        foreach ($filamentCacheKeys as $pattern) {
            \Illuminate\Support\Facades\Cache::forget($pattern);
        }

        // Clear any remaining cache patterns
        \Illuminate\Support\Facades\Cache::flush();
    }
}

if (!function_exists('clear_laravel_cache')) {
    /**
     * Clear all Laravel framework cache
     *
     * @return array Results of cache clearing operations
     */
    function clear_laravel_cache()
    {
        $results = [];

        try {
            \Illuminate\Support\Facades\Artisan::call('config:clear');
            $results['config'] = 'cleared';
        } catch (\Exception $e) {
            $results['config'] = 'failed: ' . $e->getMessage();
        }

        try {
            \Illuminate\Support\Facades\Artisan::call('route:clear');
            $results['route'] = 'cleared';
        } catch (\Exception $e) {
            $results['route'] = 'failed: ' . $e->getMessage();
        }

        try {
            \Illuminate\Support\Facades\Artisan::call('view:clear');
            $results['view'] = 'cleared';
        } catch (\Exception $e) {
            $results['view'] = 'failed: ' . $e->getMessage();
        }

        try {
            \Illuminate\Support\Facades\Artisan::call('cache:clear');
            $results['cache'] = 'cleared';
        } catch (\Exception $e) {
            $results['cache'] = 'failed: ' . $e->getMessage();
        }

        // Clear OPcache if available
        if (function_exists('opcache_reset')) {
            try {
                opcache_reset();
                $results['opcache'] = 'cleared';
            } catch (\Exception $e) {
                $results['opcache'] = 'failed: ' . $e->getMessage();
            }
        } else {
            $results['opcache'] = 'not available';
        }

        return $results;
    }
}
