<?php

namespace App\Filament\Clusters\Products\Resources;

use App\Filament\Clusters\Products;
use App\Filament\Clusters\Products\Resources\BrandResource\RelationManagers\ProductsRelationManager;
use App\Models\Shop\Product;
use App\Models\Shop\Brand;
use App\Models\Shop\Category;
use Filament\Forms;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class DigitalProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $cluster = Products::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-o-cloud-arrow-down';

    protected static ?string $navigationLabel = 'Digital Products';

    protected static ?int $navigationSort = 1;

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'products';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('product_type', 'digital');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                        if ($operation !== 'create') {
                                            return;
                                        }

                                        $set('slug', Str::slug($state));
                                        // Auto-set digital product defaults
                                        $set('requires_shipping', false);
                                        $set('qty', 999999);
                                    }),

                                Forms\Components\TextInput::make('slug')
                                    ->disabled()
                                    ->dehydrated()
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(Product::class, 'slug', ignoreRecord: true),

                                Forms\Components\MarkdownEditor::make('description')
                                    ->columnSpan('full'),

                                Forms\Components\Hidden::make('product_type')
                                    ->default('digital'),

                                Forms\Components\Hidden::make('requires_shipping')
                                    ->default(false),

                                Forms\Components\Hidden::make('qty')
                                    ->default(999999),
                            ])
                            ->columns(2),

                        Forms\Components\Section::make('Digital Product Content')
                            ->schema([
                                Forms\Components\Select::make('digital_format')
                                    ->label('Primary Format')
                                    ->options(Product::getDigitalFormats())
                                    ->searchable(),

                                Forms\Components\CheckboxList::make('digital_file_types')
                                    ->label('Available File Types')
                                    ->options([
                                        'pdf' => 'PDF Document',
                                        'epub' => 'EPUB eBook',
                                        'docx' => 'Word Document',
                                        'xlsx' => 'Excel Spreadsheet',
                                        'pptx' => 'PowerPoint Presentation',
                                        'mp3' => 'Audio File (MP3)',
                                        'mp4' => 'Video File (MP4)',
                                        'zip' => 'Archive (ZIP)',
                                        'exe' => 'Software (EXE)',
                                        'apk' => 'Android App (APK)',
                                    ])
                                    ->columns(2)
                                    ->helperText('Select all file formats included with this digital product'),

                                SpatieMediaLibraryFileUpload::make('digital_files')
                                    ->label('Digital Files')
                                    ->collection('digital-files')
                                    ->multiple()
                                    ->maxFiles(10)
                                    ->acceptedFileTypes([
                                        'application/pdf',
                                        'application/epub+zip',
                                        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                                        'audio/mpeg',
                                        'video/mp4',
                                        'application/zip',
                                        'application/x-msdownload',
                                        'application/vnd.android.package-archive',
                                    ])
                                    ->helperText('Upload the digital files that customers will receive')
                                    ->columnSpan('full'),

                                Forms\Components\TextInput::make('file_size_mb')
                                    ->label('Total File Size (MB)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->helperText('Total size of all digital files'),

                                Forms\Components\TextInput::make('version')
                                    ->label('Version')
                                    ->helperText('Version number for software/digital content'),

                                Forms\Components\DatePicker::make('last_updated')
                                    ->label('Last Updated')
                                    ->default(now())
                                    ->helperText('When was this digital content last updated'),
                            ])
                            ->columns(2),

                        Forms\Components\Section::make('Digital Licensing & Access')
                            ->schema([
                                Forms\Components\TextInput::make('download_limit')
                                    ->label('Download Limit')
                                    ->numeric()
                                    ->helperText('Maximum number of downloads per purchase (leave empty for unlimited)'),

                                Forms\Components\TextInput::make('download_expiry_days')
                                    ->label('Download Expiry (Days)')
                                    ->numeric()
                                    ->helperText('Number of days after purchase when download links expire'),

                                Forms\Components\Toggle::make('requires_license_key')
                                    ->label('Requires License Key')
                                    ->helperText('Generate license keys for this digital product'),

                                Forms\Components\MarkdownEditor::make('license_terms')
                                    ->label('License Terms')
                                    ->helperText('Terms and conditions for using this digital product')
                                    ->columnSpan('full')
                                    ->visible(fn (Forms\Get $get): bool => $get('requires_license_key')),

                                Forms\Components\KeyValue::make('specifications')
                                    ->label('Product Specifications')
                                    ->keyLabel('Specification')
                                    ->valueLabel('Value')
                                    ->helperText('Product specifications (e.g., OS: Windows 10, RAM: 4GB, Format: PDF)')
                                    ->columnSpan('full'),
                            ])
                            ->columns(2),

                        Forms\Components\Section::make('Images')
                            ->schema([
                                SpatieMediaLibraryFileUpload::make('media')
                                    ->collection('product-images')
                                    ->multiple()
                                    ->maxFiles(5)
                                    ->hiddenLabel(),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Digital Product Variants')
                            ->schema([
                                Forms\Components\Group::make()
                                    ->schema([
                                        Forms\Components\TextInput::make('option1_label')
                                            ->label('Option 1 Label')
                                            ->placeholder('e.g., Version, Platform, License Type')
                                            // ->helperText('What does Option 1 represent? (e.g., Version)')
                                            ->live(onBlur: true)
                                            ->columnSpan(1),

                                        Forms\Components\TextInput::make('option2_label')
                                            ->label('Option 2 Label (Optional)')
                                            ->placeholder('e.g., Platform, Language, Format')
                                            // ->helperText('What does Option 2 represent? (e.g., Platform)')
                                            ->live(onBlur: true)
                                            ->columnSpan(1),
                                    ])
                                    ->columns(2)
                                    ->columnSpan('full'),

                                Forms\Components\Group::make()
                                    ->schema([
                                        Forms\Components\TagsInput::make('option1_variants')
                                            ->label(function (Forms\Get $get) {
                                                return ($get('option1_label') ?: 'Option 1') . ' Variants';
                                            })
                                            ->placeholder('e.g., Personal, Commercial, Enterprise')
                                            // ->helperText('Enter variant values separated by commas or press Enter')
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
                                                static::generateVariantCombinations($set, $get);
                                            })
                                            ->columnSpan(1),

                                        Forms\Components\TagsInput::make('option2_variants')
                                            ->label(function (Forms\Get $get) {
                                                return ($get('option2_label') ?: 'Option 2') . ' Variants';
                                            })
                                            ->placeholder('e.g., Windows, Mac, Linux')
                                            // ->helperText('Enter variant values separated by commas or press Enter')
                                            ->live(onBlur: true)
                                            ->afterStateUpdated(function (Forms\Set $set, Forms\Get $get) {
                                                static::generateVariantCombinations($set, $get);
                                            })
                                            ->columnSpan(1),
                                    ])
                                    ->columns(2)
                                    ->columnSpan('full'),

                                // Forms\Components\Actions::make([
                                //     Forms\Components\Actions\Action::make('generate_variants')
                                //         ->label('Generate Variant Combinations')
                                //         ->icon('heroicon-o-squares-plus')
                                //         ->color('primary')
                                //         ->action(function (Forms\Set $set, Forms\Get $get) {
                                //             static::generateVariantCombinations($set, $get);
                                //         })
                                //         ->visible(function (Forms\Get $get) {
                                //             $option1 = $get('option1_variants');
                                //             return !empty($option1);
                                //         }),
                                // ])
                                //     ->columnSpan('full'),

                                Forms\Components\Group::make()
                                    ->schema([
                                        Forms\Components\Group::make()
                                            ->schema([
                                                Forms\Components\Grid::make(2)
                                                    ->schema([
                                                        Forms\Components\Placeholder::make('variants_title')
                                                            ->label('')
                                                            ->content(function (Forms\Get $get) {
                                                                $option1 = $get('option1_label') ?: 'Option 1';
                                                                $option2 = $get('option2_label') ?: 'Option 2';
                                                                return "Digital Product Variants ({$option1}" . ($option2 ? " × {$option2}" : "") . ")";
                                                            })
                                                            ->extraAttributes([
                                                                'style' => 'font-weight: 600; font-size: 1.125rem; color: #374151; margin-bottom: 0;',
                                                                'class' => 'text-lg font-semibold text-gray-700'
                                                            ])
                                                            ->columnSpan(1),

                                                        Forms\Components\Actions::make([
                                                            Forms\Components\Actions\Action::make('set_all_base_price')
                                                                ->label('Set All to Base Price')
                                                                ->icon('heroicon-o-currency-dollar')
                                                                ->color('success')
                                                                ->size('sm')
                                                                ->action(function (Forms\Set $set, Forms\Get $get) {
                                                                    $variants = $get('product_variants') ?? [];
                                                                    $basePrice = $get('price') ?? 0;

                                                                    foreach (array_keys($variants) as $index) {
                                                                        $set("product_variants.{$index}.price", $basePrice);
                                                                    }
                                                                })
                                                                ->requiresConfirmation()
                                                                ->modalHeading('Set All Variants to Base Price')
                                                                ->modalDescription('This will set all variant prices to match the base product price.')
                                                                ->modalSubmitActionLabel('Set All Prices')
                                                                ->visible(function (Forms\Get $get) {
                                                                    $variants = $get('product_variants') ?? [];
                                                                    return count($variants) > 0;
                                                                }),
                                                        ])
                                                            ->alignment('end')
                                                            ->columnSpan(1),
                                                    ])
                                                    ->columnSpan('full'),
                                            ])
                                            ->columnSpan('full'),

                                        Forms\Components\Repeater::make('product_variants')
                                            ->label('')
                                            ->schema([
                                                // Hidden fields to store variant data
                                                Forms\Components\Hidden::make('sku')
                                                    ->default(function (Forms\Get $get) {
                                                        $baseSku = $get('../../sku') ?: 'DIGITAL';
                                                        $index = $get('../../product_variants') ? count($get('../../product_variants')) : 0;
                                                        return $baseSku . '-V' . ($index + 1);
                                                    }),

                                                Forms\Components\Hidden::make('option1'),
                                                Forms\Components\Hidden::make('option2'),
                                                Forms\Components\Hidden::make('is_available')
                                                    ->default(true),

                                                Forms\Components\Grid::make(2)
                                                    ->schema([
                                                        Forms\Components\Placeholder::make('options_display')
                                                            ->label('Options')
                                                            ->content(function (Forms\Get $get) {
                                                                $option1 = $get('option1') ?: '';
                                                                $option2 = $get('option2') ?: '';

                                                                if ($option1 && $option2) {
                                                                    return $option1 . ', ' . $option2;
                                                                } elseif ($option1) {
                                                                    return $option1;
                                                                }

                                                                return 'No options set';
                                                            })
                                                            ->columnSpan(1),

                                                        Forms\Components\TextInput::make('price')
                                                            ->label('Price')
                                                            ->numeric()
                                                            ->default(function (Forms\Get $get) {
                                                                return $get('../../price') ?: 0;
                                                            })
                                                            ->rules(['numeric', 'min:0'])
                                                            ->placeholder('0.00')
                                                            ->prefix('$')
                                                            ->columnSpan(1),
                                                    ]),
                                            ])
                                            ->defaultItems(0)
                                            ->addActionLabel('Add Digital Variant')
                                            ->helperText('Generated variants with options displayed as text for cleaner interface')
                                            ->columnSpan('full')
                                            ->collapsible(),
                                    ])
                                    ->columnSpan('full'),
                            ])
                            ->collapsible(),

                        Forms\Components\Section::make('Pricing')
                            ->schema([
                                Forms\Components\TextInput::make('price')
                                    ->label('Regular Price')
                                    ->numeric()
                                    ->rules(['regex:/^\d{1,6}(\.\d{0,2})?$/'])
                                    ->required(),

                                Forms\Components\TextInput::make('old_price')
                                    ->label('Compare at price')
                                    ->numeric()
                                    ->rules(['regex:/^\d{1,6}(\.\d{0,2})?$/'])
                                    ->required(),

                                Forms\Components\Hidden::make('cost')
                                    ->default(0),

                                Forms\Components\Repeater::make('wholesale_pricing')
                                    ->label('Volume Licensing Tiers')
                                    ->schema([
                                        Forms\Components\TextInput::make('min_quantity')
                                            ->label('Minimum Licenses')
                                            ->numeric()
                                            ->required()
                                            ->rules(['integer', 'min:1'])
                                            ->placeholder('e.g., 5'),

                                        Forms\Components\TextInput::make('discount_percentage')
                                            ->label('Discount %')
                                            ->numeric()
                                            ->required()
                                            ->rules(['numeric', 'min:0', 'max:100'])
                                            ->placeholder('e.g., 30')
                                            ->suffix('%')
                                            ->live(onBlur: true),

                                        Forms\Components\Placeholder::make('calculated_price')
                                            ->label('Price After Discount')
                                            ->content(function (Forms\Get $get) {
                                                $basePrice = $get('../../price') ?? 0;
                                                $discount = $get('discount_percentage') ?? 0;

                                                if ($basePrice > 0 && $discount > 0) {
                                                    $discountedPrice = $basePrice * (1 - ($discount / 100));
                                                    return '$' . number_format($discountedPrice, 2);
                                                }

                                                return '$0.00';
                                            }),

                                        Forms\Components\TextInput::make('label')
                                            ->label('Tier Label (Optional)')
                                            ->maxLength(255)
                                            ->placeholder('e.g., Team License'),
                                    ])
                                    ->columns(4)
                                    ->defaultItems(0)
                                    ->addActionLabel('Add Volume Tier')
                                    ->helperText('Set percentage discounts for volume licensing (e.g., 30% off for 5+ licenses)')
                                    ->columnSpan('full')
                                    ->collapsible(),
                            ])
                            ->columns(2),

                        Forms\Components\Section::make('Digital Inventory')
                            ->schema([
                                Forms\Components\TextInput::make('sku')
                                    ->label('SKU (Stock Keeping Unit)')
                                    ->unique(Product::class, 'sku', ignoreRecord: true)
                                    ->maxLength(255)
                                    ->required(),

                                Forms\Components\TextInput::make('barcode')
                                    ->label('Product Code (ISBN, etc.)')
                                    ->unique(Product::class, 'barcode', ignoreRecord: true)
                                    ->maxLength(255)
                                    ->helperText('For digital products, this could be ISBN for books or product code'),

                                Forms\Components\Hidden::make('security_stock')
                                    ->default(0),
                            ])
                            ->columns(2),

                        Forms\Components\Section::make('Digital Product Settings')
                            ->schema([
                                Forms\Components\Checkbox::make('backorder')
                                    ->label('This digital product can be refunded')
                                    ->helperText('Allow refunds for this digital product'),
                            ]),


                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Status')
                            ->schema([
                                Forms\Components\Toggle::make('is_visible')
                                    ->label('Visible')
                                    ->helperText('This product will be hidden from all sales channels.')
                                    ->default(true),

                                Forms\Components\DatePicker::make('published_at')
                                    ->label('Availability')
                                    ->default(now())
                                    ->required(),
                            ]),

                        Forms\Components\Section::make('Associations')
                            ->schema([
                                Forms\Components\Select::make('shop_brand_id')
                                    ->label('Brand')
                                    ->relationship('brand', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->hiddenOn(ProductsRelationManager::class)
                                    ->createOptionForm(static::getBrandCreateForm())
                                    ->createOptionUsing(function (array $data): int {
                                        $brand = Brand::create([
                                            'name' => $data['name'],
                                            'slug' => $data['slug'],
                                            'website' => $data['website'] ?? null,
                                            'description' => $data['description'] ?? null,
                                            'position' => $data['position'] ?? 0,
                                            'is_visible' => $data['is_visible'] ?? true,
                                            'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                                        ]);

                                        Notification::make()
                                            ->title('Brand created successfully!')
                                            ->success()
                                            ->send();

                                        return $brand->id;
                                    })
                                    ->suffixAction(
                                        Forms\Components\Actions\Action::make('openBrandManager')
                                            ->icon('heroicon-o-cog-6-tooth')
                                            ->tooltip('Manage Brands')
                                            ->url(fn (): string => BrandResource::getUrl('index'))
                                            ->openUrlInNewTab()
                                    ),

                                Forms\Components\Select::make('categories')
                                    ->label('Categories')
                                    ->relationship('categories', 'name')
                                    ->multiple()
                                    ->required()
                                    ->searchable()
                                    ->preload()
                                    ->createOptionForm(static::getCategoryCreateForm())
                                    ->createOptionUsing(function (array $data): int {
                                        $category = Category::create([
                                            'name' => $data['name'],
                                            'slug' => $data['slug'],
                                            'parent_id' => $data['parent_id'] ?? null,
                                            'description' => $data['description'] ?? null,
                                            'position' => $data['position'] ?? 0,
                                            'is_visible' => $data['is_visible'] ?? true,
                                            'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                                        ]);

                                        Notification::make()
                                            ->title('Category created successfully!')
                                            ->success()
                                            ->send();

                                        return $category->id;
                                    })
                                    ->suffixAction(
                                        Forms\Components\Actions\Action::make('openCategoryManager')
                                            ->icon('heroicon-o-cog-6-tooth')
                                            ->tooltip('Manage Categories')
                                            ->url(fn (): string => CategoryResource::getUrl('index'))
                                            ->openUrlInNewTab()
                                    ),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\SpatieMediaLibraryImageColumn::make('product-image')
                    ->label('Image')
                    ->collection('product-images'),

                Tables\Columns\TextColumn::make('name')
                    ->label('Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('brand.name')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\IconColumn::make('is_visible')
                    ->label('Visibility')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('price')
                    ->label('Regular Price')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('wholesale_pricing')
                    ->label('Volume Tiers')
                    ->formatStateUsing(function (?array $state): string {
                        if (!$state || empty($state)) return 'None';

                        $tiers = [];
                        foreach ($state as $tier) {
                            if (isset($tier['min_quantity']) && isset($tier['discount_percentage'])) {
                                $tiers[] = $tier['min_quantity'] . '+: ' . $tier['discount_percentage'] . '% off';
                            }
                        }

                        return !empty($tiers) ? implode(' | ', array_slice($tiers, 0, 2)) : 'None';
                    })
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),

                Tables\Columns\TextColumn::make('product_variants')
                    ->label('Variants')
                    ->formatStateUsing(function (?array $state): string {
                        if (!$state || empty($state)) return 'None';

                        $variants = [];
                        foreach ($state as $variant) {
                            $variantParts = [];

                            if (isset($variant['option1']) && !empty($variant['option1'])) {
                                $variantParts[] = $variant['option1'];
                            }
                            if (isset($variant['option2']) && !empty($variant['option2'])) {
                                $variantParts[] = $variant['option2'];
                            }

                            if (!empty($variantParts)) {
                                $variants[] = implode('/', $variantParts);
                            }
                        }

                        $count = count($variants);
                        if ($count === 0) return 'None';
                        if ($count <= 2) return implode(', ', $variants);

                        return implode(', ', array_slice($variants, 0, 2)) . ' +' . ($count - 2) . ' more';
                    })
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),

                Tables\Columns\TextColumn::make('sku')
                    ->label('SKU')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('digital_format')
                    ->label('Format')
                    ->badge()
                    ->color('info')
                    ->formatStateUsing(fn (?string $state): string => $state ? strtoupper($state) : 'N/A')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('version')
                    ->label('Version')
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),

                Tables\Columns\TextColumn::make('file_size_mb')
                    ->label('Size (MB)')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),

                Tables\Columns\IconColumn::make('requires_license_key')
                    ->label('License Key')
                    ->boolean()
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),

                Tables\Columns\TextColumn::make('download_limit')
                    ->label('Download Limit')
                    ->formatStateUsing(fn (?string $state): string => $state ? $state : 'Unlimited')
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),

                Tables\Columns\TextColumn::make('published_at')
                    ->label('Publish Date')
                    ->date()
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),

                Tables\Columns\TextColumn::make('last_updated')
                    ->label('Last Updated')
                    ->date()
                    ->sortable()
                    ->toggleable()
                    ->toggledHiddenByDefault(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_visible')
                    ->label('Visibility')
                    ->boolean()
                    ->trueLabel('Only visible products')
                    ->falseLabel('Only hidden products')
                    ->native(false),

                Tables\Filters\SelectFilter::make('brand')
                    ->relationship('brand', 'name')
                    ->searchable()
                    ->preload()
                    ->label('Brand'),

                Tables\Filters\SelectFilter::make('digital_format')
                    ->label('Format')
                    ->options(Product::getDigitalFormats()),

                Tables\Filters\TernaryFilter::make('requires_license_key')
                    ->label('License Key Required')
                    ->boolean()
                    ->trueLabel('Requires license key')
                    ->falseLabel('No license key required')
                    ->native(false),

                Tables\Filters\Filter::make('price')
                    ->form([
                        Forms\Components\TextInput::make('price_from')
                            ->numeric()
                            ->label('Price from'),
                        Forms\Components\TextInput::make('price_to')
                            ->numeric()
                            ->label('Price to'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                isset($data['price_from']) && $data['price_from'] !== '',
                                fn (Builder $query): Builder => $query->where('price', '>=', $data['price_from']),
                            )
                            ->when(
                                isset($data['price_to']) && $data['price_to'] !== '',
                                fn (Builder $query): Builder => $query->where('price', '<=', $data['price_to']),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => DigitalProductResource\Pages\ListDigitalProducts::route('/'),
            'create' => DigitalProductResource\Pages\CreateDigitalProduct::route('/create'),
            'edit' => DigitalProductResource\Pages\EditDigitalProduct::route('/{record}/edit'),
        ];
    }

    public static function getWidgets(): array
    {
        return [
            DigitalProductResource\Widgets\DigitalProductStats::class,
            DigitalProductResource\Widgets\BrandCategoryStats::class,
        ];
    }

    /**
     * Get brand creation form schema
     */
    public static function getBrandCreateForm(): array
    {
        return [
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255)
                        ->live(onBlur: true)
                        ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                            if ($operation !== 'create') {
                                return;
                            }
                            $set('slug', Str::slug($state));
                        })
                        ->placeholder('Brand name'),

                    Forms\Components\TextInput::make('slug')
                        ->disabled()
                        ->dehydrated()
                        ->required()
                        ->maxLength(255)
                        ->unique(Brand::class, 'slug'),

                    Forms\Components\TextInput::make('website')
                        ->label('Website URL')
                        ->url()
                        ->maxLength(255)
                        ->placeholder('https://example.com'),

                    Forms\Components\TextInput::make('position')
                        ->label('Sort Order')
                        ->numeric()
                        ->default(0)
                        ->helperText('Higher numbers appear first'),

                    Forms\Components\MarkdownEditor::make('description')
                        ->columnSpan('full')
                        ->placeholder('Brief description of the brand...'),

                    Forms\Components\Toggle::make('is_visible')
                        ->label('Visible to customers')
                        ->default(true)
                        ->columnSpan('full'),
                ]),
        ];
    }

    /**
     * Get category creation form schema
     */
    public static function getCategoryCreateForm(): array
    {
        return [
            Forms\Components\Grid::make(2)
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255)
                        ->live(onBlur: true)
                        ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                            if ($operation !== 'create') {
                                return;
                            }
                            $set('slug', Str::slug($state));
                        })
                        ->placeholder('Category name'),

                    Forms\Components\TextInput::make('slug')
                        ->disabled()
                        ->dehydrated()
                        ->required()
                        ->maxLength(255)
                        ->unique(Category::class, 'slug'),

                    Forms\Components\Select::make('parent_id')
                        ->label('Parent Category')
                        ->relationship('parent', 'name')
                        ->searchable()
                        ->preload()
                        ->placeholder('Select parent category (optional)'),

                    Forms\Components\TextInput::make('position')
                        ->label('Sort Order')
                        ->numeric()
                        ->default(0)
                        ->helperText('Higher numbers appear first'),

                    Forms\Components\MarkdownEditor::make('description')
                        ->columnSpan('full')
                        ->placeholder('Brief description of the category...'),

                    Forms\Components\Toggle::make('is_visible')
                        ->label('Visible to customers')
                        ->default(true)
                        ->columnSpan('full'),
                ]),
        ];
    }

    /**
     * Generate variant combinations from option inputs
     */
    public static function generateVariantCombinations(Forms\Set $set, Forms\Get $get): void
    {
        $option1Variants = $get('option1_variants') ?? [];
        $option2Variants = $get('option2_variants') ?? [];
        $baseSku = $get('sku') ?: 'DIGITAL';
        $basePrice = $get('price') ?? 0;

        if (empty($option1Variants)) {
            return;
        }

        $variants = [];
        $index = 1;

        if (empty($option2Variants)) {
            // Single dimension variants (only option1)
            foreach ($option1Variants as $option1) {
                $variants[] = [
                    'sku' => $baseSku . '-V' . $index,
                    'option1' => $option1,
                    'option2' => '',
                    'price' => $basePrice,
                    'is_available' => true,
                ];
                $index++;
            }
        } else {
            // Two dimension variants (option1 × option2)
            foreach ($option1Variants as $option1) {
                foreach ($option2Variants as $option2) {
                    $variants[] = [
                        'sku' => $baseSku . '-V' . $index,
                        'option1' => $option1,
                        'option2' => $option2,
                        'price' => $basePrice,
                        'is_available' => true,
                    ];
                    $index++;
                }
            }
        }

        $set('product_variants', $variants);
    }

}
