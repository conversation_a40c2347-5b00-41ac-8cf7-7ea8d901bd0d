<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('media', function (Blueprint $table) {
            $table->foreignId('created_by')->nullable()->after('team_id')->constrained('users')->nullOnDelete();
            $table->foreignId('user_id')->nullable()->after('created_by')->constrained('users')->nullOnDelete();
            $table->index(['created_by', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('media', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['user_id']);
            $table->dropIndex(['created_by', 'user_id']);
            $table->dropColumn(['created_by', 'user_id']);
        });
    }
};
