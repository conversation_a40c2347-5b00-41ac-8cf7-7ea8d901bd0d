@extends('frontend.layouts.app')

@section('title', $liveVideo->title)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('frontend.live-classes.index') }}" 
                       class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </a>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">{{ $liveVideo->title }}</h1>
                        <p class="mt-1 text-gray-600">{{ $liveVideo->teacher->name }}</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    @if($liveVideo->status === 'live')
                        <span class="bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-medium">
                            <i class="fas fa-circle mr-1 animate-pulse"></i>
                            LIVE NOW
                        </span>
                        <a href="{{ route('frontend.live-classes.join', $liveVideo) }}" 
                           class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                            <i class="fas fa-play mr-2"></i>
                            Join Live Class
                        </a>
                    @elseif($liveVideo->status === 'scheduled')
                        <span class="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium">
                            <i class="fas fa-clock mr-1"></i>
                            SCHEDULED
                        </span>
                    @elseif($liveVideo->status === 'ended')
                        <span class="bg-gray-100 text-gray-800 px-4 py-2 rounded-full text-sm font-medium">
                            <i class="fas fa-stop mr-1"></i>
                            ENDED
                        </span>
                        @if($liveVideo->recorded_media_id)
                            <a href="{{ route('live-videos.watch', $liveVideo) }}" 
                               class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                <i class="fas fa-play mr-2"></i>
                                Watch Recording
                            </a>
                        @endif
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Video Preview/Thumbnail -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
                    <div class="relative h-64 md:h-96 bg-gray-900">
                        @if($liveVideo->status === 'live')
                            <div class="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium z-10">
                                <i class="fas fa-circle mr-1 animate-pulse"></i>
                                LIVE
                            </div>
                        @endif

                        <!-- Placeholder thumbnail -->
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="text-center text-white">
                                <i class="fas fa-video text-8xl mb-4 opacity-50"></i>
                                @if($liveVideo->status === 'scheduled')
                                    <h3 class="text-xl font-semibold mb-2">Class Starts Soon</h3>
                                    <p class="text-gray-300">{{ $liveVideo->scheduled_start_time->format('M j, Y g:i A') }}</p>
                                    <div id="countdown" class="mt-4 text-2xl font-mono"></div>
                                @elseif($liveVideo->status === 'live')
                                    <h3 class="text-xl font-semibold mb-2">Live Class in Progress</h3>
                                    <p class="text-gray-300">Click "Join Live Class" to participate</p>
                                @elseif($liveVideo->status === 'ended')
                                    <h3 class="text-xl font-semibold mb-2">Class Ended</h3>
                                    @if($liveVideo->recorded_media_id)
                                        <p class="text-gray-300">Recording is available to watch</p>
                                    @else
                                        <p class="text-gray-300">No recording available</p>
                                    @endif
                                @endif
                            </div>
                        </div>

                        @if($liveVideo->status === 'ended' && $liveVideo->duration)
                            <div class="absolute bottom-4 right-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm">
                                Duration: {{ $liveVideo->duration }} minutes
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Description -->
                @if($liveVideo->description)
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">About This Class</h3>
                        <p class="text-gray-700 leading-relaxed">{{ $liveVideo->description }}</p>
                    </div>
                @endif

                <!-- Related Content -->
                @if($liveVideo->liveable)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Related Content</h3>
                        <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                            <div class="flex-shrink-0">
                                <i class="fas fa-book text-2xl text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">{{ $liveVideo->liveable->title }}</h4>
                                <p class="text-sm text-gray-600">{{ class_basename($liveVideo->liveable_type) }}</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Class Information -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Class Information</h3>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-user w-5 text-gray-400 mr-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">Teacher</p>
                                <p class="font-medium text-gray-900">{{ $liveVideo->teacher->name }}</p>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <i class="fas fa-calendar w-5 text-gray-400 mr-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">Scheduled Time</p>
                                <p class="font-medium text-gray-900">{{ $liveVideo->scheduled_start_time->format('M j, Y') }}</p>
                                <p class="text-sm text-gray-600">{{ $liveVideo->scheduled_start_time->format('g:i A') }} - {{ $liveVideo->scheduled_end_time->format('g:i A') }}</p>
                            </div>
                        </div>

                        @if($liveVideo->actual_start_time)
                            <div class="flex items-center">
                                <i class="fas fa-play w-5 text-gray-400 mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Started At</p>
                                    <p class="font-medium text-gray-900">{{ $liveVideo->actual_start_time->format('g:i A') }}</p>
                                </div>
                            </div>
                        @endif

                        @if($liveVideo->actual_end_time)
                            <div class="flex items-center">
                                <i class="fas fa-stop w-5 text-gray-400 mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Ended At</p>
                                    <p class="font-medium text-gray-900">{{ $liveVideo->actual_end_time->format('g:i A') }}</p>
                                </div>
                            </div>
                        @endif

                        <div class="flex items-center">
                            <i class="fas fa-users w-5 text-gray-400 mr-3"></i>
                            <div>
                                <p class="text-sm text-gray-600">Access</p>
                                <p class="font-medium text-gray-900">
                                    @if($liveVideo->is_public)
                                        Public
                                    @else
                                        Restricted
                                    @endif
                                </p>
                            </div>
                        </div>

                        @if($liveVideo->is_recording_enabled)
                            <div class="flex items-center">
                                <i class="fas fa-record-vinyl w-5 text-gray-400 mr-3"></i>
                                <div>
                                    <p class="text-sm text-gray-600">Recording</p>
                                    <p class="font-medium text-gray-900">Enabled</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        @if($liveVideo->status === 'live')
                            <a href="{{ route('frontend.live-classes.join', $liveVideo) }}" 
                               class="w-full bg-red-600 hover:bg-red-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors block">
                                <i class="fas fa-play mr-2"></i>
                                Join Live Class
                            </a>
                        @elseif($liveVideo->status === 'ended' && $liveVideo->recorded_media_id)
                            <a href="{{ route('live-videos.watch', $liveVideo) }}" 
                               class="w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors block">
                                <i class="fas fa-play mr-2"></i>
                                Watch Recording
                            </a>
                        @endif

                        <a href="{{ route('frontend.live-classes.index') }}" 
                           class="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 text-center py-2 px-4 rounded-lg font-medium transition-colors block">
                            <i class="fas fa-list mr-2"></i>
                            All Live Classes
                        </a>

                        <button onclick="shareClass()" 
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors">
                            <i class="fas fa-share mr-2"></i>
                            Share Class
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Countdown timer for scheduled classes
@if($liveVideo->status === 'scheduled')
function updateCountdown() {
    const targetTime = new Date('{{ $liveVideo->scheduled_start_time->toISOString() }}').getTime();
    const now = new Date().getTime();
    const distance = targetTime - now;

    if (distance < 0) {
        document.getElementById('countdown').innerHTML = "Class should be starting soon...";
        // Refresh page to check if status changed
        setTimeout(() => location.reload(), 5000);
        return;
    }

    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

    let countdownText = '';
    if (days > 0) countdownText += `${days}d `;
    if (hours > 0) countdownText += `${hours}h `;
    countdownText += `${minutes}m ${seconds}s`;

    document.getElementById('countdown').innerHTML = countdownText;
}

updateCountdown();
setInterval(updateCountdown, 1000);
@endif

// Share function
function shareClass() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $liveVideo->title }}',
            text: 'Join this live class: {{ $liveVideo->title }}',
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('Link copied to clipboard!');
        });
    }
}

// Auto-refresh for live classes
@if($liveVideo->status === 'live')
setInterval(() => {
    // Check if status changed
    fetch(`/api/live-videos/{{ $liveVideo->id }}/viewer-count`)
        .then(response => response.json())
        .then(data => {
            if (data.status !== '{{ $liveVideo->status }}') {
                location.reload();
            }
        });
}, 30000);
@endif
</script>
@endpush
