// Collapsible Sidebar Functionality for Filament
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.querySelector('.fi-sidebar');
    const sidebarNav = document.querySelector('.fi-sidebar-nav');
    
    if (!sidebar || !sidebarNav) return;
    
    let isCollapsed = localStorage.getItem('sidebar-collapsed') === 'true';
    
    // Apply initial state
    if (isCollapsed) {
        sidebar.classList.add('fi-sidebar-collapsed');
    }
    
    // Toggle function
    function toggleSidebar() {
        isCollapsed = !isCollapsed;
        sidebar.classList.toggle('fi-sidebar-collapsed', isCollapsed);
        localStorage.setItem('sidebar-collapsed', isCollapsed);
    }
    
    // Add toggle button
    const toggleButton = document.createElement('button');
    toggleButton.innerHTML = `
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
    `;
    toggleButton.className = 'fi-sidebar-toggle-btn p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors';
    toggleButton.onclick = toggleSidebar;
    
    // Insert toggle button at the top of sidebar
    const sidebarHeader = sidebar.querySelector('.fi-sidebar-header') || sidebarNav;
    if (sidebarHeader) {
        sidebarHeader.insertBefore(toggleButton, sidebarHeader.firstChild);
    }
    
    // Auto-expand on hover when collapsed
    sidebar.addEventListener('mouseenter', function() {
        if (isCollapsed) {
            sidebar.classList.add('fi-sidebar-hover-expanded');
        }
    });
    
    sidebar.addEventListener('mouseleave', function() {
        sidebar.classList.remove('fi-sidebar-hover-expanded');
    });
});
