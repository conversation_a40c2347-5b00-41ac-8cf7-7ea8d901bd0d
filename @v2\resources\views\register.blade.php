@extends('layouts.frontend')

@section('title', 'สมัครสมาชิก - EduNest')

@push('styles')
    @vite('resources/css/register.css')
@endpush

@push('scripts')
    @vite('resources/js/register.js')
@endpush

@section('content')

<div class="register-container">
    <div class="register-card">
        <!-- Header -->
        <div class="register-header">
            <h1 class="register-title">สร้างบัญชีใหม่</h1>
            <p class="register-subtitle">เข้าร่วม EduNest และเริ่มต้นการเรียนรู้ที่ไม่มีขีดจำกัด</p>
        </div>

        <!-- Social Login Options -->
        @php
            $enabledProviders = \App\Helpers\SocialAuthHelper::getEnabledProviders();
        @endphp

        @if(count($enabledProviders) > 0)
        <div class="social-login-section">
            @foreach($enabledProviders as $provider => $config)
                <button id="{{ $provider }}Login" class="social-button {{ \App\Helpers\SocialAuthHelper::getProviderCssClass($provider) }}">
                    {!! \App\Helpers\SocialAuthHelper::getProviderIcon($provider) !!}
                    {{ \App\Helpers\SocialAuthHelper::getProviderButtonText($provider) }}
                </button>
            @endforeach
        </div>
        @endif

        <!-- Divider -->
        @if(count($enabledProviders) > 0)
        <div class="divider">
            <span>หรือสมัครด้วยอีเมล</span>
        </div>
        @endif

        <!-- Registration Form -->
        <form id="registerForm" class="register-form" method="POST" action="{{ route('register.store') }}">
            @csrf
            <div class="form-group">
                <label for="role" class="form-label">ประเภทผู้ใช้</label>
                <select id="role" name="role" class="form-input form-select" required>
                    <option value="">เลือกประเภทผู้ใช้</option>
                    <option value="student" {{ old('role') === 'student' ? 'selected' : '' }}>นักเรียน / นักศึกษา</option>
                    <option value="parent" {{ old('role') === 'parent' ? 'selected' : '' }}>ผู้ปกครอง</option>
                    <option value="teacher" {{ old('role') === 'teacher' ? 'selected' : '' }}>ครู / อาจารย์</option>
                    <option value="school" {{ old('role') === 'school' ? 'selected' : '' }}>สถานศึกษา</option>
                </select>
                @error('role')
                    <div class="error-message">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="form-group">
                <label for="name" class="form-label">ชื่อ-นามสกุล</label>
                <input type="text" id="name" name="name" class="form-input"
                       value="{{ old('name') }}" placeholder="กรอกชื่อ-นามสกุลของคุณ" required>
                @error('name')
                    <div class="error-message">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="form-group">
                <label for="email" class="form-label">อีเมล</label>
                <input type="email" id="email" name="email" class="form-input"
                       value="{{ old('email') }}" placeholder="กรอกอีเมลของคุณ" required>
                @error('email')
                    <div class="error-message">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password" class="form-label">รหัสผ่าน</label>
                <input type="password" id="password" name="password" class="form-input"
                       placeholder="สร้างรหัสผ่าน (อย่างน้อย 8 ตัวอักษร)" required>
                @error('password')
                    <div class="error-message">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password_confirmation" class="form-label">ยืนยันรหัสผ่าน</label>
                <input type="password" id="password_confirmation" name="password_confirmation" class="form-input"
                       placeholder="ยืนยันรหัสผ่านอีกครั้ง" required>
                @error('password_confirmation')
                    <div class="error-message">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <button type="submit" id="submitBtn" class="submit-button">
                สร้างบัญชี
            </button>
        </form>

        <!-- Footer -->
        <div class="register-footer">
            <p>มีบัญชีอยู่แล้ว? <a href="/login">เข้าสู่ระบบ</a></p>
            <p class="mt-2">
                การสมัครสมาชิกแสดงว่าคุณยอมรับ
                <a href="/terms">ข้อกำหนดการใช้งาน</a> และ
                <a href="/privacy">นโยบายความเป็นส่วนตัว</a>
            </p>
        </div>
    </div>
</div>

<!-- Phone Authentication Modal -->
<div id="phoneModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-2xl w-full max-w-md mx-4 p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">ยืนยันตัวตนด้วยโทรศัพท์</h3>
            <button id="closePhoneModal" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Phone Number Step -->
        <div id="phoneStep">
            <div class="mb-4">
                <label class="block text-gray-700 font-medium mb-2">หมายเลขโทรศัพท์</label>
                <div class="phone-input-group">
                    <select id="countryCode" class="country-select form-input">
                        <option value="+66">🇹🇭 +66</option>
                        <option value="+1">🇺🇸 +1</option>
                        <option value="+44">🇬🇧 +44</option>
                        <option value="+81">🇯🇵 +81</option>
                        <option value="+82">🇰🇷 +82</option>
                        <option value="+86">🇨🇳 +86</option>
                    </select>
                    <input type="tel" id="phoneNumber" class="phone-number-input form-input" placeholder="8xxxxxxxx" maxlength="10">
                </div>
            </div>
            <button id="sendOtp" class="submit-button">ส่ง OTP</button>
        </div>

        <!-- OTP Verification Step -->
        <div id="otpStep" class="hidden">
            <div class="mb-4">
                <label class="block text-gray-700 font-medium mb-2">กรอกรหัส OTP</label>
                <p class="text-sm text-gray-600 mb-4">เราได้ส่งรหัส 6 หลักไปยังหมายเลขโทรศัพท์ของคุณ</p>
                <div class="flex gap-2 justify-center">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                </div>
                <p class="text-xs text-gray-500 mt-2 text-center">ใช้รหัส 123456 สำหรับทดสอบ</p>
            </div>
            <button id="verifyOtp" class="submit-button">ยืนยัน OTP</button>
            <button id="resendOtp" class="w-full mt-2 text-sm text-gray-600 hover:text-gray-800">ส่งรหัสใหม่</button>
        </div>
    </div>
</div>

@endsection
