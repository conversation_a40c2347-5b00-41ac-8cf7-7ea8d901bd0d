<!DOCTYPE html>
<!-- saved from url=(0140)https://bd81krevfbjr8haq.canva-hosted-embed.com/codelet/AAEAEGJkODFrcmV2ZmJqcjhoYXEAAAAAAZdObeEcuQ3CnGF_kqUE9qf8hPBmvd6OY3UbdX9Wpd9zi2CobeY/ -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduNest - Your Learning Sanctuary</title>
    <script src="./saved_resource"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Prompt:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Prompt', sans-serif;
            background-color: #f8fafc;
            color: #334155;
            overflow-x: hidden;
        }
        
        /* Smooth Scrolling */
        html {
            scroll-behavior: smooth;
        }
        
        /* Modern Gradient Text */
        .gradient-text {
            background: linear-gradient(90deg, #06b6d4, #3b82f6);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }
        
        /* Enhanced Hover Effects */
        .hover-lift {
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), 
                        box-shadow 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        /* Smooth Button Transitions */
        .btn-primary {
            background: linear-gradient(90deg, #06b6d4, #3b82f6);
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }
        
        .btn-primary:active {
            transform: translateY(-1px);
        }
        
        .btn-primary::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }
        
        .btn-primary:hover::after {
            animation: ripple 1s ease-out;
        }
        
        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 0.5;
            }
            100% {
                transform: scale(20, 20);
                opacity: 0;
            }
        }
        
        .btn-secondary {
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
            overflow: hidden;
        }
        
        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        /* Enhanced Navigation Links */
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }
        
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 0;
            background: linear-gradient(90deg, #06b6d4, #3b82f6);
            transition: width 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        .nav-link:hover {
            color: #06b6d4;
        }
        
        .nav-link:hover::after {
            width: 100%;
        }
        
        /* Card Hover Effects */
        .course-card {
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            border: 1px solid rgba(226, 232, 240, 0.8);
        }
        
        .course-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
            border-color: rgba(6, 182, 212, 0.3);
        }
        
        /* Feature Icon Animation */
        .feature-icon {
            transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        /* Testimonial Card Animation */
        .testimonial-card {
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        .testimonial-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        }
        
        /* Floating Animation */
        .float {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
        
        /* Pulse Animation */
        .pulse {
            animation: pulse 3s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        /* Fade In Animation */
        .fade-in {
            animation: fadeIn 1s ease-out forwards;
            opacity: 0;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Staggered Fade In */
        .stagger-1 { animation-delay: 0.1s; }
        .stagger-2 { animation-delay: 0.2s; }
        .stagger-3 { animation-delay: 0.3s; }
        .stagger-4 { animation-delay: 0.4s; }
        .stagger-5 { animation-delay: 0.5s; }
        .stagger-6 { animation-delay: 0.6s; }
        
        /* Category Badge Hover */
        .category-badge {
            transition: all 0.3s ease;
        }
        
        .category-badge:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        /* Stats Counter Animation */
        .counter-value {
            transition: all 0.5s ease-out;
        }
        
        /* Instructor Card Hover */
        .instructor-card {
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        .instructor-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .social-icon {
            transition: all 0.3s ease;
        }
        
        .social-icon:hover {
            transform: translateY(-3px);
            color: #06b6d4;
        }
        
        /* FAQ Accordion Animation */
        .faq-item {
            transition: all 0.3s ease;
        }
        
        .faq-item:hover {
            background-color: rgba(241, 245, 249, 0.8);
        }
        
        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
        }
        
        .faq-answer.active {
            max-height: 1000px;
            transition: max-height 1s ease-in-out;
        }
        
        .faq-icon {
            transition: transform 0.3s ease;
        }
        
        .faq-icon.active {
            transform: rotate(45deg);
        }
        
        /* Newsletter Input Animation */
        .newsletter-input {
            transition: all 0.3s ease;
        }
        
        .newsletter-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        /* Footer Link Hover */
        .footer-link {
            transition: all 0.3s ease;
            position: relative;
        }
        
        .footer-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 1px;
            bottom: -2px;
            left: 0;
            background-color: currentColor;
            transition: width 0.3s ease;
        }
        
        .footer-link:hover::after {
            width: 100%;
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* Background Shapes */
        .shape {
            position: absolute;
            z-index: 0;
            border-radius: 50%;
            filter: blur(70px);
            opacity: 0.1;
        }
        
        /* Course Progress Bar */
        .progress-bar {
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-value {
            height: 100%;
            background: linear-gradient(90deg, #06b6d4, #3b82f6);
            border-radius: 3px;
            transition: width 0.5s ease;
        }
        
        /* Rating Stars */
        .stars {
            display: inline-flex;
            color: #fbbf24;
        }
        
        /* Mobile Menu Animation */
        .mobile-menu {
            transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            transform: translateX(100%);
        }
        
        .mobile-menu.active {
            transform: translateX(0);
        }
        
        /* Course Level Badge */
        .level-badge {
            transition: all 0.3s ease;
        }
        
        .level-badge:hover {
            transform: scale(1.05);
        }
        
        /* Pricing Toggle */
        .pricing-toggle {
            transition: all 0.3s ease;
        }
        
        .toggle-dot {
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        
        /* Blob Animation */
        .blob {
            animation: blob-animation 15s infinite alternate;
            opacity: 0.1;
        }
        
        @keyframes blob-animation {
            0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
            50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
            100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
        }
        
        /* SVG Path Animation */
        .path-animation {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: dash 3s linear forwards;
        }
        
        @keyframes dash {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        /* SVG Fill Animation */
        .fill-animation {
            animation: fillIn 2s ease-in-out forwards;
            opacity: 0;
        }
        
        @keyframes fillIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }
        
        /* SVG Rotate Animation */
        .rotate-animation {
            transform-origin: center;
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* SVG Morph Animation */
        .morph-animation {
            animation: morph 8s ease-in-out infinite alternate;
        }
        
        @keyframes morph {
            0% { d: path("M120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z"); }
            25% { d: path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z"); }
            50% { d: path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,100 160,80 180,100 C200,120 160,140 120,120 Z"); }
            75% { d: path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,100 160,80 180,100 C200,120 160,140 120,120 Z"); }
            100% { d: path("M120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z"); }
        }
        
        /* SVG Wave Animation */
        .wave-animation {
            animation: wave 3s ease-in-out infinite alternate;
        }
        
        @keyframes wave {
            0% { d: path("M0,100 C150,100 150,20 300,20 C450,20 450,100 600,100 L600,200 L0,200 Z"); }
            100% { d: path("M0,100 C150,20 150,100 300,100 C450,100 450,20 600,20 L600,200 L0,200 Z"); }
        }
        
        /* SVG Bounce Animation */
        .bounce-animation {
            animation: bounce 2s ease-in-out infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }
        
        /* SVG Pulse Glow Animation */
        .pulse-glow {
            filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.3));
            animation: pulseGlow 3s ease-in-out infinite;
        }
        
        @keyframes pulseGlow {
            0%, 100% { filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.3)); }
            50% { filter: drop-shadow(0 0 15px rgba(6, 182, 212, 0.7)); }
        }
        
        /* SVG Draw Animation */
        .draw-animation {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: draw 5s ease-in-out forwards;
        }
        
        @keyframes draw {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        /* SVG Gradient Animation */
        .gradient-animation {
            animation: gradientShift 5s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { stop-color: #06b6d4; }
            50% { stop-color: #3b82f6; }
            100% { stop-color: #06b6d4; }
        }
        
        /* SVG Shake Animation */
        .shake-animation {
            animation: shake 0.5s ease-in-out infinite;
            transform-origin: center;
        }
        
        @keyframes shake {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(5deg); }
            75% { transform: rotate(-5deg); }
        }
        
        /* SVG Pop Animation */
        .pop-animation {
            animation: pop 2s ease-in-out infinite;
            transform-origin: center;
        }
        
        @keyframes pop {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }
        
        /* SVG Orbit Animation */
        .orbit-animation {
            animation: orbit 10s linear infinite;
            transform-origin: 50% 50%;
        }
        
        @keyframes orbit {
            0% { transform: rotate(0deg) translateX(50px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(50px) rotate(-360deg); }
        }
        
        /* SVG Dash Animation */
        .dash-animation {
            stroke-dasharray: 10;
            animation: dash-move 20s linear infinite;
        }
        
        @keyframes dash-move {
            to {
                stroke-dashoffset: -1000;
            }
        }
        
        /* SVG Flicker Animation */
        .flicker-animation {
            animation: flicker 3s linear infinite;
        }
        
        @keyframes flicker {
            0%, 19.999%, 22%, 62.999%, 64%, 64.999%, 70%, 100% { opacity: 1; }
            20%, 21.999%, 63%, 63.999%, 65%, 69.999% { opacity: 0.5; }
        }
        
        /* SVG Spin Animation */
        .spin-animation {
            animation: spin 10s linear infinite;
            transform-origin: center;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* SVG Hover Glow Effect */
        .hover-glow:hover {
            filter: drop-shadow(0 0 10px rgba(6, 182, 212, 0.7));
            transition: filter 0.3s ease;
        }
        
        /* SVG Hover Scale Effect */
        .hover-scale {
            transition: transform 0.3s ease;
        }
        
        .hover-scale:hover {
            transform: scale(1.1);
        }
        
        /* SVG Hover Color Shift */
        .hover-color-shift {
            transition: fill 0.3s ease, stroke 0.3s ease;
        }
        
        .hover-color-shift:hover {
            fill: #3b82f6;
            stroke: #06b6d4;
        }
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 18px;
            height: 18px;
            background-color: #ff6b6b;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }
        .mr-2 {
            margin-right: 0.5rem;
        }
    </style>
    
    <?php echo $page_css ?? Null ?>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.pointer-events-none{pointer-events:none}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.sticky{position:sticky}.inset-0{inset:0px}.-bottom-10{bottom:-2.5rem}.-bottom-5{bottom:-1.25rem}.-left-10{left:-2.5rem}.-left-5{left:-1.25rem}.-right-10{right:-2.5rem}.-right-20{right:-5rem}.-top-10{top:-2.5rem}.-top-20{top:-5rem}.bottom-0{bottom:0px}.bottom-1\/4{bottom:25%}.left-0{left:0px}.left-1\/4{left:25%}.right-0{right:0px}.right-4{right:1rem}.top-0{top:0px}.top-1\/2{top:50%}.top-1\/4{top:25%}.top-4{top:1rem}.-z-10{z-index:-10}.z-50{z-index:50}.mx-auto{margin-left:auto;margin-right:auto}.mb-1{margin-bottom:0.25rem}.mb-12{margin-bottom:3rem}.mb-2{margin-bottom:0.5rem}.mb-3{margin-bottom:0.75rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.ml-1{margin-left:0.25rem}.ml-3{margin-left:0.75rem}.ml-4{margin-left:1rem}.ml-auto{margin-left:auto}.mr-4{margin-right:1rem}.mt-1{margin-top:0.25rem}.mt-2{margin-top:0.5rem}.mt-4{margin-top:1rem}.block{display:block}.inline-block{display:inline-block}.flex{display:flex}.grid{display:grid}.hidden{display:none}.h-10{height:2.5rem}.h-12{height:3rem}.h-16{height:4rem}.h-2{height:0.5rem}.h-20{height:5rem}.h-3{height:0.75rem}.h-32{height:8rem}.h-4{height:1rem}.h-40{height:10rem}.h-48{height:12rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-8{height:2rem}.h-96{height:24rem}.w-10{width:2.5rem}.w-12{width:3rem}.w-16{width:4rem}.w-2{width:0.5rem}.w-20{width:5rem}.w-32{width:8rem}.w-4{width:1rem}.w-40{width:10rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-64{width:16rem}.w-8{width:2rem}.w-96{width:24rem}.w-full{width:100%}.max-w-3xl{max-width:48rem}.max-w-7xl{max-width:80rem}.flex-1{flex:1 1 0%}.flex-shrink-0{flex-shrink:0}.rotate-3{--tw-rotate:3deg;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.flex-col{flex-direction:column}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-end{justify-content:flex-end}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.gap-8{gap:2rem}.-space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(-0.5rem * var(--tw-space-x-reverse));margin-left:calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-4 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(1rem * var(--tw-space-x-reverse));margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))}.space-x-8 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(2rem * var(--tw-space-x-reverse));margin-left:calc(2rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-4 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1rem * var(--tw-space-y-reverse))}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.whitespace-nowrap{white-space:nowrap}.rounded-3xl{border-radius:1.5rem}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.rounded-xl{border-radius:0.75rem}.border{border-width:1px}.border-2{border-width:2px}.border-t{border-top-width:1px}.border-cyan-500{--tw-border-opacity:1;border-color:rgb(6 182 212 / var(--tw-border-opacity, 1))}.border-gray-100{--tw-border-opacity:1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1))}.border-gray-200{--tw-border-opacity:1;border-color:rgb(229 231 235 / var(--tw-border-opacity, 1))}.border-white{--tw-border-opacity:1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}.bg-amber-100{--tw-bg-opacity:1;background-color:rgb(254 243 199 / var(--tw-bg-opacity, 1))}.bg-blue-100{--tw-bg-opacity:1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.bg-blue-500{--tw-bg-opacity:1;background-color:rgb(59 130 246 / var(--tw-bg-opacity, 1))}.bg-cyan-100{--tw-bg-opacity:1;background-color:rgb(207 250 254 / var(--tw-bg-opacity, 1))}.bg-cyan-500{--tw-bg-opacity:1;background-color:rgb(6 182 212 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-green-100{--tw-bg-opacity:1;background-color:rgb(220 252 231 / var(--tw-bg-opacity, 1))}.bg-purple-100{--tw-bg-opacity:1;background-color:rgb(243 232 255 / var(--tw-bg-opacity, 1))}.bg-purple-500{--tw-bg-opacity:1;background-color:rgb(168 85 247 / var(--tw-bg-opacity, 1))}.bg-red-100{--tw-bg-opacity:1;background-color:rgb(254 226 226 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-white\/90{background-color:rgb(255 255 255 / 0.9)}.bg-yellow-400{--tw-bg-opacity:1;background-color:rgb(250 204 21 / var(--tw-bg-opacity, 1))}.bg-gradient-to-r{background-image:linear-gradient(to right, var(--tw-gradient-stops))}.from-blue-400{--tw-gradient-from:#60a5fa var(--tw-gradient-from-position);--tw-gradient-to:rgb(96 165 250 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-cyan-50{--tw-gradient-from:#ecfeff var(--tw-gradient-from-position);--tw-gradient-to:rgb(236 254 255 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-cyan-500{--tw-gradient-from:#06b6d4 var(--tw-gradient-from-position);--tw-gradient-to:rgb(6 182 212 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-cyan-500\/20{--tw-gradient-from:rgb(6 182 212 / 0.2) var(--tw-gradient-from-position);--tw-gradient-to:rgb(6 182 212 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-purple-400{--tw-gradient-from:#c084fc var(--tw-gradient-from-position);--tw-gradient-to:rgb(192 132 252 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-amber-400{--tw-gradient-from:#e4d184 var(--tw-gradient-from-position);--tw-gradient-to:rgb(228 209 132 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-blue-50{--tw-gradient-to:#eff6ff var(--tw-gradient-to-position)}.to-blue-500{--tw-gradient-to:#3b82f6 var(--tw-gradient-to-position)}.to-blue-500\/20{--tw-gradient-to:rgb(59 130 246 / 0.2) var(--tw-gradient-to-position)}.to-blue-600{--tw-gradient-to:#2563eb var(--tw-gradient-to-position)}.to-purple-600{--tw-gradient-to:#9333ea var(--tw-gradient-to-position)}.text-amber-600{color:#f59e0b}.to-amber-600{--tw-gradient-to:#f59e0b var(--tw-gradient-to-position)}.p-4{padding:1rem}.p-6{padding:1.5rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.px-5{padding-left:1.25rem;padding-right:1.25rem}.px-6{padding-left:1.5rem;padding-right:1.5rem}.px-8{padding-left:2rem;padding-right:2rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-12{padding-top:3rem;padding-bottom:3rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-20{padding-top:5rem;padding-bottom:5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.pb-32{padding-bottom:8rem}.pb-4{padding-bottom:1rem}.pt-20{padding-top:5rem}.pt-4{padding-top:1rem}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-4xl{font-size:2.25rem;line-height:2.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.text-xs{font-size:0.75rem;line-height:1rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.font-semibold{font-weight:600}.text-amber-500{--tw-text-opacity:1;color:rgb(245 158 11 / var(--tw-text-opacity, 1))}.text-blue-500{--tw-text-opacity:1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235 / var(--tw-text-opacity, 1))}.text-cyan-500{--tw-text-opacity:1;color:rgb(6 182 212 / var(--tw-text-opacity, 1))}.text-cyan-600{--tw-text-opacity:1;color:rgb(8 145 178 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-green-500{--tw-text-opacity:1;color:rgb(34 197 94 / var(--tw-text-opacity, 1))}.text-purple-500{--tw-text-opacity:1;color:rgb(168 85 247 / var(--tw-text-opacity, 1))}.text-purple-600{--tw-text-opacity:1;color:rgb(147 51 234 / var(--tw-text-opacity, 1))}.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-white\/80{color:rgb(255 255 255 / 0.8)}.opacity-10{opacity:0.1}.opacity-20{opacity:0.2}.opacity-5{opacity:0.05}.shadow-lg{--tw-shadow:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.backdrop-blur-sm{--tw-backdrop-blur:blur(4px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.hover\:bg-gray-100:hover{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.hover\:text-cyan-500:hover{--tw-text-opacity:1;color:rgb(6 182 212 / var(--tw-text-opacity, 1))}@media (min-width: 640px){.sm\:flex-row{flex-direction:row}.sm\:px-6{padding-left:1.5rem;padding-right:1.5rem}}@media (min-width: 768px){.md\:flex{display:flex}.md\:hidden{display:none}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4, minmax(0, 1fr))}.md\:text-4xl{font-size:2.25rem;line-height:2.5rem}.md\:text-5xl{font-size:3rem;line-height:1}}@media (min-width: 1024px){.lg\:mb-0{margin-bottom:0px}.lg\:w-1\/2{width:50%}.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}.lg\:grid-cols-6{grid-template-columns:repeat(6, minmax(0, 1fr))}.lg\:flex-row{flex-direction:row}.lg\:px-8{padding-left:2rem;padding-right:2rem}.lg\:pr-12{padding-right:3rem}.lg\:text-6xl{font-size:3.75rem;line-height:1}}.py-4{padding-top:1rem;padding-bottom:1rem}</style>

</head>
<body>


<!-- Background Shapes -->
<div  id="home" class="shape w-96 h-96 bg-cyan-500 top-0 left-0 blob"></div>
<div class="shape w-96 h-96 bg-blue-500 bottom-0 right-0 blob" style="animation-delay: -5s;"></div>

<!-- Animated Background SVG -->
<div class="fixed inset-0 -z-10 overflow-hidden opacity-10 pointer-events-none">
    <svg width="100%" height="100%" viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
        <!-- Animated Circles -->
        <circle cx="200" cy="200" r="50" fill="#06b6d4" class="orbit-animation" style="animation-duration: 15s;"></circle>
        <circle cx="600" cy="400" r="30" fill="#3b82f6" class="orbit-animation" style="animation-duration: 20s;"></circle>
        <circle cx="900" cy="200" r="40" fill="#8b5cf6" class="orbit-animation" style="animation-duration: 25s;"></circle>

        <!-- Animated Lines -->
        <path d="M0,400 C300,300 600,500 1200,400" stroke="#06b6d4" stroke-width="2" fill="none" class="dash-animation"></path>
        <path d="M0,500 C300,600 600,400 1200,500" stroke="#3b82f6" stroke-width="2" fill="none" class="dash-animation" style="animation-delay: -5s;"></path>

        <!-- Animated Grid -->
        <g class="spin-animation" style="animation-duration: 120s; transform-origin: 600px 400px;">
            <line x1="0" y1="400" x2="1200" y2="400" stroke="#06b6d4" stroke-width="1" stroke-opacity="0.2"></line>
            <line x1="600" y1="0" x2="600" y2="800" stroke="#06b6d4" stroke-width="1" stroke-opacity="0.2"></line>
            <circle cx="600" cy="400" r="200" stroke="#3b82f6" stroke-width="1" fill="none" stroke-opacity="0.2"></circle>
            <circle cx="600" cy="400" r="300" stroke="#3b82f6" stroke-width="1" fill="none" stroke-opacity="0.2"></circle>
        </g>

        <!-- Animated Wave -->
        <path d="M0,700 C200,650 400,750 600,700 C800,650 1000,750 1200,700 L1200,800 L0,800 Z" fill="#06b6d4" fill-opacity="0.1" class="wave-animation"></path>
    </svg>
</div>

<!-- Header -->
<header class="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="/#" class="flex items-center">
                    <svg class="w-10 h-10 text-cyan-500 pulse-glow" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="draw-animation" d="M20 5L5 12.5L20 20L35 12.5L20 5Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="2"></path>
                        <path class="draw-animation" style="animation-delay: 0.5s;" d="M5 27.5L20 35L35 27.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                        <path class="draw-animation" style="animation-delay: 1s;" d="M5 20L20 27.5L35 20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <span class="ml-3 text-2xl font-bold">Edu<span class="gradient-text">Nest</span></span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex space-x-8">
                <a href="/#home" class="nav-link text-gray-700 hover:text-cyan-500 font-medium">หน้าแรก</a>
                <a href="/#features" class="nav-link text-gray-700 hover:text-cyan-500 font-medium">คุณสมบัติ</a>
                <a href="/downloads.php" class="nav-link text-gray-700 hover:text-cyan-500 font-medium">ดาวน์โหลด</a>
                <a href="/#pricing" class="nav-link text-gray-700 hover:text-cyan-500 font-medium">แพ็กเกจ</a>
                <a href="/#about" class="nav-link text-gray-700 hover:text-cyan-500 font-medium">เกี่ยวกับเรา</a>
                <a href="/#contact" class="nav-link text-gray-700 hover:text-cyan-500 font-medium">ติดต่อ</a>
            </nav>

            <!-- CTA Buttons -->
            <div class="hidden md:flex items-center space-x-4">
                <a href="/login.php" class="text-gray-700 hover:text-cyan-500 font-medium nav-link">เข้าสู่ระบบ</a>
                <a href="/#" class="btn-primary px-5 py-2 rounded-full text-white font-medium">สมัครสมาชิก</a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button id="menu-toggle" class="text-gray-700 hover:text-cyan-500 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mobile-menu fixed top-0 right-0 bottom-0 w-64 bg-white shadow-lg z-50 p-6">
        <div class="flex justify-end mb-8">
            <button id="close-menu" class="text-gray-700 hover:text-cyan-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <nav class="flex flex-col space-y-4">
            <a href="/#home" class="text-gray-700 hover:text-cyan-500 font-medium">หน้าแรก</a>
            <a href="/#features" class="text-gray-700 hover:text-cyan-500 font-medium">คุณสมบัติ</a>
            <a href="/downloads.php" class="text-gray-700 hover:text-cyan-500 font-medium">ดาวน์โหลด</a>
            <a href="/#pricing" class="text-gray-700 hover:text-cyan-500 font-medium">แพ็กเกจ</a>
            <a href="/#about" class="text-gray-700 hover:text-cyan-500 font-medium">เกี่ยวกับเรา</a>
            <a href="/#contact" class="text-gray-700 hover:text-cyan-500 font-medium">ติดต่อ</a>
            <div class="pt-4 mt-4 border-t border-gray-200">
                <a href="/login.php" class="block w-full py-2 text-center text-gray-700 hover:text-cyan-500 font-medium mb-3">เข้าสู่ระบบ</a>
                <a href="/#" class="block w-full py-2 text-center btn-primary rounded-full text-white font-medium">สมัครสมาชิก</a>
            </div>
        </nav>
    </div>
</header>

<!-- Hero Section -->
<section class="relative pt-20 pb-32 overflow-hidden">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row items-center">
            <!-- Hero Content -->
            <div class="lg:w-1/2 lg:pr-12 mb-12 lg:mb-0">
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 fade-in stagger-1">
                    รังแห่งการเรียนรู้ <span class="gradient-text">ครบวงจร</span>
                </h1>

                <p class="text-lg text-gray-600 mb-8 fade-in stagger-2">
                    <!-- เสริมสร้างทักษะและพัฒนาศักยภาพอย่างรอบด้าน<br> -->
                    ด้วยระบบสนับสนุนการเรียนรู้ครบวงจร <br>
                    ที่เชื่อมโยง ผู้ปกครอง โรงเรียน และครู เข้าด้วยกัน<br>
                    ร่วมกันสร้างประสบการณ์เรียนรู้ที่มีความหมาย และเติบโตไปพร้อมกันทุกก้าว
                </p>
                <div class="flex flex-col sm:flex-row gap-4 mb-8 fade-in stagger-3">
                    <a href="/#courses" class="btn-primary px-8 py-4 rounded-full text-white font-medium text-center">
                        เข้าใช้งานฟรี
                    </a>
                    <a href="/#" class="btn-secondary px-8 py-4 rounded-full border border-cyan-500 text-cyan-600 font-medium text-center">
                        สมัครสมาชิก
                    </a>
                </div>

            </div>

            <!-- Hero Image -->
            <div class="lg:w-1/2 relative fade-in stagger-3">
                <div class="relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-3xl transform rotate-3"></div>
                    <div class="relative bg-white rounded-3xl shadow-xl overflow-hidden border border-gray-100 float">
                        <div class="bg-gradient-to-r from-cyan-500 to-blue-500 h-3"></div>
                        <div class="p-6">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center text-white font-bold text-xl">
                                    E
                                </div>
                                <div class="ml-4">
                                    <h3 class="font-semibold text-lg">EduNest Dashboard</h3>
                                    <p class="text-gray-500 text-sm">สวัสดี, น้องมายด์!</p>
                                </div>
                            </div>

                            <div class="mb-6">
                                <h4 class="font-medium mb-3">แบบฝึกประจำวัน</h4>
                                <div class="bg-gray-50 rounded-xl p-4 mb-4">
                                    <div class="flex items-start">
                                        <div class="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center text-blue-500 mr-4 flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 pulse-glow" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <h5 class="font-medium mb-1">วิชา: คณิตศาสตร์</h5>
                                            <div class="progress-bar mb-2">
                                                <div class="progress-value" style="width: 64%"></div>
                                            </div>
                                            <p class="text-gray-500 text-sm">ความก้าวหน้า 32/50 ข้อ (64%)</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 rounded-xl p-4">
                                    <div class="flex items-start">
                                        <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center text-purple-500 mr-4 flex-shrink-0">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 pulse-glow" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <h5 class="font-medium mb-1">วิชา: วิทยาศาสตร์</h5>
                                            <div class="progress-bar mb-2">
                                                <div class="progress-value" style="width: 42%"></div>
                                            </div>
                                            <p class="text-gray-500 text-sm">ความก้าวหน้า 21/50 ข้อ (42%)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h4 class="font-medium mb-3">กิจกรรมวันนี้</h4>
                                <div class="bg-gray-50 rounded-xl p-4 flex items-center">
                                    <div class="w-10 h-10 rounded-lg bg-cyan-100 flex items-center justify-center text-cyan-500 mr-4 flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 pulse-glow" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h5 class="font-medium">Live Class: คณิตศาสตร์ - เรื่องเศษส่วน</h5>
                                        <p class="text-gray-500 text-sm">17:00 - 18:00 น.</p>
                                    </div>
                                    <button class="ml-auto bg-cyan-500 text-white px-3 py-1 rounded-full text-sm hover-scale">เข้าร่วมคลาส</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Floating Elements -->
                <div class="absolute -top-10 -right-10 w-20 h-20 bg-yellow-400 rounded-full opacity-20 pulse"></div>
                <div class="absolute -bottom-5 -left-5 w-16 h-16 bg-cyan-500 rounded-full opacity-20 pulse" style="animation-delay: 1s;"></div>

                <!-- Animated SVG Elements -->
                <svg class="absolute -top-20 -right-20 w-40 h-40 text-blue-500 opacity-10 spin-animation" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <path fill="currentColor" d="M47.7,-57.2C59.5,-45.8,65.9,-28.6,68.8,-10.8C71.6,7.1,71,25.5,62.2,39.1C53.5,52.7,36.7,61.4,19.5,65.2C2.3,69,-15.4,67.8,-30.3,60.5C-45.2,53.2,-57.3,39.7,-65.3,23.3C-73.3,6.9,-77.3,-12.5,-71.1,-28.2C-64.9,-43.9,-48.5,-55.9,-32.2,-65.2C-15.9,-74.5,0.3,-81,15.8,-77.5C31.3,-74,47.1,-60.5,47.7,-57.2Z" transform="translate(100 100)"></path>
                </svg>

                <svg class="absolute -bottom-10 -left-10 w-32 h-32 text-cyan-500 opacity-10 spin-animation" style="animation-direction: reverse;" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <path fill="currentColor" d="M47.3,-51.5C59.9,-41.8,68.3,-24.1,70.4,-5.8C72.5,12.5,68.3,31.4,57.2,44.3C46.1,57.2,28.1,64.1,8.9,68.5C-10.3,72.8,-30.7,74.6,-45.5,65.5C-60.3,56.4,-69.5,36.4,-73.3,15.3C-77,-5.8,-75.3,-28,-64.3,-41.8C-53.4,-55.7,-33.2,-61.3,-14.8,-60.2C3.6,-59.1,20.1,-51.3,47.3,-51.5Z" transform="translate(100 100)"></path>
                </svg>

                <!-- Animated Dots -->
                <div class="absolute top-1/4 right-0 w-2 h-2 bg-cyan-500 rounded-full orbit-animation"></div>
                <div class="absolute bottom-1/4 left-0 w-2 h-2 bg-blue-500 rounded-full orbit-animation" style="animation-delay: -3s;"></div>
                <div class="absolute top-1/2 left-1/4 w-2 h-2 bg-purple-500 rounded-full orbit-animation" style="animation-delay: -6s;"></div>
            </div>
        </div>
    </div>
</section>

<!-- Stats Section -->
<section id="courses" class="py-12 bg-gradient-to-r from-cyan-50 to-blue-50 relative overflow-hidden">
    <!-- Animated Background SVG -->
    <div class="absolute inset-0 pointer-events-none">
        <svg width="100%" height="100%" viewBox="0 0 1200 200" xmlns="http://www.w3.org/2000/svg">
            <path class="wave-animation" fill="#06b6d4" fill-opacity="0.05" d="M0,100 C150,150 350,50 500,100 C650,150 850,50 1200,100 L1200,200 L0,200 Z"></path>
            <path class="wave-animation" style="animation-delay: -2s;" fill="#3b82f6" fill-opacity="0.05" d="M0,100 C350,50 450,150 650,100 C850,50 950,150 1200,100 L1200,200 L0,200 Z"></path>
        </svg>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div class="fade-in stagger-1">
                <div class="relative inline-block">
                    <svg class="w-16 h-16 mx-auto mb-2 text-cyan-500 spin-animation" style="animation-duration: 30s;" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" stroke-opacity="0.2" fill="none"></circle>
                        <path class="draw-animation" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"></path>
                    </svg>
                    <h3 class="text-3xl md:text-4xl font-bold text-gray-800 counter-value">200+</h3>
                </div>
                <p class="text-gray-600 mt-2">หลักสูตร</p>
            </div>
            <div class="fade-in stagger-2">
                <div class="relative inline-block">
                    <svg class="w-16 h-16 mx-auto mb-2 text-blue-500 spin-animation" style="animation-duration: 30s; animation-direction: reverse;" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" stroke-opacity="0.2" fill="none"></circle>
                        <path class="draw-animation" d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" fill="currentColor"></path>
                    </svg>
                    <h3 class="text-3xl md:text-4xl font-bold text-gray-800 counter-value">50+</h3>
                </div>
                <p class="text-gray-600 mt-2">ผู้สอนผู้เชี่ยวชาญ</p>
            </div>
            <div class="fade-in stagger-3">
                <div class="relative inline-block">
                    <svg class="w-16 h-16 mx-auto mb-2 text-purple-500 spin-animation" style="animation-duration: 30s;" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" stroke-opacity="0.2" fill="none"></circle>
                        <path class="draw-animation" d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="currentColor"></path>
                    </svg>
                    <h3 class="text-3xl md:text-4xl font-bold text-gray-800 counter-value">10K+</h3>
                </div>
                <p class="text-gray-600 mt-2">นักเรียน</p>
            </div>
            <div class="fade-in stagger-4">
                <div class="relative inline-block">
                    <svg class="w-16 h-16 mx-auto mb-2 text-green-500 spin-animation" style="animation-duration: 30s; animation-direction: reverse;" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" stroke-opacity="0.2" fill="none"></circle>
                        <path class="draw-animation" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"></path>
                    </svg>
                    <h3 class="text-3xl md:text-4xl font-bold text-gray-800 counter-value">95%</h3>
                </div>
                <p class="text-gray-600 mt-2">อัตราความพึงพอใจ</p>
            </div>
        </div>
    </div>
</section>

<!-- Categories Section -->
<section id="categories" class="py-20 relative overflow-hidden">
    <!-- Animated Background Pattern -->
    <div class="absolute inset-0 pointer-events-none opacity-5">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#3b82f6" stroke-width="0.5"></path>
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)"></rect>

            <circle cx="10%" cy="20%" r="50" fill="#06b6d4" fill-opacity="0.2" class="pulse"></circle>
            <circle cx="80%" cy="60%" r="70" fill="#3b82f6" fill-opacity="0.2" class="pulse" style="animation-delay: -1s;"></circle>
            <circle cx="50%" cy="80%" r="40" fill="#8b5cf6" fill-opacity="0.2" class="pulse" style="animation-delay: -2s;"></circle>
        </svg>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <!-- <div class="text-center mb-12 fade-in">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">สำรวจหมวดหมู่ <span class="gradient-text">การเรียนรู้</span></h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">ค้นพบคอร์สเรียนหลากหลาย ครอบคลุมทุกความสนใจ เพื่อพัฒนาทักษะและต่อยอดศักยภาพของคุณ</p>
        </div> -->

        <div class="text-center mb-12 fade-in">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">ครบเครื่องเรื่องการเรียนรู้ <span class="gradient-text">สำหรับเด็กยุคใหม่</span></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">เนื้อหาครอบคลุม สื่อหลากหลาย พัฒนาทักษะอย่างเป็นระบบ</p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            <a href="/#" class="category-badge bg-white rounded-xl shadow-sm p-6 text-center hover-lift fade-in stagger-1">
                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-500 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path class="draw-animation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-800">เทคโนโลยี</h3>
                <p class="text-gray-500 text-sm mt-1">48 หลักสูตร</p>
            </a>

            <a href="/#" class="category-badge bg-white rounded-xl shadow-sm p-6 text-center hover-lift fade-in stagger-2">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path class="draw-animation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-800">ธุรกิจ</h3>
                <p class="text-gray-500 text-sm mt-1">36 หลักสูตร</p>
            </a>

            <a href="/#" class="category-badge bg-white rounded-xl shadow-sm p-6 text-center hover-lift fade-in stagger-3">
                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple-500 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path class="draw-animation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-800">วิทยาศาสตร์ข้อมูล</h3>
                <p class="text-gray-500 text-sm mt-1">24 หลักสูตร</p>
            </a>

            <a href="/#" class="category-badge bg-white rounded-xl shadow-sm p-6 text-center hover-lift fade-in stagger-4">
                <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-red-500 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path class="draw-animation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-800">การออกแบบ</h3>
                <p class="text-gray-500 text-sm mt-1">32 หลักสูตร</p>
            </a>

            <a href="/#" class="category-badge bg-white rounded-xl shadow-sm p-6 text-center hover-lift fade-in stagger-5">
                <div class="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-amber-500 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path class="draw-animation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-800">ภาษา</h3>
                <p class="text-gray-500 text-sm mt-1">28 หลักสูตร</p>
            </a>

            <a href="/#" class="category-badge bg-white rounded-xl shadow-sm p-6 text-center hover-lift fade-in stagger-6">
                <div class="w-16 h-16 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-cyan-500 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path class="draw-animation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-800">การพัฒนาระบบ</h3>
                <p class="text-gray-500 text-sm mt-1">42 หลักสูตร</p>
            </a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="py-20 bg-gray-50 relative overflow-hidden">
    <!-- Animated Background Pattern -->
    <div class="absolute inset-0 pointer-events-none">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <lineargradient id="courseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stop-color="#06b6d4" stop-opacity="0.05" class="gradient-animation"></stop>
                    <stop offset="100%" stop-color="#3b82f6" stop-opacity="0.05" class="gradient-animation" style="animation-delay: -2.5s;"></stop>
                </lineargradient>
            </defs>

            <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#courseGradient)"></path>

            <!-- Animated Circles -->
            <circle cx="5%" cy="20%" r="50" fill="#06b6d4" fill-opacity="0.03" class="pulse"></circle>
            <circle cx="95%" cy="70%" r="70" fill="#3b82f6" fill-opacity="0.03" class="pulse" style="animation-delay: -1.5s;"></circle>
            <circle cx="80%" cy="10%" r="40" fill="#8b5cf6" fill-opacity="0.03" class="pulse" style="animation-delay: -3s;"></circle>
            <circle cx="20%" cy="90%" r="60" fill="#06b6d4" fill-opacity="0.03" class="pulse" style="animation-delay: -4.5s;"></circle>

            <!-- Animated Lines -->
            <line x1="0" y1="15%" x2="100%" y2="15%" stroke="#06b6d4" stroke-width="1" stroke-opacity="0.05" class="dash-animation"></line>
            <line x1="0" y1="85%" x2="100%" y2="85%" stroke="#3b82f6" stroke-width="1" stroke-opacity="0.05" class="dash-animation" style="animation-delay: -10s;"></line>
        </svg>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="text-center mb-12 fade-in">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">เสริมสร้างการเรียนรู้ สนุกได้ทุกที่ <span class="gradient-text">เติบโตได้ทุกวัน</span></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">ทุกเครื่องมือที่คุณต้องการ เพื่อสนับสนุนการเรียนรู้ของเด็กๆ อย่างมีคุณภาพและยั่งยืน</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-sm hover:shadow-md">
                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">สื่อครบวงจร</h3>
                <p class="text-gray-600">
                    ใบงาน เกม วิดีโอ และกล่องกิจกรรมที่ออกแบบโดยผู้เชี่ยวชาญด้านการศึกษา เพื่อการเรียนรู้ที่สนุกและมีประสิทธิภาพ
                </p>
            </div>

            <!-- Feature 2 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-sm hover:shadow-md">
                <div class="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">แบบทดสอบระดับความสามารถ</h3>
                <p class="text-gray-600">
                    ปรับหลักสูตรการเรียนให้เหมาะสมกับความสามารถด้วยระบบออนไลน์ มีหลักสูตรตามมาตรฐานแกนกลางการศึกษาขั้นพื้นฐาน
                </p>
            </div>

            <!-- Feature 3 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-sm hover:shadow-md">
                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">คลาสเรียนสดออนไลน์</h3>
                <p class="text-gray-600">
                    คลาสเรียนสดออนไลน์โดยครูผู้เชี่ยวชาญ ที่เด็กๆ สามารถมีส่วนร่วมและโต้ตอบได้แบบเรียลไทม์
                </p>
            </div>

            <!-- Feature 4 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-sm hover:shadow-md">
                <div class="bg-red-100 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">กล่องกิจกรรมส่งถึงบ้าน</h3>
                <p class="text-gray-600">
                    ชุดกิจกรรมการเรียนรู้แบบ hands-on ที่จัดส่งถึงบ้าน เพื่อให้เด็กๆ ได้เรียนรู้ผ่านการลงมือทำจริง
                </p>
            </div>

            <!-- Feature 5 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-sm hover:shadow-md">
                <div class="bg-amber-100 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">ระบบติดตามผลการเรียน</h3>
                <p class="text-gray-600">
                    ติดตามพัฒนาการและความก้าวหน้าของเด็กได้อย่างละเอียด พร้อมรายงานที่เข้าใจง่ายสำหรับผู้ปกครองและครู
                </p>
            </div>

            <!-- Feature 6 -->
            <div class="feature-card bg-white rounded-xl p-6 shadow-sm hover:shadow-md">
                <div class="bg-cyan-100 w-16 h-16 rounded-full flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-cyan-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path class="draw-animation" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">ชุมชนการเรียนรู้</h3>
                <p class="text-gray-600">
                    สร้างเครือข่ายการเรียนรู้ร่วมกันผ่านฟอรั่ม เวิร์กช็อป และกิจกรรมออนไลน์ เพื่อแลกเปลี่ยนประสบการณ์และร่วมกันสนับสนุนการเรียนรู้ของเด็กๆ
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Featured Courses Section -->
<section id="forwhom" class="py-20 bg-gray-50 relative overflow-hidden">
    <!-- Animated Background Pattern -->
    <div class="absolute inset-0 pointer-events-none">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <lineargradient id="courseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stop-color="#06b6d4" stop-opacity="0.05" class="gradient-animation"></stop>
                    <stop offset="100%" stop-color="#3b82f6" stop-opacity="0.05" class="gradient-animation" style="animation-delay: -2.5s;"></stop>
                </lineargradient>
            </defs>

            <path d="M0,0 L100,0 L100,100 L0,100 Z" fill="url(#courseGradient)"></path>

            <!-- Animated Circles -->
            <circle cx="5%" cy="20%" r="50" fill="#06b6d4" fill-opacity="0.03" class="pulse"></circle>
            <circle cx="95%" cy="70%" r="70" fill="#3b82f6" fill-opacity="0.03" class="pulse" style="animation-delay: -1.5s;"></circle>
            <circle cx="80%" cy="10%" r="40" fill="#8b5cf6" fill-opacity="0.03" class="pulse" style="animation-delay: -3s;"></circle>
            <circle cx="20%" cy="90%" r="60" fill="#06b6d4" fill-opacity="0.03" class="pulse" style="animation-delay: -4.5s;"></circle>

            <!-- Animated Lines -->
            <line x1="0" y1="15%" x2="100%" y2="15%" stroke="#06b6d4" stroke-width="1" stroke-opacity="0.05" class="dash-animation"></line>
            <line x1="0" y1="85%" x2="100%" y2="85%" stroke="#3b82f6" stroke-width="1" stroke-opacity="0.05" class="dash-animation" style="animation-delay: -10s;"></line>
        </svg>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div class="text-center mb-12 fade-in">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">สร้างเครือข่ายการเรียนรู้ <span class="gradient-text">ร่วมกัน</span></h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">ร่วมกันสร้างประสบการณ์เรียนรู้ที่มีความหมาย และเติบโตไปพร้อมกันทุกก้าว</p>
        </div>

        <!-- <div class="flex overflow-x-auto pb-4 mb-8 space-x-4 scrollbar-hide fade-in">
            <button class="px-6 py-3 rounded-lg whitespace-nowrap bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-medium">All Courses</button>
            <button class="px-6 py-3 rounded-lg whitespace-nowrap bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors">Web Development</button>
            <button class="px-6 py-3 rounded-lg whitespace-nowrap bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors">Data Science</button>
            <button class="px-6 py-3 rounded-lg whitespace-nowrap bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors">Business</button>
            <button class="px-6 py-3 rounded-lg whitespace-nowrap bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors">Design</button>
            <button class="px-6 py-3 rounded-lg whitespace-nowrap bg-white text-gray-700 font-medium hover:bg-gray-100 transition-colors">Marketing</button>
        </div> -->

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Course Card 1 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm course-card fade-in stagger-1">
                <div class="relative">
                    <div class="h-48 bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-white/80 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <span class="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-medium text-blue-600 shadow-sm pulse">
                        For parents and students
                    </span>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2">สำหรับผู้ปกครองและนักเรียน</h3>
                    <p class="text-gray-600 mb-4">ทดสอบระดับความสามารถ เริ่มการเรียนพิเศษ พร้อมรับกิจกรรมและสื่อการเรียนรู้คุณภาพสูงที่เหมาะสมกับพัฒนาการด้วยระบบออนไลน์ พร้อมติดตามความก้าวหน้าได้อย่างใกล้ชิด
                    </p>
                    <div class="flex justify-between items-center">
                        <button class="btn-primary px-4 py-2 rounded-lg text-white">อ่านเพิ่มเติม</button>
                    </div>
                </div>
            </div>

            <!-- Course Card 2 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm course-card fade-in stagger-1">
                <div class="relative">
                    <div class="h-48 bg-gradient-to-r from-purple-400 to-purple-600 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-white/80 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"></path>
                        </svg>
                    </div>
                    <span class="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-medium text-purple-600 shadow-sm pulse">
                        For teachers
                    </span>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2">สำหรับครู</h3>
                    <p class="text-gray-600 mb-4"> แบ่งปันความรู้และสร้างรายได้จากสื่อการสอนคุณภาพ พร้อมเครื่องมือที่ช่วยให้การสร้างเนื้อหาเป็นเรื่องง่าย
                    </p>
                    <div class="flex justify-between items-center">
                        <button class="btn-primary px-4 py-2 rounded-lg text-white bg-gradient-to-r from-purple-400 to-purple-600">อ่านเพิ่มเติม</button>
                    </div>
                </div>
            </div>

            <!-- Course Card 3 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm course-card fade-in stagger-1">
                <div class="relative">
                    <div class="h-48 bg-gradient-to-r from-amber-400 to-amber-600 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-white/80 hover-scale" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <span class="absolute top-4 right-4 bg-white px-3 py-1 rounded-full text-sm font-medium text-amber-600 shadow-sm pulse">
                        For schools
                    </span>
                </div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2">สำหรับโรงเรียน</h3>
                    <p class="text-gray-600 mb-4">ยกระดับการเรียนการสอนด้วยเครื่องมือและสื่อการเรียนรู้ที่ทันสมัย พร้อมระบบบริหารจัดการที่มีประสิทธิภาพ
                    </p>
                    <div class="flex justify-between items-center">
                        <button class="btn-primary px-4 py-2 rounded-lg text-white bg-gradient-to-r from-amber-400 to-amber-600">อ่านเพิ่มเติม</button>
                    </div>
                </div>
            </div>

        </div>
    </div>
</section>

    <!-- Footer -->
    <footer class="bg-white py-16 border-t border-gray-100">
        <div class="max-w-7xl mx-auto px-6 md:px-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <div>
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="https://bd81krevfbjr8haq.canva-hosted-embed.com/codelet/AAEAEGJkODFrcmV2ZmJqcjhoYXEAAAAAAZdObeEcuQ3CnGF_kqUE9qf8hPBmvd6OY3UbdX9Wpd9zi2CobeY/#" class="flex items-center">
                            <svg class="w-10 h-10 text-cyan-500 pulse-glow" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path class="draw-animation" d="M20 5L5 12.5L20 20L35 12.5L20 5Z" fill="currentColor" fill-opacity="0.2" stroke="currentColor" stroke-width="2"></path>
                                <path class="draw-animation" style="animation-delay: 0.5s;" d="M5 27.5L20 35L35 27.5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path class="draw-animation" style="animation-delay: 1s;" d="M5 20L20 27.5L35 20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                            <span class="ml-3 text-2xl font-bold">Edu<span class="gradient-text">Nest</span></span>
                        </a>
                    </div>
                    <p class="text-gray-500 mb-6">
                        รังแห่งการเรียนรู้สำหรับเด็ก ที่เชื่อมโยงผู้ปกครอง โรงเรียน และครูเข้าด้วยกัน
                    </p>
                    <div class="flex space-x-4">
                        <a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-400 hover:text-blue-500 transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-2c-.55 0-1 .45-1 1v2h3v3h-3v6.95c5.05-.5 9-4.76 9-9.95z"></path>
                            </svg>
                        </a>
                        <a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-400 hover:text-blue-500 transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-**********-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"></path>
                            </svg>
                        </a>
                        <a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-400 hover:text-blue-500 transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path>
                            </svg>
                        </a>
                        <a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-400 hover:text-blue-500 transition-colors">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-6 text-gray-800">เกี่ยวกับเรา</h3>
                    <ul class="space-y-3">
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">เกี่ยวกับ EduNest</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">ทีมงานของเรา</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">ร่วมงานกับเรา</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">ข่าวสารและกิจกรรม</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">ติดต่อเรา</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-6 text-gray-800">บริการของเรา</h3>
                    <ul class="space-y-3">
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">สำหรับผู้ปกครองและนักเรียน</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">สำหรับโรงเรียน</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">สำหรับครูร่วมสร้าง</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">แพ็กเกจและราคา</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">กล่องกิจกรรม</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-6 text-gray-800">ช่วยเหลือ</h3>
                    <ul class="space-y-3">
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">คำถามที่พบบ่อย</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">วิธีการใช้งาน</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">นโยบายความเป็นส่วนตัว</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">เงื่อนไขการใช้บริการ</a></li>
                        <li><a href="https://bd3bqmx5fzktemtq.canva-hosted-embed.com/codelet/AAEAEGJkM2JxbXg1ZnprdGVtdHEAAAAAAZdOT08FAksRN8f68iocM5w7GEnOwks8-f6ismBL1BMZojDsMKg/#" class="text-gray-500 hover:text-blue-500 transition-colors">ศูนย์ช่วยเหลือ</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-100 mt-12 py-4 text-center text-gray-500">
                <p>Copyright © 2023-2025 EduNest.com. All Rights Reserved.</p>
            </div>
        </div>
    </footer>
 
</body></html>