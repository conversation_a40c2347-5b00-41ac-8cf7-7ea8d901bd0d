<!DOCTYPE html>
<html>
<head>
    <title>SVG Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="p-8">
    <h1 class="text-2xl font-bold mb-4">SVG Component Test</h1>
    
    <div class="space-y-4">
        <div class="p-4 border rounded">
            <h2 class="font-semibold mb-2">Direct Include Test:</h2>
            @php
                $iconPath = 'components.svg.icons.default';
                $iconExists = view()->exists($iconPath);
            @endphp
            
            <p>Icon path: {{ $iconPath }}</p>
            <p>Icon exists: {{ $iconExists ? 'Yes' : 'No' }}</p>
            
            @if($iconExists)
                <div class="mt-2">
                    @include($iconPath, ['color' => '#06b6d4'])
                </div>
            @else
                <p class="text-red-500">Icon not found!</p>
            @endif
        </div>
        
        <div class="p-4 border rounded">
            <h2 class="font-semibold mb-2">Animated SVG Component Test:</h2>
            @include('components.animated-svg', [
                'type' => 'custom-icon',
                'icon' => 'default',
                'size' => '16',
                'animation' => 'pulse-glow-svg',
                'color' => '#06b6d4'
            ])
        </div>
        
        <div class="p-4 border rounded">
            <h2 class="font-semibold mb-2">Built-in Animation Test:</h2>
            @include('components.animated-svg', [
                'type' => 'pulsing-orb',
                'color' => '#06b6d4'
            ])
        </div>
    </div>
</body>
</html>
