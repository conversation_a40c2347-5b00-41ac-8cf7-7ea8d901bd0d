<?php

namespace App\Filament\App\Pages;

use Filament\Pages\Page;

class IconTest extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-star';
    
    protected static string $view = 'filament.app.pages.icon-test';
    
    protected static ?string $navigationGroup = 'Development';
    
    protected static ?string $title = 'Icon Test';
    
    protected static ?int $navigationSort = 999;

    // Hide from navigation in production
    public static function shouldRegisterNavigation(): bool
    {
        return app()->environment(['local', 'development']);
    }

    public function getIconList(): array
    {
        return [
            // Navigation & Interface
            'Navigation' => [
                'home', 'bars-3', 'x-mark', 'chevron-left', 'chevron-right', 
                'chevron-up', 'chevron-down', 'arrow-left', 'arrow-right'
            ],
            
            // Actions & Controls
            'Actions' => [
                'plus', 'minus', 'pencil', 'trash', 'eye', 'eye-slash', 
                'cog-6-tooth', 'adjustments-horizontal'
            ],
            
            // Users & People
            'Users' => [
                'user', 'users', 'user-plus', 'user-minus', 'user-circle', 
                'user-group', 'academic-cap'
            ],
            
            // Communication
            'Communication' => [
                'envelope', 'envelope-open', 'chat-bubble-left', 'phone', 
                'bell', 'megaphone'
            ],
            
            // Documents & Files
            'Documents' => [
                'document', 'document-text', 'document-plus', 'folder', 
                'folder-open', 'clipboard', 'book-open'
            ],
            
            // Time & Status
            'Time & Status' => [
                'clock', 'calendar', 'calendar-days', 'play', 'pause', 
                'stop', 'check', 'check-circle', 'x-circle'
            ],
            
            // Business & Finance
            'Business' => [
                'banknotes', 'credit-card', 'currency-dollar', 'shopping-cart', 
                'shopping-bag', 'gift'
            ],
            
            // Data & Analytics
            'Analytics' => [
                'chart-bar', 'chart-pie', 'presentation-chart-line', 
                'table-cells', 'list-bullet', 'squares-2x2'
            ],
            
            // Technology
            'Technology' => [
                'computer-desktop', 'device-phone-mobile', 'wifi', 'globe-alt', 
                'magnifying-glass', 'link'
            ],
            
            // Miscellaneous
            'Misc' => [
                'star', 'heart', 'tag', 'share', 'printer', 'key', 
                'lock-closed', 'shield-check', 'exclamation-triangle'
            ]
        ];
    }
}
