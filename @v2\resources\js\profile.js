// Profile Page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {

    // Initialize fade-in animation
    initializeFadeIn();

    // Initialize all profile components
    initializeFormValidation();
    initializeFileUpload();
    initializeSubjectsInput();
    initializeFormSubmission();
    initializeProgressTracking();

    // Initialize fade-in animation
    function initializeFadeIn() {
        const profileCard = document.getElementById('profileCard');
        if (profileCard) {
            // Trigger fade-in animation after a short delay
            setTimeout(() => {
                profileCard.classList.add('loaded');
            }, 100);
        }
    }
    
    // Form validation
    function initializeFormValidation() {
        const form = document.getElementById('profileForm');
        if (!form) return;
        
        const inputs = form.querySelectorAll('.form-input, .form-select, .form-textarea');
        
        inputs.forEach(input => {
            input.addEventListener('blur', validateField);
            input.addEventListener('input', clearErrors);
        });
        
        function validateField(e) {
            const field = e.target;
            const value = field.value.trim();
            const fieldName = field.name;
            const isRequired = field.hasAttribute('required') || field.classList.contains('required');
            
            clearFieldErrors(field);
            
            // Required field validation
            if (isRequired && !value) {
                showFieldError(field, 'This field is required');
                return;
            }
            
            // Specific field validations
            switch (fieldName) {
                case 'email':
                    if (value && !isValidEmail(value)) {
                        showFieldError(field, 'Please enter a valid email address');
                    } else if (value) {
                        showFieldSuccess(field);
                    }
                    break;
                    
                case 'phone_number':
                case 'emergency_contact_phone':
                case 'school_phone':
                    if (value && !isValidPhone(value)) {
                        showFieldError(field, 'Please enter a valid phone number');
                    } else if (value) {
                        showFieldSuccess(field);
                    }
                    break;
                    
                case 'date_of_birth':
                    if (value && !isValidDate(value)) {
                        showFieldError(field, 'Please enter a valid date');
                    } else if (value) {
                        showFieldSuccess(field);
                    }
                    break;
                    
                case 'years_of_experience':
                    if (value && (isNaN(value) || value < 0 || value > 50)) {
                        showFieldError(field, 'Please enter a valid number of years (0-50)');
                    } else if (value) {
                        showFieldSuccess(field);
                    }
                    break;
                    
                case 'total_students':
                case 'total_teachers':
                    if (value && (isNaN(value) || value < 0)) {
                        showFieldError(field, 'Please enter a valid number');
                    } else if (value) {
                        showFieldSuccess(field);
                    }
                    break;
                    
                default:
                    if (value && isRequired) {
                        showFieldSuccess(field);
                    }
                    break;
            }
        }
        
        function clearErrors(e) {
            const field = e.target;
            if (field.classList.contains('error')) {
                clearFieldErrors(field);
            }
        }
        
        function showFieldError(field, message) {
            field.classList.add('error');
            field.classList.remove('success');
            
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${message}
            `;
            
            field.parentNode.appendChild(errorDiv);
        }
        
        function showFieldSuccess(field) {
            field.classList.add('success');
            field.classList.remove('error');
        }
        
        function clearFieldErrors(field) {
            field.classList.remove('error', 'success');
            const errorMsg = field.parentNode.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.remove();
            }
        }
        
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        function isValidPhone(phone) {
            const phoneRegex = /^[\d\s\-\+\(\)]{10,}$/;
            return phoneRegex.test(phone);
        }
        
        function isValidDate(date) {
            const dateObj = new Date(date);
            return dateObj instanceof Date && !isNaN(dateObj) && dateObj < new Date();
        }
    }
    
    // File upload handling
    function initializeFileUpload() {
        const fileInput = document.getElementById('profile_image');
        const fileLabel = document.querySelector('.file-upload-label');
        const profileAvatar = document.querySelector('.profile-avatar');
        
        if (!fileInput || !fileLabel) return;
        
        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            
            if (file) {
                // Validate file type
                if (!file.type.startsWith('image/')) {
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification('Please select an image file', 'error');
                    }
                    return;
                }
                
                // Validate file size (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    if (window.LayoutUtils) {
                        window.LayoutUtils.showNotification('Image size must be less than 2MB', 'error');
                    }
                    return;
                }
                
                // Update label text
                fileLabel.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    ${file.name}
                `;
                
                // Preview image
                if (profileAvatar) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        profileAvatar.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            }
        });
    }
    
    // Subjects input for teachers
    function initializeSubjectsInput() {
        const subjectsContainer = document.querySelector('.subjects-container');
        const subjectInput = document.querySelector('.subject-input');
        
        if (!subjectsContainer || !subjectInput) return;
        
        let subjects = [];
        
        // Load existing subjects
        const existingSubjects = document.querySelectorAll('.subject-tag');
        existingSubjects.forEach(tag => {
            const text = tag.textContent.trim().replace('×', '');
            if (text) {
                subjects.push(text);
            }
        });
        
        subjectInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ',') {
                e.preventDefault();
                addSubject();
            }
        });
        
        subjectInput.addEventListener('blur', addSubject);
        
        function addSubject() {
            const value = subjectInput.value.trim();
            
            if (value && !subjects.includes(value)) {
                subjects.push(value);
                renderSubjects();
                subjectInput.value = '';
                updateHiddenInput();
            }
        }
        
        function removeSubject(subject) {
            subjects = subjects.filter(s => s !== subject);
            renderSubjects();
            updateHiddenInput();
        }
        
        function renderSubjects() {
            const tagsHtml = subjects.map(subject => `
                <span class="subject-tag">
                    ${subject}
                    <button type="button" class="subject-remove" onclick="removeSubject('${subject}')">×</button>
                </span>
            `).join('');
            
            subjectsContainer.innerHTML = tagsHtml + `
                <input type="text" class="subject-input" placeholder="Add subject and press Enter">
            `;
            
            // Re-attach event listeners
            const newInput = subjectsContainer.querySelector('.subject-input');
            newInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addSubject();
                }
            });
            newInput.addEventListener('blur', addSubject);
        }
        
        function updateHiddenInput() {
            // Create or update hidden inputs for subjects
            const existingInputs = document.querySelectorAll('input[name="subjects[]"]');
            existingInputs.forEach(input => input.remove());
            
            subjects.forEach(subject => {
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = 'subjects[]';
                hiddenInput.value = subject;
                subjectsContainer.appendChild(hiddenInput);
            });
        }
        
        // Make removeSubject globally available
        window.removeSubject = removeSubject;
    }
    
    // Form submission
    function initializeFormSubmission() {
        const form = document.getElementById('profileForm');
        const submitBtn = document.getElementById('submitBtn');

        // Form validation and submission setup

        if (!form || !submitBtn) return;
        
        form.addEventListener('submit', function(e) {
            // Don't prevent default - let the form submit normally
            // Just validate before submission
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            showButtonLoading(submitBtn, 'Saving Profile...');
        });
        
        function validateForm() {
            const requiredInputs = form.querySelectorAll('.form-input[required], .form-select[required], .form-textarea[required]');
            let isValid = true;
            
            requiredInputs.forEach(input => {
                const event = new Event('blur');
                input.dispatchEvent(event);
                
                if (input.classList.contains('error') || !input.value.trim()) {
                    isValid = false;
                }
            });
            
            return isValid;
        }
        
        // Form submission is now handled by normal form submission
    }
    
    // Progress tracking
    function initializeProgressTracking() {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-percentage');
        
        if (!progressFill || !progressText) return;
        
        // Calculate completion percentage
        const form = document.getElementById('profileForm');
        if (!form) return;
        
        const allInputs = form.querySelectorAll('.form-input, .form-select, .form-textarea');
        const requiredInputs = form.querySelectorAll('.form-input[required], .form-select[required], .form-textarea[required]');
        
        function updateProgress() {
            let completed = 0;
            
            requiredInputs.forEach(input => {
                if (input.value.trim()) {
                    completed++;
                }
            });
            
            const percentage = Math.round((completed / requiredInputs.length) * 100);
            
            progressFill.style.width = percentage + '%';
            progressText.textContent = percentage + '%';
        }
        
        // Update progress on input changes
        allInputs.forEach(input => {
            input.addEventListener('input', updateProgress);
        });
        
        // Initial progress calculation
        updateProgress();
    }
    
    // Utility functions
    function showButtonLoading(button, text) {
        button.disabled = true;
        button.innerHTML = `
            <div class="loading">
                <div class="loading-spinner"></div>
                ${text}
            </div>
        `;
    }
    
    function resetButtonLoading(button, originalText) {
        button.disabled = false;
        button.innerHTML = originalText;
    }

    // Global function for skip button
    window.skipProfile = function() {
        if (confirm('Are you sure you want to skip profile completion? You can complete it later from your dashboard.')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/profile/skip';

            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    };

    // Global function for disconnecting social accounts
    window.disconnectSocialAccount = function(provider, providerName) {
        if (confirm(`Are you sure you want to disconnect your ${providerName} account? You will no longer be able to login using ${providerName}.`)) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/profile/disconnect/${provider}`;

            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = '_token';
                csrfInput.value = csrfToken.getAttribute('content');
                form.appendChild(csrfInput);
            }

            document.body.appendChild(form);
            form.submit();
        }
    };
});
