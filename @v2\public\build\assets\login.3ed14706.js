document.addEventListener("DOMContentLoaded",function(){L(),g(),v(),E();function L(){document.getElementById("loginForm").querySelectorAll(".form-input").forEach(t=>{t.addEventListener("blur",a),t.addEventListener("input",c)});function a(t){const o=t.target,l=o.value.trim(),n=o.name;switch(e(o),n){case"email":l?f(l)?d(o):s(o,"\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E2D\u0E35\u0E40\u0E21\u0E25\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"):s(o,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2D\u0E35\u0E40\u0E21\u0E25");break;case"password":l?l.length<6?s(o,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E2D\u0E22\u0E48\u0E32\u0E07\u0E19\u0E49\u0E2D\u0E22 6 \u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23"):d(o):s(o,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19");break}}function c(t){const o=t.target;o.classList.contains("error")&&e(o)}function s(t,o){t.classList.add("error"),t.classList.remove("success");const l=document.createElement("div");l.className="error-message",l.innerHTML=`
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${o}
            `,t.parentNode.appendChild(l)}function d(t){t.classList.add("success"),t.classList.remove("error")}function e(t){t.classList.remove("error","success");const o=t.parentNode.querySelector(".error-message");o&&o.remove()}function f(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}}function g(){document.querySelectorAll('.social-button:not([id="phoneLogin"])').forEach(a=>{const c=a.id.replace("Login","");a.addEventListener("click",()=>r(c))});function r(a){const c=document.getElementById(a+"Login");w(c,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E40\u0E0A\u0E37\u0E48\u0E2D\u0E21\u0E15\u0E48\u0E2D..."),window.location.href=`/auth/${a}`}}function v(){const i=document.getElementById("phoneLogin"),r=document.getElementById("phoneModal"),a=document.getElementById("closePhoneModal"),c=document.getElementById("sendOtp"),s=document.getElementById("verifyOtp"),d=document.getElementById("phoneNumber"),e=document.querySelectorAll(".otp-input");i&&i.addEventListener("click",function(){r.classList.remove("hidden"),d.focus()}),a&&a.addEventListener("click",function(){r.classList.add("hidden"),o()}),r&&r.addEventListener("click",function(n){n.target===this&&(this.classList.add("hidden"),o())}),c&&c.addEventListener("click",f),s&&s.addEventListener("click",t),e.forEach((n,u)=>{n.addEventListener("input",function(m){m.target.value.length===1&&u<e.length-1&&e[u+1].focus()}),n.addEventListener("keydown",function(m){m.key==="Backspace"&&m.target.value===""&&u>0&&e[u-1].focus()})});async function f(){const n=d.value.trim(),u=document.getElementById("countryCode").value;if(!n){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C","error");return}if(!l(n)){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error");return}w(c,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E48\u0E07 OTP...");try{const h=await(await fetch("/api/auth/phone/send-otp",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({phone:n,country_code:u})})).json();h.success?(document.getElementById("phoneStep").classList.add("hidden"),document.getElementById("otpStep").classList.remove("hidden"),window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2A\u0E48\u0E07 OTP \u0E44\u0E1B\u0E22\u0E31\u0E07 "+u+n+" \u0E41\u0E25\u0E49\u0E27","success"),e[0].focus()):window.LayoutUtils&&window.LayoutUtils.showNotification(h.message||"\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14","error")}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E2A\u0E48\u0E07 OTP","error")}p(c,"\u0E2A\u0E48\u0E07 OTP")}async function t(){const n=Array.from(e).map(h=>h.value).join(""),u=d.value.trim(),m=document.getElementById("countryCode").value;if(n.length!==6){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01 OTP \u0E43\u0E2B\u0E49\u0E04\u0E23\u0E1A 6 \u0E2B\u0E25\u0E31\u0E01","error");return}w(s,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E15\u0E23\u0E27\u0E08\u0E2A\u0E2D\u0E1A...");try{const y=await(await fetch("/api/auth/phone/verify-otp",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({otp:n,phone:m+u})})).json();y.success?(window.LayoutUtils&&window.LayoutUtils.showNotification(y.message,"success"),setTimeout(()=>{window.location.href=y.redirect||"/dashboard"},1500)):(window.LayoutUtils&&window.LayoutUtils.showNotification(y.message||"\u0E23\u0E2B\u0E31\u0E2A OTP \u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error"),e.forEach(B=>B.value=""),e[0].focus())}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E15\u0E23\u0E27\u0E08\u0E2A\u0E2D\u0E1A OTP","error")}p(s,"\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19 OTP")}function o(){document.getElementById("phoneStep").classList.remove("hidden"),document.getElementById("otpStep").classList.add("hidden"),d.value="",e.forEach(n=>n.value="")}function l(n){return/^[0-9]{9,10}$/.test(n)}}function E(){const i=document.getElementById("loginForm"),r=document.getElementById("submitBtn");i&&i.addEventListener("submit",function(s){s.preventDefault(),a()&&c()});function a(){const s=i.querySelectorAll(".form-input[required]");let d=!0;return s.forEach(e=>{const f=new Event("blur");e.dispatchEvent(f),(e.classList.contains("error")||!e.value.trim())&&(d=!1)}),d}async function c(){w(r,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A...");try{const s=new FormData(i),e=await(await fetch("/api/login",{method:"POST",headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content"),Accept:"application/json"},body:s})).json();e.success?(window.LayoutUtils&&window.LayoutUtils.showNotification(e.message,"success"),setTimeout(()=>{window.location.href=e.redirect},1500)):(window.LayoutUtils&&window.LayoutUtils.showNotification(e.message||"\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14","error"),e.errors&&Object.keys(e.errors).forEach(f=>{const t=i.querySelector(`[name="${f}"]`);t&&showFieldError(t,e.errors[f][0])}))}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A","error")}p(r,"\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A")}}function w(i,r){i.disabled=!0,i.innerHTML=`
            <div class="loading">
                <div class="loading-spinner"></div>
                ${r}
            </div>
        `}function p(i,r){i.disabled=!1,i.innerHTML=r}});
