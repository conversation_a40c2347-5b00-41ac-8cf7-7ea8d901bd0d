# Role-Based Panel Architecture

This document explains the role-based panel architecture implemented for the education platform, where **Team = School**.

## Panel Structure

### 🏢 **Backend Panel** (`/backend`)
- **Purpose:** System administration and global management
- **Users:** Super admins only (`team_id = null` and `super_admin` role)
- **Features:**
  - Manage all schools/teams
  - User management across all schools
  - Role and permission management
  - System configuration
  - Global reports and analytics

### 🏫 **App Panel** (`/app/{school-slug}`)
- **Purpose:** School-specific operations and daily management
- **Users:** All school users (teachers, parents, students, school admins)
- **Tenant:** Team (School) - each school is isolated
- **Role-Based Experience:** Different dashboards and navigation based on user role

## Role-Based Features in App Panel

### 👨‍💼 **School Admin** (`team_admin` or `school` role)
**Navigation Groups:**
- Dashboard
- School Management
- Academic Management
- User Management
- Communication
- Reports & Analytics
- Settings

**Dashboard Features:**
- School overview statistics
- Student enrollment metrics
- Teacher performance stats
- Recent activities

**Quick Actions:**
- Add Teacher
- Enroll Student
- View Reports
- Settings

### 👨‍🏫 **Teacher** (`teacher` role)
**Navigation Groups:**
- Dashboard
- My Classes
- Students & Grades
- Assignments
- Communication
- Reports

**Dashboard Features:**
- Class overview
- Student count across classes
- Pending grading tasks
- Today's schedule

**Quick Actions:**
- Take Attendance
- Create Assignment
- Grade Work
- Message Parents

### 👨‍👩‍👧‍👦 **Parent** (`parent` role)
**Navigation Groups:**
- Dashboard
- My Children
- Academic Progress
- Communication
- Payments
- Events & Calendar

**Dashboard Features:**
- Children's progress overview
- Upcoming events
- Payment status
- Communication summary

**Quick Actions:**
- View Progress
- Messages
- Payments
- Events

### 🎓 **Student** (`student` role)
**Navigation Groups:**
- Dashboard
- My Courses
- Assignments
- Grades
- Schedule
- Resources

**Dashboard Features:**
- Personal academic overview
- Assignment due dates
- Grade summaries
- Today's schedule

**Quick Actions:**
- Assignments
- My Grades
- Schedule
- Resources

## Technical Implementation

### Panel Configuration
```php
// app/Providers/Filament/AppPanelProvider.php
->navigationGroups($this->getNavigationGroupsForUser())
->brandName(fn() => auth()->user()?->team?->name ?? 'School Portal')
->tenant(Team::class, ownershipRelationship: 'team', slugAttribute: 'slug')
```

### Role-Based Dashboard
```php
// app/Filament/App/Pages/Dashboard.php
public function getWidgets(): array
{
    $user = auth()->user();
    
    if ($user->hasRole('team_admin') || $user->hasRole('school')) {
        return [/* School admin widgets */];
    }
    
    if ($user->hasRole('teacher')) {
        return [/* Teacher widgets */];
    }
    
    // ... other roles
}
```

### Widget Access Control
```php
// Example: app/Filament/App/Widgets/SchoolOverviewWidget.php
public static function canView(): bool
{
    $user = Auth::user();
    return $user && ($user->hasRole('team_admin') || $user->hasRole('school'));
}
```

## Benefits of This Architecture

### ✅ **Advantages**
1. **Single Codebase:** Easier maintenance and updates
2. **Shared Resources:** Common components can be reused
3. **Tenant Isolation:** Each school's data is completely isolated
4. **Role Flexibility:** Easy to add new roles or modify permissions
5. **Consistent UI/UX:** Same design system across all roles
6. **Simplified Deployment:** Only two panels to manage

### 🎯 **User Experience**
- **Personalized:** Each role sees only relevant features
- **Intuitive:** Navigation and actions match user responsibilities
- **Efficient:** Quick actions for common tasks
- **Contextual:** School branding and information displayed

### 🔧 **Developer Experience**
- **Maintainable:** Role logic centralized in panel provider
- **Extensible:** Easy to add new roles or modify existing ones
- **Testable:** Clear separation of concerns
- **Scalable:** Can handle multiple schools with different configurations

## URL Structure

```
/backend                    # Super admin panel
/app/{school-slug}         # School-specific panel
/app/riverside-high        # Example: Riverside High School
/app/downtown-elementary   # Example: Downtown Elementary
```

## Security Features

1. **Tenant Isolation:** Users can only access their school's data
2. **Role-Based Access:** Features restricted by user role
3. **Permission Checks:** Widget and resource-level access control
4. **Super Admin Override:** Super admins can access any school
5. **Middleware Protection:** Multiple layers of access control

## Future Enhancements

- [ ] Role-specific resource filtering
- [ ] Custom navigation per school
- [ ] School-specific themes and branding
- [ ] Advanced reporting per role
- [ ] Mobile-responsive role dashboards
- [ ] Real-time notifications per role
- [ ] Integration with school management systems

## Migration from Separate Panels

If you later decide you need separate panels, the current architecture makes it easy to:
1. Extract role-specific resources to new panels
2. Maintain shared components
3. Keep the same URL structure
4. Preserve user experience

This approach gives you the flexibility to evolve the architecture as your needs grow while maintaining a clean, maintainable codebase.
