<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BookResource\Pages;
use App\Models\Book;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class BookResource extends Resource
{
    protected static ?string $model = Book::class;

    protected static ?string $navigationIcon = 'heroicon-o-book-open';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationLabel = 'Books';

    protected static ?string $modelLabel = 'Book';

    protected static ?string $pluralModelLabel = 'Books';

    // Specify the relationship name on the tenant model
    protected static ?string $tenantRelationshipName = 'books';

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering based on current tenant
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->where('team_id', Filament::getTenant()->id);
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        
                        Forms\Components\TextInput::make('author')
                            ->required()
                            ->maxLength(255)
                            ->columnSpan(2),
                        
                        Forms\Components\Select::make('subject_id')
                            ->label('Subject')
                            ->relationship('subject', 'name', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->where('is_active', true);
                            })
                            ->required()
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('code')
                                    ->maxLength(255),
                                Forms\Components\Select::make('level')
                                    ->options([
                                        'elementary' => 'Elementary',
                                        'middle' => 'Middle School',
                                        'high' => 'High School',
                                        'university' => 'University',
                                    ]),
                            ])
                            ->columnSpan(2),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Publication Details')
                    ->schema([
                        Forms\Components\TextInput::make('isbn')
                            ->label('ISBN')
                            ->maxLength(255)
                            ->placeholder('978-0-123456-78-9')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('publisher')
                            ->maxLength(255)
                            ->columnSpan(1),
                        
                        Forms\Components\DatePicker::make('published_date')
                            ->label('Publication Date')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('edition')
                            ->maxLength(255)
                            ->placeholder('1st, 2nd, etc.')
                            ->columnSpan(1),
                        
                        Forms\Components\TextInput::make('pages')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(9999)
                            ->columnSpan(1),
                        
                        Forms\Components\Select::make('language')
                            ->options([
                                'English' => 'English',
                                'Thai' => 'Thai',
                                'Chinese' => 'Chinese',
                                'Japanese' => 'Japanese',
                                'French' => 'French',
                                'Spanish' => 'Spanish',
                                'German' => 'German',
                            ])
                            ->default('English')
                            ->columnSpan(1),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->columnSpan(2),
                    ])
                    ->columns(4),

                Forms\Components\Section::make('Description')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->rows(4)
                            ->placeholder('Brief description of the book content and objectives')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('author')
                    ->searchable()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->badge()
                    ->color('primary')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('isbn')
                    ->label('ISBN')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('publisher')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('published_date')
                    ->label('Published')
                    ->date()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('language')
                    ->badge()
                    ->color('gray'),
                
                Tables\Columns\TextColumn::make('lessons_count')
                    ->label('Lessons')
                    ->counts('lessons')
                    ->badge()
                    ->color('info')
                    ->sortable(),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('subject_id')
                    ->label('Subject')
                    ->relationship('subject', 'name', function (Builder $query) {
                        if (Filament::hasTenancy() && Filament::getTenant()) {
                            $query->where('team_id', Filament::getTenant()->id);
                        }
                        return $query->where('is_active', true);
                    }),
                
                Tables\Filters\SelectFilter::make('language')
                    ->options([
                        'English' => 'English',
                        'Thai' => 'Thai',
                        'Chinese' => 'Chinese',
                        'Japanese' => 'Japanese',
                        'French' => 'French',
                        'Spanish' => 'Spanish',
                        'German' => 'German',
                    ]),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('title', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            BookResource\RelationManagers\LessonsRelationManager::class,
            BookResource\RelationManagers\LiveVideosRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBooks::route('/'),
            'create' => Pages\CreateBook::route('/create'),
            'view' => Pages\ViewBook::route('/{record}'),
            'edit' => Pages\EditBook::route('/{record}/edit'),
        ];
    }
}
