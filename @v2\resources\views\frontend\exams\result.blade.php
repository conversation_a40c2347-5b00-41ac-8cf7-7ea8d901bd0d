@extends('layouts.frontend')

@section('title', 'Exam Result: ' . $exam->title)

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="{{ route('frontend.exams.show', $exam) }}" 
               class="inline-flex items-center text-blue-600 hover:text-blue-800">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to Exam
            </a>
        </div>

        <!-- Result Header -->
        <div class="bg-white rounded-lg shadow-sm p-8 mb-6">
            <div class="text-center">
                <div class="mb-4">
                    @if($attempt->total_score >= ($exam->total_score * 0.8))
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h1 class="text-2xl font-bold text-green-600 mb-2">Excellent Work!</h1>
                    @elseif($attempt->total_score >= ($exam->total_score * 0.6))
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h1 class="text-2xl font-bold text-yellow-600 mb-2">Good Job!</h1>
                    @else
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <h1 class="text-2xl font-bold text-red-600 mb-2">Keep Trying!</h1>
                    @endif
                </div>
                
                <h2 class="text-xl font-semibold text-gray-900 mb-4">{{ $exam->title }}</h2>
                
                <!-- Score Display -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="text-3xl font-bold text-blue-600">{{ $attempt->total_score ?? 0 }}</div>
                        <div class="text-sm text-gray-500">Your Score</div>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-3xl font-bold text-gray-600">{{ $exam->total_score }}</div>
                        <div class="text-sm text-gray-500">Total Points</div>
                    </div>
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="text-3xl font-bold text-green-600">
                            {{ $exam->total_score > 0 ? round(($attempt->total_score / $exam->total_score) * 100, 1) : 0 }}%
                        </div>
                        <div class="text-sm text-gray-500">Percentage</div>
                    </div>
                </div>

                <!-- Attempt Info -->
                <div class="text-sm text-gray-500 space-y-1">
                    <div>Attempt #{{ $attempt->attempt_number }}</div>
                    <div>Submitted: {{ $attempt->submitted_at->format('M d, Y H:i') }}</div>
                    @if($attempt->started_at && $attempt->submitted_at)
                        <div>Time Taken: {{ $attempt->started_at->diffInMinutes($attempt->submitted_at) }} minutes</div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Teacher Feedback -->
        @if($attempt->teacher_feedback)
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Teacher Feedback</h3>
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                    <p class="text-gray-700">{{ $attempt->teacher_feedback }}</p>
                </div>
            </div>
        @endif

        <!-- Question Review -->
        @if($exam->show_results_immediately || $attempt->isGraded())
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">Question Review</h3>
                
                <div class="space-y-6">
                    @foreach($attempt->answers as $answer)
                        <div class="border-b border-gray-200 pb-6 last:border-b-0">
                            <!-- Question -->
                            <div class="mb-4">
                                <div class="flex items-start justify-between mb-2">
                                    <h4 class="text-base font-medium text-gray-900">
                                        Question {{ $loop->iteration }}
                                    </h4>
                                    <div class="flex items-center space-x-2">
                                        @if($answer->is_correct === true)
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">Correct</span>
                                        @elseif($answer->is_correct === false)
                                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm">Incorrect</span>
                                        @else
                                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">Pending Review</span>
                                        @endif
                                        <span class="text-sm text-gray-500">
                                            {{ $answer->score ?? 0 }}/{{ $answer->max_score }} pts
                                        </span>
                                    </div>
                                </div>
                                <p class="text-gray-700 mb-4">{{ $answer->question->question_text }}</p>
                            </div>

                            <!-- User's Answer -->
                            <div class="mb-4">
                                <h5 class="text-sm font-medium text-gray-900 mb-2">Your Answer:</h5>
                                @if($answer->question->question_type === 'multiple_choice')
                                    @if($answer->selectedChoice)
                                        <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                                            {{ $answer->selectedChoice->choice_text }}
                                        </div>
                                    @else
                                        <div class="p-3 bg-gray-50 border border-gray-200 rounded text-gray-500">
                                            No answer selected
                                        </div>
                                    @endif
                                @else
                                    <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                                        {{ $answer->answer_text ?: 'No answer provided' }}
                                    </div>
                                @endif
                            </div>

                            <!-- Correct Answer (for multiple choice) -->
                            @if($answer->question->question_type === 'multiple_choice')
                                <div class="mb-4">
                                    <h5 class="text-sm font-medium text-gray-900 mb-2">Correct Answer:</h5>
                                    @foreach($answer->question->choices->where('is_correct', true) as $correctChoice)
                                        <div class="p-3 bg-green-50 border border-green-200 rounded">
                                            {{ $correctChoice->choice_text }}
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            <!-- Explanation -->
                            @if($answer->question->explanation)
                                <div class="mb-4">
                                    <h5 class="text-sm font-medium text-gray-900 mb-2">Explanation:</h5>
                                    <div class="p-3 bg-gray-50 border border-gray-200 rounded">
                                        {{ $answer->question->explanation }}
                                    </div>
                                </div>
                            @endif

                            <!-- Teacher Comment -->
                            @if($answer->teacher_comment)
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900 mb-2">Teacher Comment:</h5>
                                    <div class="p-3 bg-yellow-50 border border-yellow-200 rounded">
                                        {{ $answer->teacher_comment }}
                                    </div>
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        @else
            <div class="bg-white rounded-lg shadow-sm p-6 text-center">
                <div class="text-gray-500 mb-4">
                    <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Results Not Available</h3>
                <p class="text-gray-500">Detailed results will be available after the teacher reviews your answers.</p>
            </div>
        @endif

        <!-- Actions -->
        <div class="mt-8 text-center space-x-4">
            <a href="{{ route('frontend.exams.index') }}" 
               class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700">
                Browse More Exams
            </a>
            
            @if($exam->allow_multiple_attempts)
                <a href="{{ route('frontend.exams.show', $exam) }}" 
                   class="inline-flex items-center px-6 py-3 bg-green-600 text-white font-medium rounded-md hover:bg-green-700">
                    Take Again
                </a>
            @endif
        </div>
    </div>
</div>
@endsection
