@extends('layouts.frontend')

@section('title', 'PromptPay QR Code Payment')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-purple-600 px-6 py-4">
                <h1 class="text-2xl font-bold text-white">PromptPay QR Code Payment</h1>
                <p class="text-purple-100 mt-1">Scan the QR code with your mobile banking app</p>
            </div>

            <div class="p-6">
                <!-- Payment Details -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Payment Amount</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p class="text-2xl font-bold">฿{{ number_format($payment->amount * 35, 2) }} THB</p>
                                <p class="text-sm">Payment Reference: <span class="font-mono">{{ $promptPayDetails['reference'] }}</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QR Code -->
                <div class="text-center mb-6">
                    <div class="inline-block bg-white p-6 rounded-lg shadow-md border-2 border-gray-200">
                        <div class="mb-4">
                            <h2 class="text-lg font-semibold text-gray-900">Scan QR Code</h2>
                            <p class="text-sm text-gray-600">Use your mobile banking app to scan this code</p>
                        </div>
                        
                        <div class="flex justify-center">
                            {!! $promptPayDetails['qr_code'] !!}
                        </div>
                        
                        <div class="mt-4 text-xs text-gray-500">
                            <p>PromptPay ID: {{ $promptPayDetails['promptpay_id'] }}</p>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">How to Pay</h3>
                            <div class="mt-2 text-sm text-green-700">
                                <ol class="list-decimal list-inside space-y-1">
                                    <li>Open your mobile banking app</li>
                                    <li>Select "QR Payment" or "Scan QR Code"</li>
                                    <li>Scan the QR code above</li>
                                    <li>Verify the amount and recipient</li>
                                    <li>Complete the payment</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Supported Banks -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-900 mb-3">Supported Banking Apps</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <div class="text-center p-3 bg-gray-50 rounded-lg">
                            <div class="text-xs text-gray-600">Bangkok Bank</div>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded-lg">
                            <div class="text-xs text-gray-600">Kasikorn Bank</div>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded-lg">
                            <div class="text-xs text-gray-600">Siam Commercial Bank</div>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded-lg">
                            <div class="text-xs text-gray-600">Krung Thai Bank</div>
                        </div>
                    </div>
                </div>

                <!-- Payment Status -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Payment Status</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Your payment is currently <strong>pending</strong>. Your subscription will be activated automatically once payment is confirmed (usually within a few minutes).</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4">
                    <button onclick="checkPaymentStatus()" class="flex-1 bg-purple-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2">
                        Check Payment Status
                    </button>
                    <a href="{{ route('dashboard') }}" class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 text-center">
                        Go to Dashboard
                    </a>
                </div>

                <!-- Contact Information -->
                <div class="mt-8 text-center text-sm text-gray-600">
                    <p>Payment not working? Contact our support team at <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-purple-500"><EMAIL></a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function checkPaymentStatus() {
    // In a real implementation, you would make an AJAX call to check payment status
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Checking...';
    button.disabled = true;
    
    setTimeout(() => {
        // Simulate checking payment status
        alert('Payment is still pending. Please complete the payment using your mobile banking app.');
        button.textContent = originalText;
        button.disabled = false;
    }, 2000);
}

// Auto-refresh payment status every 30 seconds
setInterval(() => {
    // In a real implementation, you would check payment status via AJAX
    console.log('Checking payment status...');
}, 30000);
</script>
@endsection
