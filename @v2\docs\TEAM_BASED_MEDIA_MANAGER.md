# Team-Based Media Manager Implementation

## Overview

The media manager has been enhanced to support team-based filtering, ensuring that each team can only access their own media files and folders. This implementation provides complete isolation between teams while maintaining the flexibility for super admins to access all content.

## What Was Implemented

### 1. Custom Models with Team Support

#### `app/Models/MediaManager/Media.php`
- Extends the base TomatoPHP Media model
- Adds `BelongsToTeam` trait for automatic team scoping
- Implements team-based global scopes
- Provides methods for team-specific media queries

#### `app/Models/MediaManager/Folder.php`
- Extends the base TomatoPHP Folder model
- Adds `BelongsToTeam` trait for automatic team scoping
- Maintains user access control while adding team filtering
- Provides methods for team-specific folder queries

### 2. Custom Form Component

#### `app/Forms/Components/TeamMediaManagerInput.php`
- Extends the base MediaManagerInput component
- Automatically filters media and folders by current team
- Ensures new media is assigned to the correct team
- Provides team-aware media selection

### 3. Database Seeder

#### `database/seeders/MediaSeeder.php`
- Creates sample folders and media for each team
- Updates existing media records with random team_id values
- Generates realistic test data for development

### 4. Configuration Updates

- Updated `config/filament-media-manager.php` to use custom models
- Enhanced AdminPanelProvider with sub-folders and user access features

## How Team Filtering Works

### Automatic Team Assignment
When creating new media or folders:
1. **With Tenant Context**: Uses current tenant's team_id
2. **Without Tenant**: Uses authenticated user's team_id
3. **Super Admin**: Can optionally set team_id manually

### Query Filtering
All media and folder queries are automatically filtered by:
1. **Current Tenant**: If in tenant context, shows only tenant's content
2. **User's Team**: If no tenant context, shows user's team content
3. **Super Admin Exception**: Super admins without tenant context can see all content

### Global Scopes
Both models use global scopes to ensure:
- Team isolation is maintained at the database level
- No accidental cross-team data access
- Consistent filtering across all queries

## Usage Examples

### 1. Using the Team-Aware Media Manager Input

```php
<?php

namespace App\Filament\Resources;

use App\Forms\Components\TeamMediaManagerInput;
use Filament\Forms;
use Filament\Forms\Form;

class ProductResource extends Resource
{
    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('name')
                ->required(),
            
            // Team-filtered media manager
            TeamMediaManagerInput::make('product_images')
                ->label('Product Images')
                ->disk('public')
                ->teamAware() // Enable team-specific filtering
                ->schema([
                    Forms\Components\TextInput::make('alt_text')
                        ->label('Alt Text')
                        ->maxLength(255),
                    Forms\Components\Select::make('image_type')
                        ->label('Image Type')
                        ->options([
                            'primary' => 'Primary Image',
                            'gallery' => 'Gallery Image',
                            'thumbnail' => 'Thumbnail',
                        ])
                        ->required(),
                ]),
        ]);
    }
}
```

### 2. Direct Model Usage

```php
use App\Models\MediaManager\Media;
use App\Models\MediaManager\Folder;

// Get media for current team
$teamMedia = Media::forCurrentTeam()->get();

// Get folders for specific team
$teamFolders = Folder::forTeam($teamId)->get();

// Get media for a specific folder (team-filtered)
$folderMedia = Media::forCurrentTeam()
    ->forFolder($folderId)
    ->get();
```

### 3. In Filament Resources

```php
public static function getEloquentQuery(): Builder
{
    // Media and folders are automatically team-filtered
    // No additional filtering needed
    return parent::getEloquentQuery();
}
```

## Running the Seeder

To populate your database with sample team-based media:

```bash
# Run just the media seeder
php artisan db:seed --class=MediaSeeder

# Or run the full database seeder (includes media seeder)
php artisan db:seed
```

The seeder will:
- Create sample folders for each team (Product Images, Documents, Marketing Materials)
- Generate sample media files for each folder
- Update any existing media/folders with random team_id values

## Team Isolation Features

### 1. Complete Data Separation
- Each team can only see their own media and folders
- No cross-team data leakage
- Automatic filtering at the database level

### 2. Super Admin Override
- Super admins without tenant context can access all content
- Useful for system administration and debugging
- Maintains security for regular users

### 3. Tenant-Aware Operations
- All create/update operations respect current tenant
- Automatic team_id assignment
- Consistent behavior across the application

## Security Considerations

### 1. Global Scopes
- Applied at the model level for consistent filtering
- Cannot be bypassed accidentally in queries
- Provides defense in depth

### 2. Form Component Security
- Team filtering applied at the component level
- Prevents selection of other teams' media
- Automatic team assignment for new uploads

### 3. Access Control
- Integrates with existing user access control
- Maintains folder password protection
- Respects user permissions and roles

## Troubleshooting

### Media Not Showing
1. Check if user has correct team_id assigned
2. Verify tenant context is properly set
3. Ensure media records have team_id populated

### Cross-Team Access Issues
1. Verify global scopes are working correctly
2. Check if user has super_admin role
3. Confirm tenant middleware is functioning

### Seeder Issues
1. Ensure teams exist before running MediaSeeder
2. Check LocalImages class has sample images
3. Verify storage permissions are correct

## Migration Notes

If you have existing media that needs team assignment:

```php
// Update existing media with team_id
use App\Models\MediaManager\Media;
use App\Models\Team;

$teams = Team::all();
Media::whereNull('team_id')->chunk(100, function ($media) use ($teams) {
    foreach ($media as $item) {
        $item->update(['team_id' => $teams->random()->id]);
    }
});
```
