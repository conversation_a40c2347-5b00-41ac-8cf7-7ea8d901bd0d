function _(u,t){if(u==null)return{};var e={},s=Object.keys(u),r,i;for(i=0;i<s.length;i++)r=s[i],!(t.indexOf(r)>=0)&&(e[r]=u[r]);return e}function h(u){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return new h.InputMask(u,t)}var p=class{constructor(t){Object.assign(this,{inserted:"",rawInserted:"",skip:!1,tailShift:0},t)}aggregate(t){return this.rawInserted+=t.rawInserted,this.skip=this.skip||t.skip,this.inserted+=t.inserted,this.tailShift+=t.tailShift,this}get offset(){return this.tailShift+this.inserted.length}};h.ChangeDetails=p;function v(u){return typeof u=="string"||u instanceof String}var a={NONE:"NONE",LEFT:"LEFT",FORCE_LEFT:"FORCE_LEFT",RIGHT:"RIGHT",FORCE_RIGHT:"FORCE_RIGHT"};function $(u){switch(u){case a.LEFT:return a.FORCE_LEFT;case a.RIGHT:return a.FORCE_RIGHT;default:return u}}function O(u){return u.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function C(u){return Array.isArray(u)?u:[u,new p]}function M(u,t){if(t===u)return!0;var e=Array.isArray(t),s=Array.isArray(u),r;if(e&&s){if(t.length!=u.length)return!1;for(r=0;r<t.length;r++)if(!M(t[r],u[r]))return!1;return!0}if(e!=s)return!1;if(t&&u&&typeof t=="object"&&typeof u=="object"){var i=t instanceof Date,n=u instanceof Date;if(i&&n)return t.getTime()==u.getTime();if(i!=n)return!1;var o=t instanceof RegExp,l=u instanceof RegExp;if(o&&l)return t.toString()==u.toString();if(o!=l)return!1;var d=Object.keys(t);for(r=0;r<d.length;r++)if(!Object.prototype.hasOwnProperty.call(u,d[r]))return!1;for(r=0;r<d.length;r++)if(!M(u[d[r]],t[d[r]]))return!1;return!0}else if(t&&u&&typeof t=="function"&&typeof u=="function")return t.toString()===u.toString();return!1}var y=class{constructor(t,e,s,r){for(this.value=t,this.cursorPos=e,this.oldValue=s,this.oldSelection=r;this.value.slice(0,this.startChangePos)!==this.oldValue.slice(0,this.startChangePos);)--this.oldSelection.start}get startChangePos(){return Math.min(this.cursorPos,this.oldSelection.start)}get insertedCount(){return this.cursorPos-this.startChangePos}get inserted(){return this.value.substr(this.startChangePos,this.insertedCount)}get removedCount(){return Math.max(this.oldSelection.end-this.startChangePos||this.oldValue.length-this.value.length,0)}get removed(){return this.oldValue.substr(this.startChangePos,this.removedCount)}get head(){return this.value.substring(0,this.startChangePos)}get tail(){return this.value.substring(this.startChangePos+this.insertedCount)}get removeDirection(){return!this.removedCount||this.insertedCount?a.NONE:(this.oldSelection.end===this.cursorPos||this.oldSelection.start===this.cursorPos)&&this.oldSelection.end===this.oldSelection.start?a.RIGHT:a.LEFT}};var f=class{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,s=arguments.length>2?arguments[2]:void 0;this.value=t,this.from=e,this.stop=s}toString(){return this.value}extend(t){this.value+=String(t)}appendTo(t){return t.append(this.toString(),{tail:!0}).aggregate(t._appendPlaceholder())}get state(){return{value:this.value,from:this.from,stop:this.stop}}set state(t){Object.assign(this,t)}unshift(t){if(!this.value.length||t!=null&&this.from>=t)return"";let e=this.value[0];return this.value=this.value.slice(1),e}shift(){if(!this.value.length)return"";let t=this.value[this.value.length-1];return this.value=this.value.slice(0,-1),t}};var m=class{constructor(t){this._value="",this._update(Object.assign({},m.DEFAULTS,t)),this.isInitialized=!0}updateOptions(t){Object.keys(t).length&&this.withValueRefresh(this._update.bind(this,t))}_update(t){Object.assign(this,t)}get state(){return{_value:this.value}}set state(t){this._value=t._value}reset(){this._value=""}get value(){return this._value}set value(t){this.resolve(t)}resolve(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{input:!0};return this.reset(),this.append(t,e,""),this.doCommit(),this.value}get unmaskedValue(){return this.value}set unmaskedValue(t){this.reset(),this.append(t,{},""),this.doCommit()}get typedValue(){return this.doParse(this.value)}set typedValue(t){this.value=this.doFormat(t)}get rawInputValue(){return this.extractInput(0,this.value.length,{raw:!0})}set rawInputValue(t){this.reset(),this.append(t,{raw:!0},""),this.doCommit()}get displayValue(){return this.value}get isComplete(){return!0}get isFilled(){return this.isComplete}nearestInputPos(t,e){return t}totalInputPositions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length;return Math.min(this.value.length,e-t)}extractInput(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length;return this.value.slice(t,e)}extractTail(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length;return new f(this.extractInput(t,e),t)}appendTail(t){return v(t)&&(t=new f(String(t))),t.appendTo(this)}_appendCharRaw(t){return t?(this._value+=t,new p({inserted:t,rawInserted:t})):new p}_appendChar(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,r=this.state,i;if([t,i]=C(this.doPrepare(t,e)),i=i.aggregate(this._appendCharRaw(t,e)),i.inserted){let n,o=this.doValidate(e)!==!1;if(o&&s!=null){let l=this.state;this.overwrite===!0&&(n=s.state,s.unshift(this.value.length-i.tailShift));let d=this.appendTail(s);o=d.rawInserted===s.toString(),!(o&&d.inserted)&&this.overwrite==="shift"&&(this.state=l,n=s.state,s.shift(),d=this.appendTail(s),o=d.rawInserted===s.toString()),o&&d.inserted&&(this.state=l)}o||(i=new p,this.state=r,s&&n&&(s.state=n))}return i}_appendPlaceholder(){return new p}_appendEager(){return new p}append(t,e,s){if(!v(t))throw new Error("value should be string");let r=new p,i=v(s)?new f(String(s)):s;e!=null&&e.tail&&(e._beforeTailState=this.state);for(let n=0;n<t.length;++n){let o=this._appendChar(t[n],e,i);if(!o.rawInserted&&!this.doSkipInvalid(t[n],e,i))break;r.aggregate(o)}return(this.eager===!0||this.eager==="append")&&e!==null&&e!==void 0&&e.input&&t&&r.aggregate(this._appendEager()),i!=null&&(r.tailShift+=this.appendTail(i).tailShift),r}remove(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length;return this._value=this.value.slice(0,t)+this.value.slice(e),new p}withValueRefresh(t){if(this._refreshing||!this.isInitialized)return t();this._refreshing=!0;let e=this.rawInputValue,s=this.value,r=t();return this.rawInputValue=e,this.value&&this.value!==s&&s.indexOf(this.value)===0&&this.append(s.slice(this.value.length),{},""),delete this._refreshing,r}runIsolated(t){if(this._isolated||!this.isInitialized)return t(this);this._isolated=!0;let e=this.state,s=t(this);return this.state=e,delete this._isolated,s}doSkipInvalid(t){return this.skipInvalid}doPrepare(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return this.prepare?this.prepare(t,this,e):t}doValidate(t){return(!this.validate||this.validate(this.value,this,t))&&(!this.parent||this.parent.doValidate(t))}doCommit(){this.commit&&this.commit(this.value,this)}doFormat(t){return this.format?this.format(t,this):t}doParse(t){return this.parse?this.parse(t,this):t}splice(t,e,s,r){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{input:!0},n=t+e,o=this.extractTail(n),l=this.eager===!0||this.eager==="remove",d;l&&(r=$(r),d=this.extractInput(0,n,{raw:!0}));let A=t,x=new p;if(r!==a.NONE&&(A=this.nearestInputPos(t,e>1&&t!==0&&!l?a.NONE:r),x.tailShift=A-t),x.aggregate(this.remove(A)),l&&r!==a.NONE&&d===this.rawInputValue)if(r===a.FORCE_LEFT){let V;for(;d===this.rawInputValue&&(V=this.value.length);)x.aggregate(new p({tailShift:-1})).aggregate(this.remove(V-1))}else r===a.FORCE_RIGHT&&o.unshift();return x.aggregate(this.append(s,i,o))}maskEquals(t){return this.mask===t}typedValueEquals(t){let e=this.typedValue;return t===e||m.EMPTY_VALUES.includes(t)&&m.EMPTY_VALUES.includes(e)||this.doFormat(t)===this.doFormat(this.typedValue)}};m.DEFAULTS={format:String,parse:u=>u,skipInvalid:!0};m.EMPTY_VALUES=[void 0,null,""];h.Masked=m;function G(u){if(u==null)throw new Error("mask property should be defined");return u instanceof RegExp?h.MaskedRegExp:v(u)?h.MaskedPattern:u instanceof Date||u===Date?h.MaskedDate:u instanceof Number||typeof u=="number"||u===Number?h.MaskedNumber:Array.isArray(u)||u===Array?h.MaskedDynamic:h.Masked&&u.prototype instanceof h.Masked?u:u instanceof h.Masked?u.constructor:u instanceof Function?h.MaskedFunction:(console.warn("Mask not found for mask",u),h.Masked)}function k(u){if(h.Masked&&u instanceof h.Masked)return u;u=Object.assign({},u);let t=u.mask;if(h.Masked&&t instanceof h.Masked)return t;let e=G(t);if(!e)throw new Error("Masked class is not found for provided mask, appropriate module needs to be import manually before creating mask.");return new e(u)}h.createMask=k;var X=["parent","isOptional","placeholderChar","displayChar","lazy","eager"],K={0:/\d/,a:/[\u0041-\u005A\u0061-\u007A\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,"*":/./},w=class{constructor(t){let{parent:e,isOptional:s,placeholderChar:r,displayChar:i,lazy:n,eager:o}=t,l=_(t,X);this.masked=k(l),Object.assign(this,{parent:e,isOptional:s,placeholderChar:r,displayChar:i,lazy:n,eager:o})}reset(){this.isFilled=!1,this.masked.reset()}remove(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length;return t===0&&e>=1?(this.isFilled=!1,this.masked.remove(t,e)):new p}get value(){return this.masked.value||(this.isFilled&&!this.isOptional?this.placeholderChar:"")}get unmaskedValue(){return this.masked.unmaskedValue}get displayValue(){return this.masked.value&&this.displayChar||this.value}get isComplete(){return!!this.masked.value||this.isOptional}_appendChar(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.isFilled)return new p;let s=this.masked.state,r=this.masked._appendChar(t,e);return r.inserted&&this.doValidate(e)===!1&&(r.inserted=r.rawInserted="",this.masked.state=s),!r.inserted&&!this.isOptional&&!this.lazy&&!e.input&&(r.inserted=this.placeholderChar),r.skip=!r.inserted&&!this.isOptional,this.isFilled=!!r.inserted,r}append(){return this.masked.append(...arguments)}_appendPlaceholder(){let t=new p;return this.isFilled||this.isOptional||(this.isFilled=!0,t.inserted=this.placeholderChar),t}_appendEager(){return new p}extractTail(){return this.masked.extractTail(...arguments)}appendTail(){return this.masked.appendTail(...arguments)}extractInput(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,s=arguments.length>2?arguments[2]:void 0;return this.masked.extractInput(t,e,s)}nearestInputPos(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:a.NONE,s=0,r=this.value.length,i=Math.min(Math.max(t,s),r);switch(e){case a.LEFT:case a.FORCE_LEFT:return this.isComplete?i:s;case a.RIGHT:case a.FORCE_RIGHT:return this.isComplete?i:r;case a.NONE:default:return i}}totalInputPositions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length;return this.value.slice(t,e).length}doValidate(){return this.masked.doValidate(...arguments)&&(!this.parent||this.parent.doValidate(...arguments))}doCommit(){this.masked.doCommit()}get state(){return{masked:this.masked.state,isFilled:this.isFilled}}set state(t){this.masked.state=t.masked,this.isFilled=t.isFilled}};var R=class{constructor(t){Object.assign(this,t),this._value="",this.isFixed=!0}get value(){return this._value}get unmaskedValue(){return this.isUnmasking?this.value:""}get displayValue(){return this.value}reset(){this._isRawInput=!1,this._value=""}remove(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._value.length;return this._value=this._value.slice(0,t)+this._value.slice(e),this._value||(this._isRawInput=!1),new p}nearestInputPos(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:a.NONE,s=0,r=this._value.length;switch(e){case a.LEFT:case a.FORCE_LEFT:return s;case a.NONE:case a.RIGHT:case a.FORCE_RIGHT:default:return r}}totalInputPositions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._value.length;return this._isRawInput?e-t:0}extractInput(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this._value.length;return(arguments.length>2&&arguments[2]!==void 0?arguments[2]:{}).raw&&this._isRawInput&&this._value.slice(t,e)||""}get isComplete(){return!0}get isFilled(){return!!this._value}_appendChar(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=new p;if(this.isFilled)return s;let r=this.eager===!0||this.eager==="append",n=this.char===t&&(this.isUnmasking||e.input||e.raw)&&(!e.raw||!r)&&!e.tail;return n&&(s.rawInserted=this.char),this._value=s.inserted=this.char,this._isRawInput=n&&(e.raw||e.input),s}_appendEager(){return this._appendChar(this.char,{tail:!0})}_appendPlaceholder(){let t=new p;return this.isFilled||(this._value=t.inserted=this.char),t}extractTail(){return arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,new f("")}appendTail(t){return v(t)&&(t=new f(String(t))),t.appendTo(this)}append(t,e,s){let r=this._appendChar(t[0],e);return s!=null&&(r.tailShift+=this.appendTail(s).tailShift),r}doCommit(){}get state(){return{_value:this._value,_isRawInput:this._isRawInput}}set state(t){Object.assign(this,t)}};var J=["chunks"],F=class{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;this.chunks=t,this.from=e}toString(){return this.chunks.map(String).join("")}extend(t){if(!String(t))return;v(t)&&(t=new f(String(t)));let e=this.chunks[this.chunks.length-1],s=e&&(e.stop===t.stop||t.stop==null)&&t.from===e.from+e.toString().length;if(t instanceof f)s?e.extend(t.toString()):this.chunks.push(t);else if(t instanceof F){if(t.stop==null){let r;for(;t.chunks.length&&t.chunks[0].stop==null;)r=t.chunks.shift(),r.from+=t.from,this.extend(r)}t.toString()&&(t.stop=t.blockIndex,this.chunks.push(t))}}appendTo(t){if(!(t instanceof h.MaskedPattern))return new f(this.toString()).appendTo(t);let e=new p;for(let s=0;s<this.chunks.length&&!e.skip;++s){let r=this.chunks[s],i=t._mapPosToBlock(t.value.length),n=r.stop,o;if(n!=null&&(!i||i.index<=n)){if(r instanceof F||t._stops.indexOf(n)>=0){let l=t._appendPlaceholder(n);e.aggregate(l)}o=r instanceof F&&t._blocks[n]}if(o){let l=o.appendTail(r);l.skip=!1,e.aggregate(l),t._value+=l.inserted;let d=r.toString().slice(l.rawInserted.length);d&&e.aggregate(t.append(d,{tail:!0}))}else e.aggregate(t.append(r.toString(),{tail:!0}))}return e}get state(){return{chunks:this.chunks.map(t=>t.state),from:this.from,stop:this.stop,blockIndex:this.blockIndex}}set state(t){let{chunks:e}=t,s=_(t,J);Object.assign(this,s),this.chunks=e.map(r=>{let i="chunks"in r?new F:new f;return i.state=r,i})}unshift(t){if(!this.chunks.length||t!=null&&this.from>=t)return"";let e=t!=null?t-this.from:t,s=0;for(;s<this.chunks.length;){let r=this.chunks[s],i=r.unshift(e);if(r.toString()){if(!i)break;++s}else this.chunks.splice(s,1);if(i)return i}return""}shift(){if(!this.chunks.length)return"";let t=this.chunks.length-1;for(;0<=t;){let e=this.chunks[t],s=e.shift();if(e.toString()){if(!s)break;--t}else this.chunks.splice(t,1);if(s)return s}return""}};var L=class{constructor(t,e){this.masked=t,this._log=[];let{offset:s,index:r}=t._mapPosToBlock(e)||(e<0?{index:0,offset:0}:{index:this.masked._blocks.length,offset:0});this.offset=s,this.index=r,this.ok=!1}get block(){return this.masked._blocks[this.index]}get pos(){return this.masked._blockStartPos(this.index)+this.offset}get state(){return{index:this.index,offset:this.offset,ok:this.ok}}set state(t){Object.assign(this,t)}pushState(){this._log.push(this.state)}popState(){let t=this._log.pop();return this.state=t,t}bindBlock(){this.block||(this.index<0&&(this.index=0,this.offset=0),this.index>=this.masked._blocks.length&&(this.index=this.masked._blocks.length-1,this.offset=this.block.value.length))}_pushLeft(t){for(this.pushState(),this.bindBlock();0<=this.index;--this.index,this.offset=((e=this.block)===null||e===void 0?void 0:e.value.length)||0){var e;if(t())return this.ok=!0}return this.ok=!1}_pushRight(t){for(this.pushState(),this.bindBlock();this.index<this.masked._blocks.length;++this.index,this.offset=0)if(t())return this.ok=!0;return this.ok=!1}pushLeftBeforeFilled(){return this._pushLeft(()=>{if(!(this.block.isFixed||!this.block.value)&&(this.offset=this.block.nearestInputPos(this.offset,a.FORCE_LEFT),this.offset!==0))return!0})}pushLeftBeforeInput(){return this._pushLeft(()=>{if(!this.block.isFixed)return this.offset=this.block.nearestInputPos(this.offset,a.LEFT),!0})}pushLeftBeforeRequired(){return this._pushLeft(()=>{if(!(this.block.isFixed||this.block.isOptional&&!this.block.value))return this.offset=this.block.nearestInputPos(this.offset,a.LEFT),!0})}pushRightBeforeFilled(){return this._pushRight(()=>{if(!(this.block.isFixed||!this.block.value)&&(this.offset=this.block.nearestInputPos(this.offset,a.FORCE_RIGHT),this.offset!==this.block.value.length))return!0})}pushRightBeforeInput(){return this._pushRight(()=>{if(!this.block.isFixed)return this.offset=this.block.nearestInputPos(this.offset,a.NONE),!0})}pushRightBeforeRequired(){return this._pushRight(()=>{if(!(this.block.isFixed||this.block.isOptional&&!this.block.value))return this.offset=this.block.nearestInputPos(this.offset,a.NONE),!0})}};var N=class extends m{_update(t){t.mask&&(t.validate=e=>e.search(t.mask)>=0),super._update(t)}};h.MaskedRegExp=N;var Q=["_blocks"],c=class extends m{constructor(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};t.definitions=Object.assign({},K,t.definitions),super(Object.assign({},c.DEFAULTS,t))}_update(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};t.definitions=Object.assign({},this.definitions,t.definitions),super._update(t),this._rebuildMask()}_rebuildMask(){let t=this.definitions;this._blocks=[],this._stops=[],this._maskedBlocks={};let e=this.mask;if(!e||!t)return;let s=!1,r=!1;for(let o=0;o<e.length;++o){var i,n;if(this.blocks){let V=e.slice(o),Y=Object.keys(this.blocks).filter(T=>V.indexOf(T)===0);Y.sort((T,W)=>W.length-T.length);let I=Y[0];if(I){let T=k(Object.assign({parent:this,lazy:this.lazy,eager:this.eager,placeholderChar:this.placeholderChar,displayChar:this.displayChar,overwrite:this.overwrite},this.blocks[I]));T&&(this._blocks.push(T),this._maskedBlocks[I]||(this._maskedBlocks[I]=[]),this._maskedBlocks[I].push(this._blocks.length-1)),o+=I.length-1;continue}}let l=e[o],d=l in t;if(l===c.STOP_CHAR){this._stops.push(this._blocks.length);continue}if(l==="{"||l==="}"){s=!s;continue}if(l==="["||l==="]"){r=!r;continue}if(l===c.ESCAPE_CHAR){if(++o,l=e[o],!l)break;d=!1}let A=(i=t[l])!==null&&i!==void 0&&i.mask&&!(((n=t[l])===null||n===void 0?void 0:n.mask.prototype)instanceof h.Masked)?t[l]:{mask:t[l]},x=d?new w(Object.assign({parent:this,isOptional:r,lazy:this.lazy,eager:this.eager,placeholderChar:this.placeholderChar,displayChar:this.displayChar},A)):new R({char:l,eager:this.eager,isUnmasking:s});this._blocks.push(x)}}get state(){return Object.assign({},super.state,{_blocks:this._blocks.map(t=>t.state)})}set state(t){let{_blocks:e}=t,s=_(t,Q);this._blocks.forEach((r,i)=>r.state=e[i]),super.state=s}reset(){super.reset(),this._blocks.forEach(t=>t.reset())}get isComplete(){return this._blocks.every(t=>t.isComplete)}get isFilled(){return this._blocks.every(t=>t.isFilled)}get isFixed(){return this._blocks.every(t=>t.isFixed)}get isOptional(){return this._blocks.every(t=>t.isOptional)}doCommit(){this._blocks.forEach(t=>t.doCommit()),super.doCommit()}get unmaskedValue(){return this._blocks.reduce((t,e)=>t+=e.unmaskedValue,"")}set unmaskedValue(t){super.unmaskedValue=t}get value(){return this._blocks.reduce((t,e)=>t+=e.value,"")}set value(t){super.value=t}get displayValue(){return this._blocks.reduce((t,e)=>t+=e.displayValue,"")}appendTail(t){return super.appendTail(t).aggregate(this._appendPlaceholder())}_appendEager(){var t;let e=new p,s=(t=this._mapPosToBlock(this.value.length))===null||t===void 0?void 0:t.index;if(s==null)return e;this._blocks[s].isFilled&&++s;for(let r=s;r<this._blocks.length;++r){let i=this._blocks[r]._appendEager();if(!i.inserted)break;e.aggregate(i)}return e}_appendCharRaw(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=this._mapPosToBlock(this.value.length),r=new p;if(!s)return r;for(let o=s.index;;++o){var i,n;let l=this._blocks[o];if(!l)break;let d=l._appendChar(t,Object.assign({},e,{_beforeTailState:(i=e._beforeTailState)===null||i===void 0||(n=i._blocks)===null||n===void 0?void 0:n[o]})),A=d.skip;if(r.aggregate(d),A||d.rawInserted)break}return r}extractTail(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,s=new F;return t===e||this._forEachBlocksInRange(t,e,(r,i,n,o)=>{let l=r.extractTail(n,o);l.stop=this._findStopBefore(i),l.from=this._blockStartPos(i),l instanceof F&&(l.blockIndex=i),s.extend(l)}),s}extractInput(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(t===e)return"";let r="";return this._forEachBlocksInRange(t,e,(i,n,o,l)=>{r+=i.extractInput(o,l,s)}),r}_findStopBefore(t){let e;for(let s=0;s<this._stops.length;++s){let r=this._stops[s];if(r<=t)e=r;else break}return e}_appendPlaceholder(t){let e=new p;if(this.lazy&&t==null)return e;let s=this._mapPosToBlock(this.value.length);if(!s)return e;let r=s.index,i=t??this._blocks.length;return this._blocks.slice(r,i).forEach(n=>{if(!n.lazy||t!=null){let o=n._blocks!=null?[n._blocks.length]:[],l=n._appendPlaceholder(...o);this._value+=l.inserted,e.aggregate(l)}}),e}_mapPosToBlock(t){let e="";for(let s=0;s<this._blocks.length;++s){let r=this._blocks[s],i=e.length;if(e+=r.value,t<=e.length)return{index:s,offset:t-i}}}_blockStartPos(t){return this._blocks.slice(0,t).reduce((e,s)=>e+=s.value.length,0)}_forEachBlocksInRange(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,s=arguments.length>2?arguments[2]:void 0,r=this._mapPosToBlock(t);if(r){let i=this._mapPosToBlock(e),n=i&&r.index===i.index,o=r.offset,l=i&&n?i.offset:this._blocks[r.index].value.length;if(s(this._blocks[r.index],r.index,o,l),i&&!n){for(let d=r.index+1;d<i.index;++d)s(this._blocks[d],d,0,this._blocks[d].value.length);s(this._blocks[i.index],i.index,0,i.offset)}}}remove(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,s=super.remove(t,e);return this._forEachBlocksInRange(t,e,(r,i,n,o)=>{s.aggregate(r.remove(n,o))}),s}nearestInputPos(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:a.NONE;if(!this._blocks.length)return 0;let s=new L(this,t);if(e===a.NONE)return s.pushRightBeforeInput()||(s.popState(),s.pushLeftBeforeInput())?s.pos:this.value.length;if(e===a.LEFT||e===a.FORCE_LEFT){if(e===a.LEFT){if(s.pushRightBeforeFilled(),s.ok&&s.pos===t)return t;s.popState()}if(s.pushLeftBeforeInput(),s.pushLeftBeforeRequired(),s.pushLeftBeforeFilled(),e===a.LEFT){if(s.pushRightBeforeInput(),s.pushRightBeforeRequired(),s.ok&&s.pos<=t||(s.popState(),s.ok&&s.pos<=t))return s.pos;s.popState()}return s.ok?s.pos:e===a.FORCE_LEFT?0:(s.popState(),s.ok||(s.popState(),s.ok)?s.pos:0)}return e===a.RIGHT||e===a.FORCE_RIGHT?(s.pushRightBeforeInput(),s.pushRightBeforeRequired(),s.pushRightBeforeFilled()?s.pos:e===a.FORCE_RIGHT?this.value.length:(s.popState(),s.ok||(s.popState(),s.ok)?s.pos:this.nearestInputPos(t,a.LEFT))):t}totalInputPositions(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,s=0;return this._forEachBlocksInRange(t,e,(r,i,n,o)=>{s+=r.totalInputPositions(n,o)}),s}maskedBlock(t){return this.maskedBlocks(t)[0]}maskedBlocks(t){let e=this._maskedBlocks[t];return e?e.map(s=>this._blocks[s]):[]}};c.DEFAULTS={lazy:!0,placeholderChar:"_"};c.STOP_CHAR="`";c.ESCAPE_CHAR="\\";c.InputDefinition=w;c.FixedDefinition=R;h.MaskedPattern=c;var D=class extends c{get _matchFrom(){return this.maxLength-String(this.from).length}_update(t){t=Object.assign({to:this.to||0,from:this.from||0,maxLength:this.maxLength||0},t);let e=String(t.to).length;t.maxLength!=null&&(e=Math.max(e,t.maxLength)),t.maxLength=e;let s=String(t.from).padStart(e,"0"),r=String(t.to).padStart(e,"0"),i=0;for(;i<r.length&&r[i]===s[i];)++i;t.mask=r.slice(0,i).replace(/0/g,"\\0")+"0".repeat(e-i),super._update(t)}get isComplete(){return super.isComplete&&!!this.value}boundaries(t){let e="",s="",[,r,i]=t.match(/^(\D*)(\d*)(\D*)/)||[];return i&&(e="0".repeat(r.length)+i,s="9".repeat(r.length)+i),e=e.padEnd(this.maxLength,"0"),s=s.padEnd(this.maxLength,"9"),[e,s]}doPrepare(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s;if([t,s]=C(super.doPrepare(t.replace(/\D/g,""),e)),!this.autofix||!t)return t;let r=String(this.from).padStart(this.maxLength,"0"),i=String(this.to).padStart(this.maxLength,"0"),n=this.value+t;if(n.length>this.maxLength)return"";let[o,l]=this.boundaries(n);return Number(l)<this.from?r[n.length-1]:Number(o)>this.to?this.autofix==="pad"&&n.length<this.maxLength?["",s.aggregate(this.append(r[n.length-1]+t,e))]:i[n.length-1]:t}doValidate(){let t=this.value;if(t.search(/[^0]/)===-1&&t.length<=this._matchFrom)return!0;let[s,r]=this.boundaries(t);return this.from<=Number(r)&&Number(s)<=this.to&&super.doValidate(...arguments)}};h.MaskedRange=D;var S=class extends c{constructor(t){super(Object.assign({},S.DEFAULTS,t))}_update(t){t.mask===Date&&delete t.mask,t.pattern&&(t.mask=t.pattern);let e=t.blocks;t.blocks=Object.assign({},S.GET_DEFAULT_BLOCKS()),t.min&&(t.blocks.Y.from=t.min.getFullYear()),t.max&&(t.blocks.Y.to=t.max.getFullYear()),t.min&&t.max&&t.blocks.Y.from===t.blocks.Y.to&&(t.blocks.m.from=t.min.getMonth()+1,t.blocks.m.to=t.max.getMonth()+1,t.blocks.m.from===t.blocks.m.to&&(t.blocks.d.from=t.min.getDate(),t.blocks.d.to=t.max.getDate())),Object.assign(t.blocks,this.blocks,e),Object.keys(t.blocks).forEach(s=>{let r=t.blocks[s];!("autofix"in r)&&"autofix"in t&&(r.autofix=t.autofix)}),super._update(t)}doValidate(){let t=this.date;return super.doValidate(...arguments)&&(!this.isComplete||this.isDateExist(this.value)&&t!=null&&(this.min==null||this.min<=t)&&(this.max==null||t<=this.max))}isDateExist(t){return this.format(this.parse(t,this),this).indexOf(t)>=0}get date(){return this.typedValue}set date(t){this.typedValue=t}get typedValue(){return this.isComplete?super.typedValue:null}set typedValue(t){super.typedValue=t}maskEquals(t){return t===Date||super.maskEquals(t)}};S.DEFAULTS={pattern:"d{.}`m{.}`Y",format:u=>{if(!u)return"";let t=String(u.getDate()).padStart(2,"0"),e=String(u.getMonth()+1).padStart(2,"0"),s=u.getFullYear();return[t,e,s].join(".")},parse:u=>{let[t,e,s]=u.split(".");return new Date(s,e-1,t)}};S.GET_DEFAULT_BLOCKS=()=>({d:{mask:D,from:1,to:31,maxLength:2},m:{mask:D,from:1,to:12,maxLength:2},Y:{mask:D,from:1900,to:9999}});h.MaskedDate=S;var B=class{get selectionStart(){let t;try{t=this._unsafeSelectionStart}catch{}return t??this.value.length}get selectionEnd(){let t;try{t=this._unsafeSelectionEnd}catch{}return t??this.value.length}select(t,e){if(!(t==null||e==null||t===this.selectionStart&&e===this.selectionEnd))try{this._unsafeSelect(t,e)}catch{}}_unsafeSelect(t,e){}get isActive(){return!1}bindEvents(t){}unbindEvents(){}};h.MaskElement=B;var E=class extends B{constructor(t){super(),this.input=t,this._handlers={}}get rootElement(){var t,e,s;return(t=(e=(s=this.input).getRootNode)===null||e===void 0?void 0:e.call(s))!==null&&t!==void 0?t:document}get isActive(){return this.input===this.rootElement.activeElement}get _unsafeSelectionStart(){return this.input.selectionStart}get _unsafeSelectionEnd(){return this.input.selectionEnd}_unsafeSelect(t,e){this.input.setSelectionRange(t,e)}get value(){return this.input.value}set value(t){this.input.value=t}bindEvents(t){Object.keys(t).forEach(e=>this._toggleEventHandler(E.EVENTS_MAP[e],t[e]))}unbindEvents(){Object.keys(this._handlers).forEach(t=>this._toggleEventHandler(t))}_toggleEventHandler(t,e){this._handlers[t]&&(this.input.removeEventListener(t,this._handlers[t]),delete this._handlers[t]),e&&(this.input.addEventListener(t,e),this._handlers[t]=e)}};E.EVENTS_MAP={selectionChange:"keydown",input:"input",drop:"drop",click:"click",focus:"focus",commit:"blur"};h.HTMLMaskElement=E;var P=class extends E{get _unsafeSelectionStart(){let t=this.rootElement,e=t.getSelection&&t.getSelection(),s=e&&e.anchorOffset,r=e&&e.focusOffset;return r==null||s==null||s<r?s:r}get _unsafeSelectionEnd(){let t=this.rootElement,e=t.getSelection&&t.getSelection(),s=e&&e.anchorOffset,r=e&&e.focusOffset;return r==null||s==null||s>r?s:r}_unsafeSelect(t,e){if(!this.rootElement.createRange)return;let s=this.rootElement.createRange();s.setStart(this.input.firstChild||this.input,t),s.setEnd(this.input.lastChild||this.input,e);let r=this.rootElement,i=r.getSelection&&r.getSelection();i&&(i.removeAllRanges(),i.addRange(s))}get value(){return this.input.textContent}set value(t){this.input.textContent=t}};h.HTMLContenteditableMaskElement=P;var tt=["mask"],j=class{constructor(t,e){this.el=t instanceof B?t:t.isContentEditable&&t.tagName!=="INPUT"&&t.tagName!=="TEXTAREA"?new P(t):new E(t),this.masked=k(e),this._listeners={},this._value="",this._unmaskedValue="",this._saveSelection=this._saveSelection.bind(this),this._onInput=this._onInput.bind(this),this._onChange=this._onChange.bind(this),this._onDrop=this._onDrop.bind(this),this._onFocus=this._onFocus.bind(this),this._onClick=this._onClick.bind(this),this.alignCursor=this.alignCursor.bind(this),this.alignCursorFriendly=this.alignCursorFriendly.bind(this),this._bindEvents(),this.updateValue(),this._onChange()}get mask(){return this.masked.mask}maskEquals(t){var e;return t==null||((e=this.masked)===null||e===void 0?void 0:e.maskEquals(t))}set mask(t){if(this.maskEquals(t))return;if(!(t instanceof h.Masked)&&this.masked.constructor===G(t)){this.masked.updateOptions({mask:t});return}let e=k({mask:t});e.unmaskedValue=this.masked.unmaskedValue,this.masked=e}get value(){return this._value}set value(t){this.value!==t&&(this.masked.value=t,this.updateControl(),this.alignCursor())}get unmaskedValue(){return this._unmaskedValue}set unmaskedValue(t){this.unmaskedValue!==t&&(this.masked.unmaskedValue=t,this.updateControl(),this.alignCursor())}get typedValue(){return this.masked.typedValue}set typedValue(t){this.masked.typedValueEquals(t)||(this.masked.typedValue=t,this.updateControl(),this.alignCursor())}get displayValue(){return this.masked.displayValue}_bindEvents(){this.el.bindEvents({selectionChange:this._saveSelection,input:this._onInput,drop:this._onDrop,click:this._onClick,focus:this._onFocus,commit:this._onChange})}_unbindEvents(){this.el&&this.el.unbindEvents()}_fireEvent(t){for(var e=arguments.length,s=new Array(e>1?e-1:0),r=1;r<e;r++)s[r-1]=arguments[r];let i=this._listeners[t];i&&i.forEach(n=>n(...s))}get selectionStart(){return this._cursorChanging?this._changingCursorPos:this.el.selectionStart}get cursorPos(){return this._cursorChanging?this._changingCursorPos:this.el.selectionEnd}set cursorPos(t){!this.el||!this.el.isActive||(this.el.select(t,t),this._saveSelection())}_saveSelection(){this.displayValue!==this.el.value&&console.warn("Element value was changed outside of mask. Syncronize mask using `mask.updateValue()` to work properly."),this._selection={start:this.selectionStart,end:this.cursorPos}}updateValue(){this.masked.value=this.el.value,this._value=this.masked.value}updateControl(){let t=this.masked.unmaskedValue,e=this.masked.value,s=this.displayValue,r=this.unmaskedValue!==t||this.value!==e;this._unmaskedValue=t,this._value=e,this.el.value!==s&&(this.el.value=s),r&&this._fireChangeEvents()}updateOptions(t){let{mask:e}=t,s=_(t,tt),r=!this.maskEquals(e),i=!M(this.masked,s);r&&(this.mask=e),i&&this.masked.updateOptions(s),(r||i)&&this.updateControl()}updateCursor(t){t!=null&&(this.cursorPos=t,this._delayUpdateCursor(t))}_delayUpdateCursor(t){this._abortUpdateCursor(),this._changingCursorPos=t,this._cursorChanging=setTimeout(()=>{this.el&&(this.cursorPos=this._changingCursorPos,this._abortUpdateCursor())},10)}_fireChangeEvents(){this._fireEvent("accept",this._inputEvent),this.masked.isComplete&&this._fireEvent("complete",this._inputEvent)}_abortUpdateCursor(){this._cursorChanging&&(clearTimeout(this._cursorChanging),delete this._cursorChanging)}alignCursor(){this.cursorPos=this.masked.nearestInputPos(this.masked.nearestInputPos(this.cursorPos,a.LEFT))}alignCursorFriendly(){this.selectionStart===this.cursorPos&&this.alignCursor()}on(t,e){return this._listeners[t]||(this._listeners[t]=[]),this._listeners[t].push(e),this}off(t,e){if(!this._listeners[t])return this;if(!e)return delete this._listeners[t],this;let s=this._listeners[t].indexOf(e);return s>=0&&this._listeners[t].splice(s,1),this}_onInput(t){if(this._inputEvent=t,this._abortUpdateCursor(),!this._selection)return this.updateValue();let e=new y(this.el.value,this.cursorPos,this.displayValue,this._selection),s=this.masked.rawInputValue,r=this.masked.splice(e.startChangePos,e.removed.length,e.inserted,e.removeDirection,{input:!0,raw:!0}).offset,i=s===this.masked.rawInputValue?e.removeDirection:a.NONE,n=this.masked.nearestInputPos(e.startChangePos+r,i);i!==a.NONE&&(n=this.masked.nearestInputPos(n,a.NONE)),this.updateControl(),this.updateCursor(n),delete this._inputEvent}_onChange(){this.displayValue!==this.el.value&&this.updateValue(),this.masked.doCommit(),this.updateControl(),this._saveSelection()}_onDrop(t){t.preventDefault(),t.stopPropagation()}_onFocus(t){this.alignCursorFriendly()}_onClick(t){this.alignCursorFriendly()}destroy(){this._unbindEvents(),this._listeners.length=0,delete this.el}};h.InputMask=j;var U=class extends c{_update(t){t.enum&&(t.mask="*".repeat(t.enum[0].length)),super._update(t)}doValidate(){return this.enum.some(t=>t.indexOf(this.unmaskedValue)>=0)&&super.doValidate(...arguments)}};h.MaskedEnum=U;var g=class extends m{constructor(t){super(Object.assign({},g.DEFAULTS,t))}_update(t){super._update(t),this._updateRegExps()}_updateRegExps(){let t="^"+(this.allowNegative?"[+|\\-]?":""),e="\\d*",s=(this.scale?"(".concat(O(this.radix),"\\d{0,").concat(this.scale,"})?"):"")+"$";this._numberRegExp=new RegExp(t+e+s),this._mapToRadixRegExp=new RegExp("[".concat(this.mapToRadix.map(O).join(""),"]"),"g"),this._thousandsSeparatorRegExp=new RegExp(O(this.thousandsSeparator),"g")}_removeThousandsSeparators(t){return t.replace(this._thousandsSeparatorRegExp,"")}_insertThousandsSeparators(t){let e=t.split(this.radix);return e[0]=e[0].replace(/\B(?=(\d{3})+(?!\d))/g,this.thousandsSeparator),e.join(this.radix)}doPrepare(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};t=this._removeThousandsSeparators(this.scale&&this.mapToRadix.length&&(e.input&&e.raw||!e.input&&!e.raw)?t.replace(this._mapToRadixRegExp,this.radix):t);let[s,r]=C(super.doPrepare(t,e));return t&&!s&&(r.skip=!0),[s,r]}_separatorsCount(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=0;for(let r=0;r<t;++r)this._value.indexOf(this.thousandsSeparator,r)===r&&(++s,e&&(t+=this.thousandsSeparator.length));return s}_separatorsCountFromSlice(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this._value;return this._separatorsCount(this._removeThousandsSeparators(t).length,!0)}extractInput(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length,s=arguments.length>2?arguments[2]:void 0;return[t,e]=this._adjustRangeWithSeparators(t,e),this._removeThousandsSeparators(super.extractInput(t,e,s))}_appendCharRaw(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.thousandsSeparator)return super._appendCharRaw(t,e);let s=e.tail&&e._beforeTailState?e._beforeTailState._value:this._value,r=this._separatorsCountFromSlice(s);this._value=this._removeThousandsSeparators(this.value);let i=super._appendCharRaw(t,e);this._value=this._insertThousandsSeparators(this._value);let n=e.tail&&e._beforeTailState?e._beforeTailState._value:this._value,o=this._separatorsCountFromSlice(n);return i.tailShift+=(o-r)*this.thousandsSeparator.length,i.skip=!i.rawInserted&&t===this.thousandsSeparator,i}_findSeparatorAround(t){if(this.thousandsSeparator){let e=t-this.thousandsSeparator.length+1,s=this.value.indexOf(this.thousandsSeparator,e);if(s<=t)return s}return-1}_adjustRangeWithSeparators(t,e){let s=this._findSeparatorAround(t);s>=0&&(t=s);let r=this._findSeparatorAround(e);return r>=0&&(e=r+this.thousandsSeparator.length),[t,e]}remove(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.value.length;[t,e]=this._adjustRangeWithSeparators(t,e);let s=this.value.slice(0,t),r=this.value.slice(e),i=this._separatorsCount(s.length);this._value=this._insertThousandsSeparators(this._removeThousandsSeparators(s+r));let n=this._separatorsCountFromSlice(s);return new p({tailShift:(n-i)*this.thousandsSeparator.length})}nearestInputPos(t,e){if(!this.thousandsSeparator)return t;switch(e){case a.NONE:case a.LEFT:case a.FORCE_LEFT:{let s=this._findSeparatorAround(t-1);if(s>=0){let r=s+this.thousandsSeparator.length;if(t<r||this.value.length<=r||e===a.FORCE_LEFT)return s}break}case a.RIGHT:case a.FORCE_RIGHT:{let s=this._findSeparatorAround(t);if(s>=0)return s+this.thousandsSeparator.length}}return t}doValidate(t){let e=!!this._removeThousandsSeparators(this.value).match(this._numberRegExp);if(e){let s=this.number;e=e&&!isNaN(s)&&(this.min==null||this.min>=0||this.min<=this.number)&&(this.max==null||this.max<=0||this.number<=this.max)}return e&&super.doValidate(t)}doCommit(){if(this.value){let t=this.number,e=t;this.min!=null&&(e=Math.max(e,this.min)),this.max!=null&&(e=Math.min(e,this.max)),e!==t&&(this.unmaskedValue=this.doFormat(e));let s=this.value;this.normalizeZeros&&(s=this._normalizeZeros(s)),this.padFractionalZeros&&this.scale>0&&(s=this._padFractionalZeros(s)),this._value=s}super.doCommit()}_normalizeZeros(t){let e=this._removeThousandsSeparators(t).split(this.radix);return e[0]=e[0].replace(/^(\D*)(0*)(\d*)/,(s,r,i,n)=>r+n),t.length&&!/\d$/.test(e[0])&&(e[0]=e[0]+"0"),e.length>1&&(e[1]=e[1].replace(/0*$/,""),e[1].length||(e.length=1)),this._insertThousandsSeparators(e.join(this.radix))}_padFractionalZeros(t){if(!t)return t;let e=t.split(this.radix);return e.length<2&&e.push(""),e[1]=e[1].padEnd(this.scale,"0"),e.join(this.radix)}doSkipInvalid(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,r=this.scale===0&&t!==this.thousandsSeparator&&(t===this.radix||t===g.UNMASKED_RADIX||this.mapToRadix.includes(t));return super.doSkipInvalid(t,e,s)&&!r}get unmaskedValue(){return this._removeThousandsSeparators(this._normalizeZeros(this.value)).replace(this.radix,g.UNMASKED_RADIX)}set unmaskedValue(t){super.unmaskedValue=t}get typedValue(){return this.doParse(this.unmaskedValue)}set typedValue(t){this.rawInputValue=this.doFormat(t).replace(g.UNMASKED_RADIX,this.radix)}get number(){return this.typedValue}set number(t){this.typedValue=t}get allowNegative(){return this.signed||this.min!=null&&this.min<0||this.max!=null&&this.max<0}typedValueEquals(t){return(super.typedValueEquals(t)||g.EMPTY_VALUES.includes(t)&&g.EMPTY_VALUES.includes(this.typedValue))&&!(t===0&&this.value==="")}};g.UNMASKED_RADIX=".";g.DEFAULTS={radix:",",thousandsSeparator:"",mapToRadix:[g.UNMASKED_RADIX],scale:2,signed:!1,normalizeZeros:!0,padFractionalZeros:!1,parse:Number,format:u=>u.toLocaleString("en-US",{useGrouping:!1,maximumFractionDigits:20})};g.EMPTY_VALUES=[...m.EMPTY_VALUES,0];h.MaskedNumber=g;var H=class extends m{_update(t){t.mask&&(t.validate=t.mask),super._update(t)}};h.MaskedFunction=H;var et=["compiledMasks","currentMaskRef","currentMask"],st=["mask"],b=class extends m{constructor(t){super(Object.assign({},b.DEFAULTS,t)),this.currentMask=null}_update(t){super._update(t),"mask"in t&&(this.compiledMasks=Array.isArray(t.mask)?t.mask.map(e=>k(e)):[])}_appendCharRaw(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=this._applyDispatch(t,e);return this.currentMask&&s.aggregate(this.currentMask._appendChar(t,this.currentMaskFlags(e))),s}_applyDispatch(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"",r=e.tail&&e._beforeTailState!=null?e._beforeTailState._value:this.value,i=this.rawInputValue,n=e.tail&&e._beforeTailState!=null?e._beforeTailState._rawInputValue:i,o=i.slice(n.length),l=this.currentMask,d=new p,A=l?.state;if(this.currentMask=this.doDispatch(t,Object.assign({},e),s),this.currentMask)if(this.currentMask!==l){if(this.currentMask.reset(),n){let x=this.currentMask.append(n,{raw:!0});d.tailShift=x.inserted.length-r.length}o&&(d.tailShift+=this.currentMask.append(o,{raw:!0,tail:!0}).tailShift)}else this.currentMask.state=A;return d}_appendPlaceholder(){let t=this._applyDispatch(...arguments);return this.currentMask&&t.aggregate(this.currentMask._appendPlaceholder()),t}_appendEager(){let t=this._applyDispatch(...arguments);return this.currentMask&&t.aggregate(this.currentMask._appendEager()),t}appendTail(t){let e=new p;return t&&e.aggregate(this._applyDispatch("",{},t)),e.aggregate(this.currentMask?this.currentMask.appendTail(t):super.appendTail(t))}currentMaskFlags(t){var e,s;return Object.assign({},t,{_beforeTailState:((e=t._beforeTailState)===null||e===void 0?void 0:e.currentMaskRef)===this.currentMask&&((s=t._beforeTailState)===null||s===void 0?void 0:s.currentMask)||t._beforeTailState})}doDispatch(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"";return this.dispatch(t,this,e,s)}doValidate(t){return super.doValidate(t)&&(!this.currentMask||this.currentMask.doValidate(this.currentMaskFlags(t)))}doPrepare(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},[s,r]=C(super.doPrepare(t,e));if(this.currentMask){let i;[s,i]=C(super.doPrepare(s,this.currentMaskFlags(e))),r=r.aggregate(i)}return[s,r]}reset(){var t;(t=this.currentMask)===null||t===void 0||t.reset(),this.compiledMasks.forEach(e=>e.reset())}get value(){return this.currentMask?this.currentMask.value:""}set value(t){super.value=t}get unmaskedValue(){return this.currentMask?this.currentMask.unmaskedValue:""}set unmaskedValue(t){super.unmaskedValue=t}get typedValue(){return this.currentMask?this.currentMask.typedValue:""}set typedValue(t){let e=String(t);this.currentMask&&(this.currentMask.typedValue=t,e=this.currentMask.unmaskedValue),this.unmaskedValue=e}get displayValue(){return this.currentMask?this.currentMask.displayValue:""}get isComplete(){var t;return!!(!((t=this.currentMask)===null||t===void 0)&&t.isComplete)}get isFilled(){var t;return!!(!((t=this.currentMask)===null||t===void 0)&&t.isFilled)}remove(){let t=new p;return this.currentMask&&t.aggregate(this.currentMask.remove(...arguments)).aggregate(this._applyDispatch()),t}get state(){var t;return Object.assign({},super.state,{_rawInputValue:this.rawInputValue,compiledMasks:this.compiledMasks.map(e=>e.state),currentMaskRef:this.currentMask,currentMask:(t=this.currentMask)===null||t===void 0?void 0:t.state})}set state(t){let{compiledMasks:e,currentMaskRef:s,currentMask:r}=t,i=_(t,et);this.compiledMasks.forEach((n,o)=>n.state=e[o]),s!=null&&(this.currentMask=s,this.currentMask.state=r),super.state=i}extractInput(){return this.currentMask?this.currentMask.extractInput(...arguments):""}extractTail(){return this.currentMask?this.currentMask.extractTail(...arguments):super.extractTail(...arguments)}doCommit(){this.currentMask&&this.currentMask.doCommit(),super.doCommit()}nearestInputPos(){return this.currentMask?this.currentMask.nearestInputPos(...arguments):super.nearestInputPos(...arguments)}get overwrite(){return this.currentMask?this.currentMask.overwrite:super.overwrite}set overwrite(t){console.warn('"overwrite" option is not available in dynamic mask, use this option in siblings')}get eager(){return this.currentMask?this.currentMask.eager:super.eager}set eager(t){console.warn('"eager" option is not available in dynamic mask, use this option in siblings')}get skipInvalid(){return this.currentMask?this.currentMask.skipInvalid:super.skipInvalid}set skipInvalid(t){(this.isInitialized||t!==m.DEFAULTS.skipInvalid)&&console.warn('"skipInvalid" option is not available in dynamic mask, use this option in siblings')}maskEquals(t){return Array.isArray(t)&&this.compiledMasks.every((e,s)=>{if(!t[s])return;let r=t[s],{mask:i}=r,n=_(r,st);return M(e,n)&&e.maskEquals(i)})}typedValueEquals(t){var e;return!!(!((e=this.currentMask)===null||e===void 0)&&e.typedValueEquals(t))}};b.DEFAULTS={dispatch:(u,t,e,s)=>{if(!t.compiledMasks.length)return;let r=t.rawInputValue,i=t.compiledMasks.map((n,o)=>{let l=t.currentMask===n,d=l?n.value.length:n.nearestInputPos(n.value.length,a.FORCE_LEFT);return n.rawInputValue!==r?(n.reset(),n.append(r,{raw:!0})):l||n.remove(d),n.append(u,t.currentMaskFlags(e)),n.appendTail(s),{index:o,weight:n.rawInputValue.length,totalInputPositions:n.totalInputPositions(0,Math.max(d,n.nearestInputPos(n.value.length,a.FORCE_LEFT)))}});return i.sort((n,o)=>o.weight-n.weight||o.totalInputPositions-n.totalInputPositions),t.compiledMasks[i[0].index]}};h.MaskedDynamic=b;var z={MASKED:"value",UNMASKED:"unmaskedValue",TYPED:"typedValue"};function q(u){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:z.MASKED,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:z.MASKED,s=k(u);return r=>s.runIsolated(i=>(i[t]=r,i[e]))}function Z(u){for(var t=arguments.length,e=new Array(t>1?t-1:0),s=1;s<t;s++)e[s-1]=arguments[s];return q(...e)(u)}h.PIPE_TYPE=z;h.createPipe=q;h.pipe=Z;try{globalThis.IMask=h}catch{}function rt({getMaskOptionsUsing:u,state:t}){return{isStateBeingUpdated:!1,mask:null,state:t,init:function(){u&&(typeof this.state<"u"&&(this.$el.value=this.state?.valueOf()),this.mask=h(this.$el,u(h)).on("accept",()=>{this.isStateBeingUpdated=!0,this.state=this.mask.unmaskedValue,this.$nextTick(()=>this.isStateBeingUpdated=!1)}),this.$watch("state",()=>{this.isStateBeingUpdated||(this.mask.unmaskedValue=this.state?.valueOf()??"")}))}}}export{rt as default};
