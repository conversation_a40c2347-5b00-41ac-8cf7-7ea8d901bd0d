<?php

namespace App\Models\Shop;

use App\Traits\BelongsToTeam;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * 
 *
 * @property int $id
 * @property int|null $team_id
 * @property int $sort
 * @property int|null $shop_order_id
 * @property int|null $shop_product_id
 * @property int $qty
 * @property string $unit_price
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Team|null $team
 * @method static \Database\Factories\Shop\OrderItemFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem forCurrentTeam()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem forTeam(\App\Models\Team $team)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereShopOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereShopProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereUnitPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class OrderItem extends Model
{
    use HasFactory;
    use BelongsToTeam;

    /**
     * @var string
     */
    protected $table = 'shop_order_items';
}
