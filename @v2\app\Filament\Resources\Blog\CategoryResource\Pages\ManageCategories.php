<?php

namespace App\Filament\Resources\Blog\CategoryResource\Pages;

use App\Filament\Imports\Blog\CategoryImporter;
use App\Filament\Resources\Blog\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageCategories extends ManageRecords
{
    protected static string $resource = CategoryResource::class;

    public function getTitle(): string
    {
        $type = request()->get('type');
        if ($type) {
            $config = config("post-types.types.{$type}", []);
            return ($config['name'] ?? ucfirst($type)) . ' Categories';
        }

        return 'Categories';
    }

    protected function getActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->importer(CategoryImporter::class),
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    // Set type from URL parameter
                    if ($type = request()->get('type')) {
                        $data['type'] = $type;
                    }
                    return $data;
                }),
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\ImportAction::make()
                ->importer(CategoryImporter::class),
            Actions\CreateAction::make()
                ->mutateFormDataUsing(function (array $data): array {
                    // Set type from URL parameter
                    if ($type = request()->get('type')) {
                        $data['type'] = $type;
                    }
                    return $data;
                }),
        ];
    }
}
