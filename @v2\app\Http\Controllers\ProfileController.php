<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Models\UserProfile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class ProfileController extends Controller
{
    /**
     * Display the profile completion page
     */
    public function show(): View
    {
        $user = Auth::user();
        $profile = $user->getOrCreateProfile();
        $role = $user->getPrimaryRole();

        return view('profile.show', compact('user', 'profile', 'role'));
    }

    /**
     * Display the profile edit page
     */
    public function edit(): View
    {
        $user = Auth::user();
        $profile = $user->getOrCreateProfile();
        $role = $user->getPrimaryRole();

        return view('profile.edit', compact('user', 'profile', 'role'));
    }

    /**
     * Update the user's profile
     */
    public function update(Request $request): RedirectResponse
    {
        $user = Auth::user();
        $profile = $user->getOrCreateProfile();
        $role = $user->getPrimaryRole();

        // Handle role change for social/phone users
        if ($request->has('user_role') && $request->user_role !== $role) {
            $request->validate([
                'user_role' => 'required|string|in:student,parent,teacher,school'
            ]);

            // Remove old role and assign new role
            $user->syncRoles([$request->user_role]);
            $role = $request->user_role;
        }

        // Validate based on role
        $rules = $this->getValidationRules($role);
        $validatedData = $request->validate($rules);

        // Handle profile image upload
        if ($request->hasFile('profile_image')) {
            // Delete old image if exists
            if ($profile->profile_image) {
                Storage::disk('public')->delete($profile->profile_image);
            }

            $imagePath = $request->file('profile_image')->store('profile-images', 'public');
            $validatedData['profile_image'] = $imagePath;
        }

        // Handle subjects array for teachers
        if ($role === 'teacher' && $request->has('subjects')) {
            $validatedData['subjects'] = array_filter($request->input('subjects', []));
        }

        // Handle children IDs for parents
        if ($role === 'parent' && $request->has('children_ids')) {
            $validatedData['children_ids'] = array_filter($request->input('children_ids', []));
        }

        // Update profile
        $profile->update($validatedData);

        // Check if profile is now complete
        if ($profile->isProfileComplete() && !$profile->profile_completed) {
            $profile->markAsCompleted();
        }

        return redirect()->route('profile.show')->with('success', 'Profile updated successfully!');
    }

    /**
     * Get validation rules based on user role
     */
    private function getValidationRules(string $role): array
    {
        $baseRules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'bio' => 'nullable|string|max:1000',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'facebook_url' => 'nullable|url|max:255',
            'line_id' => 'nullable|string|max:100',
            'instagram_url' => 'nullable|url|max:255',
        ];

        return match($role) {
            'student' => array_merge($baseRules, [
                'date_of_birth' => 'required|date|before:today',
                'gender' => 'required|in:male,female,other',
                'emergency_contact_name' => 'required|string|max:255',
                'emergency_contact_phone' => 'required|string|max:20',
                'emergency_contact_relationship' => 'required|string|max:100',
                'student_id' => 'nullable|string|max:50|unique:user_profiles,student_id,' . auth()->user()->profile?->id,
                'grade_level' => 'nullable|string|max:50',
                'class_section' => 'nullable|string|max:50',
            ]),

            'parent' => array_merge($baseRules, [
                'date_of_birth' => 'nullable|date|before:today',
                'gender' => 'nullable|in:male,female,other',
                'occupation' => 'required|string|max:255',
                'workplace' => 'nullable|string|max:255',
                'children_ids' => 'nullable|array',
                'children_ids.*' => 'string|max:50',
            ]),

            'teacher' => array_merge($baseRules, [
                'date_of_birth' => 'nullable|date|before:today',
                'gender' => 'nullable|in:male,female,other',
                'employee_id' => 'nullable|string|max:50|unique:user_profiles,employee_id,' . auth()->user()->profile?->id,
                'qualification' => 'required|string|max:255',
                'years_of_experience' => 'required|integer|min:0|max:50',
                'subjects' => 'required|array|min:1',
                'subjects.*' => 'string|max:100',
            ]),

            'school' => array_merge($baseRules, [
                'school_name' => 'required|string|max:255',
                'school_code' => 'required|string|max:50|unique:user_profiles,school_code,' . auth()->user()->profile?->id,
                'school_address' => 'required|string|max:500',
                'principal_name' => 'required|string|max:255',
                'school_phone' => 'nullable|string|max:20',
                'school_email' => 'nullable|email|max:255',
                'total_students' => 'nullable|integer|min:0',
                'total_teachers' => 'nullable|integer|min:0',
                'established_date' => 'nullable|date|before_or_equal:today',
            ]),

            default => $baseRules,
        };
    }

    /**
     * Skip profile completion (for optional completion)
     */
    public function skip(): RedirectResponse
    {
        $user = Auth::user();
        return redirect($user->getDashboardRoute());
    }
}
