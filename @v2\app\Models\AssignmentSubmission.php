<?php

namespace App\Models;

use App\Traits\BelongsToTeam;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class AssignmentSubmission extends Model
{
    use HasFactory, BelongsToTeam;

    protected $fillable = [
        'team_id',
        'assignment_id',
        'user_id', // student
        'score',
        'submitted_at',
        'status',
        'content',
        'file_path',
        'feedback',
        'graded_at',
        'graded_by',
    ];

    protected $casts = [
        'submitted_at' => 'datetime',
        'graded_at' => 'datetime',
        'score' => 'decimal:2',
    ];

    const STATUS_DRAFT = 'draft';
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_GRADED = 'graded';
    const STATUS_RETURNED = 'returned';

    /**
     * Get the team that owns the submission
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the assignment that this submission belongs to
     */
    public function assignment(): BelongsTo
    {
        return $this->belongsTo(Assignment::class);
    }

    /**
     * Get the student (user) who made the submission
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the teacher (user) who graded the submission
     */
    public function grader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'graded_by');
    }

    /**
     * Scope a query to filter by assignment
     */
    public function scopeForAssignment(Builder $query, int $assignmentId): Builder
    {
        return $query->where('assignment_id', $assignmentId);
    }

    /**
     * Scope a query to filter by student
     */
    public function scopeForStudent(Builder $query, int $studentId): Builder
    {
        return $query->where('user_id', $studentId);
    }

    /**
     * Scope a query to filter by status
     */
    public function scopeWithStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to get submitted submissions
     */
    public function scopeSubmitted(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_SUBMITTED);
    }

    /**
     * Scope a query to get graded submissions
     */
    public function scopeGraded(Builder $query): Builder
    {
        return $query->where('status', self::STATUS_GRADED);
    }

    /**
     * Scope a query to get ungraded submissions
     */
    public function scopeUngraded(Builder $query): Builder
    {
        return $query->whereIn('status', [self::STATUS_SUBMITTED])
                    ->whereNull('score');
    }

    /**
     * Scope a query to filter by team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Get the formatted submitted date
     */
    public function getFormattedSubmittedAtAttribute(): string
    {
        return $this->submitted_at ? $this->submitted_at->format('M j, Y g:i A') : 'Not submitted';
    }

    /**
     * Get the formatted graded date
     */
    public function getFormattedGradedAtAttribute(): string
    {
        return $this->graded_at ? $this->graded_at->format('M j, Y g:i A') : 'Not graded';
    }

    /**
     * Check if submission is late
     */
    public function getIsLateAttribute(): bool
    {
        return $this->submitted_at && 
               $this->assignment->due_date && 
               $this->submitted_at->isAfter($this->assignment->due_date);
    }

    /**
     * Get the percentage score
     */
    public function getPercentageScoreAttribute(): ?float
    {
        if (!$this->score || !$this->assignment->total_score) {
            return null;
        }
        
        return ($this->score / $this->assignment->total_score) * 100;
    }

    /**
     * Get the grade letter based on percentage
     */
    public function getGradeLetterAttribute(): ?string
    {
        $percentage = $this->percentage_score;
        
        if ($percentage === null) {
            return null;
        }
        
        if ($percentage >= 90) return 'A';
        if ($percentage >= 80) return 'B';
        if ($percentage >= 70) return 'C';
        if ($percentage >= 60) return 'D';
        return 'F';
    }

    /**
     * Check if submission is graded
     */
    public function getIsGradedAttribute(): bool
    {
        return $this->status === self::STATUS_GRADED && $this->score !== null;
    }

    /**
     * Get status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_DRAFT => 'gray',
            self::STATUS_SUBMITTED => 'warning',
            self::STATUS_GRADED => 'success',
            self::STATUS_RETURNED => 'info',
            default => 'gray'
        };
    }
}
