<?php

namespace App\Filament\Clusters\Products\Resources\DigitalProductResource\Widgets;

use App\Models\Shop\Brand;
use App\Models\Shop\Category;
use App\Models\Shop\Product;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BrandCategoryStats extends BaseWidget
{
    protected function getStats(): array
    {
        $totalBrands = Brand::count();
        $visibleBrands = Brand::where('is_visible', true)->count();
        $totalCategories = Category::count();
        $visibleCategories = Category::where('is_visible', true)->count();
        $parentCategories = Category::whereNull('parent_id')->count();
        $childCategories = Category::whereNotNull('parent_id')->count();
        
        // Digital products without brand or category
        $digitalProductsWithoutBrand = Product::where('product_type', 'digital')
            ->whereNull('shop_brand_id')->count();
        $digitalProductsWithoutCategory = Product::where('product_type', 'digital')
            ->whereDoesntHave('categories')->count();

        return [
            Stat::make('Total Brands', $totalBrands)
                ->description("{$visibleBrands} visible, " . ($totalBrands - $visibleBrands) . " hidden")
                ->descriptionIcon('heroicon-m-tag')
                ->color('primary')
                ->chart([3, 7, 12, 8, 15, 10, $totalBrands])
                ->chartColor('primary'),

            Stat::make('Total Categories', $totalCategories)
                ->description("{$parentCategories} parent, {$childCategories} child")
                ->descriptionIcon('heroicon-m-folder')
                ->color('success')
                ->chart([5, 8, 12, 6, 14, 9, $totalCategories])
                ->chartColor('success'),

            Stat::make('Visible Categories', $visibleCategories)
                ->description('Categories shown to customers')
                ->descriptionIcon('heroicon-m-eye')
                ->color('info'),

            Stat::make('Digital Missing Data', $digitalProductsWithoutBrand + $digitalProductsWithoutCategory)
                ->description("{$digitalProductsWithoutBrand} without brand, {$digitalProductsWithoutCategory} without category")
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color($digitalProductsWithoutBrand + $digitalProductsWithoutCategory > 0 ? 'warning' : 'success'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }
}
