// Downloads Page JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle (inherited from homepage)
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeMenu = document.getElementById('close-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('active');
        });
    }

    if (closeMenu && mobileMenu) {
        closeMenu.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
        });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        if (mobileMenu && mobileMenu.classList.contains('active')) {
            if (!mobileMenu.contains(event.target) && !menuToggle.contains(event.target)) {
                mobileMenu.classList.remove('active');
            }
        }
    });

    // Search functionality
    const searchBtn = document.getElementById('searchBtn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            // Get selected filters
            const mediaTypeFilters = getSelectedFilters('media-type');
            const subjectFilters = getSelectedFilters('subject');
            const gradeFilters = getSelectedFilters('grade');
            
            // Show loading state
            this.innerHTML = '<svg class="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>กำลังค้นหา...';
            this.disabled = true;
            
            // Simulate search (replace with actual search implementation)
            setTimeout(() => {
                this.innerHTML = 'ค้นหา';
                this.disabled = false;
                
                // Filter results based on selected criteria
                filterResults(mediaTypeFilters, subjectFilters, gradeFilters);
            }, 1000);
        });
    }

    // Media type button functionality
    const mediaTypeButtons = document.querySelectorAll('.media-type-btn');
    mediaTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            mediaTypeButtons.forEach(btn => btn.classList.remove('bg-blue-100', 'border-blue-300'));
            
            // Add active class to clicked button
            this.classList.add('bg-blue-100', 'border-blue-300');
            
            // Filter content based on media type
            const mediaType = this.querySelector('span').textContent;
            filterByMediaType(mediaType);
        });
    });

    // Filter buttons in the special materials section
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all filter buttons
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // Add active class to clicked button
            this.classList.add('active');
            
            // Filter special materials
            const filterType = this.textContent.trim();
            filterSpecialMaterials(filterType);
        });
    });

    // Download button functionality
    const downloadButtons = document.querySelectorAll('.download-btn');
    downloadButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const materialName = this.closest('.media-card').querySelector('h3').textContent;
            const originalText = this.textContent;
            
            // Show downloading state
            this.innerHTML = '<svg class="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>กำลังดาวน์โหลด...';
            this.disabled = true;
            
            // Simulate download process
            setTimeout(() => {
                this.innerHTML = '✓ ดาวน์โหลดแล้ว';
                this.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                this.classList.add('bg-green-600');
                
                // Show success message
                showNotification(`ดาวน์โหลด "${materialName}" เรียบร้อยแล้ว`, 'success');
                
                // Reset button after 3 seconds
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                    this.classList.remove('bg-green-600');
                    this.classList.add('bg-blue-600', 'hover:bg-blue-700');
                }, 3000);
            }, 2000);
        });
    });

    // Dropdown functionality
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const button = dropdown.querySelector('button');
        const content = dropdown.querySelector('.dropdown-content');
        
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            
            // Close other dropdowns
            dropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.querySelector('.dropdown-content').style.display = 'none';
                }
            });
            
            // Toggle current dropdown
            content.style.display = content.style.display === 'block' ? 'none' : 'block';
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function() {
        dropdowns.forEach(dropdown => {
            dropdown.querySelector('.dropdown-content').style.display = 'none';
        });
    });

    // Checkbox functionality
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateFilterButton(this);
        });
    });

    // Fade in animation for cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Observe all media cards
    const mediaCards = document.querySelectorAll('.media-card');
    mediaCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        observer.observe(card);
    });
});

// Helper Functions
function getSelectedFilters(filterType) {
    const checkboxes = document.querySelectorAll(`input[data-filter="${filterType}"]:checked`);
    return Array.from(checkboxes).map(cb => cb.value);
}

function filterResults(mediaTypes, subjects, grades) {
    // Implementation for filtering results
    console.log('Filtering with:', { mediaTypes, subjects, grades });
    
    // Show filtered results with animation
    const results = document.querySelectorAll('.media-card, .table-row');
    results.forEach((result, index) => {
        result.style.opacity = '0';
        result.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            result.style.transition = 'all 0.3s ease';
            result.style.opacity = '1';
            result.style.transform = 'translateY(0)';
        }, index * 50);
    });
}

function filterByMediaType(mediaType) {
    console.log('Filtering by media type:', mediaType);
    // Implementation for media type filtering
}

function filterSpecialMaterials(filterType) {
    console.log('Filtering special materials by:', filterType);
    // Implementation for special materials filtering
}

function updateFilterButton(checkbox) {
    const dropdown = checkbox.closest('.dropdown');
    const button = dropdown.querySelector('button span');
    const checkedBoxes = dropdown.querySelectorAll('input[type="checkbox"]:checked');
    
    if (checkedBoxes.length > 0) {
        button.textContent = `เลือกแล้ว (${checkedBoxes.length})`;
        button.classList.add('text-blue-600');
    } else {
        // Reset to original text
        const originalText = dropdown.querySelector('button').getAttribute('data-original-text') || button.textContent;
        button.textContent = originalText;
        button.classList.remove('text-blue-600');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
    
    // Set notification style based on type
    switch (type) {
        case 'success':
            notification.classList.add('bg-green-500', 'text-white');
            break;
        case 'error':
            notification.classList.add('bg-red-500', 'text-white');
            break;
        default:
            notification.classList.add('bg-blue-500', 'text-white');
    }
    
    notification.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}
