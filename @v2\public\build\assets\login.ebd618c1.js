document.addEventListener("DOMContentLoaded",function(){g(),L(),v(),E();function g(){document.getElementById("loginForm").querySelectorAll(".form-input").forEach(t=>{t.addEventListener("blur",r),t.addEventListener("input",a)});function r(t){const o=t.target,l=o.value.trim(),s=o.name;switch(e(o),s){case"email":l?f(l)?d(o):i(o,"\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E2D\u0E35\u0E40\u0E21\u0E25\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"):i(o,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2D\u0E35\u0E40\u0E21\u0E25");break;case"password":l?l.length<6?i(o,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E2D\u0E22\u0E48\u0E32\u0E07\u0E19\u0E49\u0E2D\u0E22 6 \u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23"):d(o):i(o,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19");break}}function a(t){const o=t.target;o.classList.contains("error")&&e(o)}function i(t,o){t.classList.add("error"),t.classList.remove("success");const l=document.createElement("div");l.className="error-message",l.innerHTML=`
                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${o}
            `,t.parentNode.appendChild(l)}function d(t){t.classList.add("success"),t.classList.remove("error")}function e(t){t.classList.remove("error","success");const o=t.parentNode.querySelector(".error-message");o&&o.remove()}function f(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}}function L(){const n=document.querySelectorAll('.social-button:not([id="phoneLogin"])');console.log("Found social buttons:",n.length),n.forEach(r=>{console.log("Button ID:",r.id)}),n.forEach(r=>{const a=r.id.replace("Login","");console.log("Adding event listener for provider:",a),r.addEventListener("click",()=>c(a))});function c(r){console.log("Social login clicked for provider:",r);const a=document.getElementById(r+"Login");w(a,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E40\u0E0A\u0E37\u0E48\u0E2D\u0E21\u0E15\u0E48\u0E2D...");const i=`/auth/${r}`;console.log("Redirecting to:",i),window.location.href=i}}function v(){const n=document.getElementById("phoneLogin"),c=document.getElementById("phoneModal"),r=document.getElementById("closePhoneModal"),a=document.getElementById("sendOtp"),i=document.getElementById("verifyOtp"),d=document.getElementById("phoneNumber"),e=document.querySelectorAll(".otp-input");n&&n.addEventListener("click",function(){c.classList.remove("hidden"),d.focus()}),r&&r.addEventListener("click",function(){c.classList.add("hidden"),o()}),c&&c.addEventListener("click",function(s){s.target===this&&(this.classList.add("hidden"),o())}),a&&a.addEventListener("click",f),i&&i.addEventListener("click",t),e.forEach((s,u)=>{s.addEventListener("input",function(m){m.target.value.length===1&&u<e.length-1&&e[u+1].focus()}),s.addEventListener("keydown",function(m){m.key==="Backspace"&&m.target.value===""&&u>0&&e[u-1].focus()})});async function f(){const s=d.value.trim(),u=document.getElementById("countryCode").value;if(!s){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C","error");return}if(!l(s)){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error");return}w(a,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E48\u0E07 OTP...");try{const h=await(await fetch("/api/auth/phone/send-otp",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({phone:s,country_code:u})})).json();h.success?(document.getElementById("phoneStep").classList.add("hidden"),document.getElementById("otpStep").classList.remove("hidden"),window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2A\u0E48\u0E07 OTP \u0E44\u0E1B\u0E22\u0E31\u0E07 "+u+s+" \u0E41\u0E25\u0E49\u0E27","success"),e[0].focus()):window.LayoutUtils&&window.LayoutUtils.showNotification(h.message||"\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14","error")}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E2A\u0E48\u0E07 OTP","error")}y(a,"\u0E2A\u0E48\u0E07 OTP")}async function t(){const s=Array.from(e).map(h=>h.value).join(""),u=d.value.trim(),m=document.getElementById("countryCode").value;if(s.length!==6){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01 OTP \u0E43\u0E2B\u0E49\u0E04\u0E23\u0E1A 6 \u0E2B\u0E25\u0E31\u0E01","error");return}w(i,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E15\u0E23\u0E27\u0E08\u0E2A\u0E2D\u0E1A...");try{const p=await(await fetch("/api/auth/phone/verify-otp",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content")},body:JSON.stringify({otp:s,phone:m+u})})).json();p.success?(window.LayoutUtils&&window.LayoutUtils.showNotification(p.message,"success"),setTimeout(()=>{window.location.href=p.redirect||"/dashboard"},1500)):(window.LayoutUtils&&window.LayoutUtils.showNotification(p.message||"\u0E23\u0E2B\u0E31\u0E2A OTP \u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error"),e.forEach(B=>B.value=""),e[0].focus())}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E15\u0E23\u0E27\u0E08\u0E2A\u0E2D\u0E1A OTP","error")}y(i,"\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19 OTP")}function o(){document.getElementById("phoneStep").classList.remove("hidden"),document.getElementById("otpStep").classList.add("hidden"),d.value="",e.forEach(s=>s.value="")}function l(s){return/^[0-9]{9,10}$/.test(s)}}function E(){const n=document.getElementById("loginForm"),c=document.getElementById("submitBtn");n&&n.addEventListener("submit",function(i){i.preventDefault(),r()&&a()});function r(){const i=n.querySelectorAll(".form-input[required]");let d=!0;return i.forEach(e=>{const f=new Event("blur");e.dispatchEvent(f),(e.classList.contains("error")||!e.value.trim())&&(d=!1)}),d}async function a(){w(c,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A...");try{const i=new FormData(n),e=await(await fetch("/api/login",{method:"POST",headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content"),Accept:"application/json"},body:i})).json();e.success?(window.LayoutUtils&&window.LayoutUtils.showNotification(e.message,"success"),setTimeout(()=>{window.location.href=e.redirect},1500)):(window.LayoutUtils&&window.LayoutUtils.showNotification(e.message||"\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14","error"),e.errors&&Object.keys(e.errors).forEach(f=>{const t=n.querySelector(`[name="${f}"]`);t&&showFieldError(t,e.errors[f][0])}))}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A","error")}y(c,"\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A")}}function w(n,c){n.disabled=!0,n.innerHTML=`
            <div class="loading">
                <div class="loading-spinner"></div>
                ${c}
            </div>
        `}function y(n,c){n.disabled=!1,n.innerHTML=c}});
