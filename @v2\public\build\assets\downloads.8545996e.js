document.addEventListener("DOMContentLoaded",function(){const n=document.getElementById("menu-toggle"),s=document.getElementById("mobile-menu"),t=document.getElementById("close-menu");n&&s&&n.addEventListener("click",function(){s.classList.add("active")}),t&&s&&t.addEventListener("click",function(){s.classList.remove("active")}),document.addEventListener("click",function(e){s&&s.classList.contains("active")&&!s.contains(e.target)&&!n.contains(e.target)&&s.classList.remove("active")});const l=document.getElementById("searchBtn");l&&l.addEventListener("click",function(){const e=u("media-type"),o=u("subject"),i=u("grade");this.innerHTML='<svg class="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>\u0E01\u0E33\u0E25\u0E31\u0E07\u0E04\u0E49\u0E19\u0E2B\u0E32...',this.disabled=!0,setTimeout(()=>{this.innerHTML="\u0E04\u0E49\u0E19\u0E2B\u0E32",this.disabled=!1,b(e,o,i)},1e3)});const c=document.querySelectorAll(".media-type-btn");c.forEach(e=>{e.addEventListener("click",function(){c.forEach(i=>i.classList.remove("bg-blue-100","border-blue-300")),this.classList.add("bg-blue-100","border-blue-300");const o=this.querySelector("span").textContent;p(o)})});const a=document.querySelectorAll(".filter-btn");a.forEach(e=>{e.addEventListener("click",function(){a.forEach(i=>i.classList.remove("active")),this.classList.add("active");const o=this.textContent.trim();y(o)})}),document.querySelectorAll(".download-btn").forEach(e=>{e.addEventListener("click",function(o){o.preventDefault();const i=this.closest(".media-card").querySelector("h3").textContent,d=this.textContent;this.innerHTML='<svg class="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>\u0E01\u0E33\u0E25\u0E31\u0E07\u0E14\u0E32\u0E27\u0E19\u0E4C\u0E42\u0E2B\u0E25\u0E14...',this.disabled=!0,setTimeout(()=>{this.innerHTML="\u2713 \u0E14\u0E32\u0E27\u0E19\u0E4C\u0E42\u0E2B\u0E25\u0E14\u0E41\u0E25\u0E49\u0E27",this.classList.remove("bg-blue-600","hover:bg-blue-700"),this.classList.add("bg-green-600"),v(`\u0E14\u0E32\u0E27\u0E19\u0E4C\u0E42\u0E2B\u0E25\u0E14 "${i}" \u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27`,"success"),setTimeout(()=>{this.innerHTML=d,this.disabled=!1,this.classList.remove("bg-green-600"),this.classList.add("bg-blue-600","hover:bg-blue-700")},3e3)},2e3)})});const r=document.querySelectorAll(".dropdown");r.forEach(e=>{const o=e.querySelector("button"),i=e.querySelector(".dropdown-content");o.addEventListener("click",function(d){d.stopPropagation(),r.forEach(h=>{h!==e&&(h.querySelector(".dropdown-content").style.display="none")}),i.style.display=i.style.display==="block"?"none":"block"})}),document.addEventListener("click",function(){r.forEach(e=>{e.querySelector(".dropdown-content").style.display="none"})}),document.querySelectorAll('input[type="checkbox"]').forEach(e=>{e.addEventListener("change",function(){g(this)})});const f={threshold:.1,rootMargin:"0px 0px -50px 0px"},m=new IntersectionObserver(e=>{e.forEach(o=>{o.isIntersecting&&(o.target.classList.add("fade-in"),m.unobserve(o.target))})},f);document.querySelectorAll(".media-card").forEach((e,o)=>{e.style.animationDelay=`${o*.1}s`,m.observe(e)})});function u(n){const s=document.querySelectorAll(`input[data-filter="${n}"]:checked`);return Array.from(s).map(t=>t.value)}function b(n,s,t){console.log("Filtering with:",{mediaTypes:n,subjects:s,grades:t}),document.querySelectorAll(".media-card, .table-row").forEach((c,a)=>{c.style.opacity="0",c.style.transform="translateY(20px)",setTimeout(()=>{c.style.transition="all 0.3s ease",c.style.opacity="1",c.style.transform="translateY(0)"},a*50)})}function p(n){console.log("Filtering by media type:",n)}function y(n){console.log("Filtering special materials by:",n)}function g(n){const s=n.closest(".dropdown"),t=s.querySelector("button span"),l=s.querySelectorAll('input[type="checkbox"]:checked');if(l.length>0)t.textContent=`\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E41\u0E25\u0E49\u0E27 (${l.length})`,t.classList.add("text-blue-600");else{const c=s.querySelector("button").getAttribute("data-original-text")||t.textContent;t.textContent=c,t.classList.remove("text-blue-600")}}function v(n,s="info"){const t=document.createElement("div");switch(t.className="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full",s){case"success":t.classList.add("bg-green-500","text-white");break;case"error":t.classList.add("bg-red-500","text-white");break;default:t.classList.add("bg-blue-500","text-white")}t.innerHTML=`
        <div class="flex items-center">
            <span>${n}</span>
            <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
    `,document.body.appendChild(t),setTimeout(()=>{t.classList.remove("translate-x-full")},100),setTimeout(()=>{t.classList.add("translate-x-full"),setTimeout(()=>{t.remove()},300)},5e3)}
