<?php namespace MyFatoorah\Library\API; use MyFatoorah\Library\MyFatoorah; use Exception; class  MyFatoorahList extends MyFatoorah{public static function getOneCurrencyRate($currency,$allRates){foreach($allRates as $value){if($value->Text==$currency){return (double) $value->Value;}}throw new Exception('The selected currency is not supported by MyFatoorah');}public function getCurrencyRate($currency){$allRates=$this->getCurrencyRates();return self::getOneCurrencyRate($currency,$allRates);}public function getCurrencyRates(){$url="$this->apiURL/v2/GetCurrenciesExchangeList";return (array) $this->callAPI($url,null,null,'Get Currencies Exchange List');}}