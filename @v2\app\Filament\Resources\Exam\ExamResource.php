<?php

namespace App\Filament\Resources\Exam;

use App\Filament\Resources\Exam\ExamResource\Pages;
use App\Models\Exam\Exam;
use App\Models\Subject;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ExamResource extends Resource
{
    protected static ?string $model = Exam::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?string $navigationLabel = 'Exercises & Exams';

    protected static ?string $modelLabel = 'Exercise';

    protected static ?string $pluralModelLabel = 'Exercises & Exams';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 7;

    // Permission methods
    public static function canViewAny(): bool
    {
        return auth()->user()->can('view_any_exam');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->can('create_exam');
    }

    public static function canView($record): bool
    {
        return auth()->user()->can('view_exam');
    }

    public static function canEdit($record): bool
    {
        return auth()->user()->can('update_exam');
    }

    public static function canDelete($record): bool
    {
        return auth()->user()->can('delete_exam');
    }

    public static function canDeleteAny(): bool
    {
        return auth()->user()->can('delete_any_exam');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Information')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Select::make('subject_id')
                            ->label('Subject')
                            ->relationship('subject', 'name')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Select::make('type')
                            ->options([
                                'exercise' => 'Exercise',
                                'exam' => 'Exam',
                            ])
                            ->required()
                            ->default('exercise'),

                        Forms\Components\TextInput::make('grade_level')
                            ->label('Grade Level')
                            ->placeholder('e.g., Grade 1, Grade 2, etc.')
                            ->maxLength(50),

                        Forms\Components\Select::make('visibility')
                            ->options([
                                'private' => 'Private (Only me)',
                                'school' => 'School (All school users)',
                                'public' => 'Public (Everyone)',
                            ])
                            ->required()
                            ->default('private')
                            ->helperText('Private: Only you can see this exam. School: All users in your school can see it. Public: Anyone can see and take this exam.'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Questions')
                    ->schema([
                        Forms\Components\Builder::make('questions_data')
                            ->label('Questions')
                            ->blocks([
                                Forms\Components\Builder\Block::make('question')
                                    ->label('Question')
                                    ->icon('heroicon-o-question-mark-circle')
                                    ->schema([
                                        Forms\Components\Textarea::make('question_text')
                                            ->label('Question')
                                            ->required()
                                            ->rows(3)
                                            ->columnSpanFull(),

                                        Forms\Components\Grid::make(3)
                                            ->schema([
                                                Forms\Components\Select::make('question_type')
                                                    ->label('Question Type')
                                                    ->options([
                                                        'multiple_choice' => 'Multiple Choice',
                                                        'short_answer' => 'Short Answer',
                                                        'essay' => 'Essay',
                                                    ])
                                                    ->required()
                                                    ->default('multiple_choice')
                                                    ->reactive(),

                                                Forms\Components\TextInput::make('points')
                                                    ->label('Points')
                                                    ->numeric()
                                                    ->required()
                                                    ->default(1)
                                                    ->minValue(0.1)
                                                    ->step(0.1),

                                                Forms\Components\Select::make('difficulty_level')
                                                    ->label('Difficulty')
                                                    ->options([
                                                        'easy' => 'Easy',
                                                        'medium' => 'Medium',
                                                        'hard' => 'Hard',
                                                    ])
                                                    ->required()
                                                    ->default('medium'),
                                            ]),

                                        Forms\Components\Repeater::make('choices')
                                            ->label('Answer Choices')
                                            ->schema([
                                                Forms\Components\Grid::make(12)
                                                    ->schema([
                                                        Forms\Components\TextInput::make('choice_text')
                                                            ->label('Choice Text')
                                                            ->required()
                                                            ->placeholder('Enter choice text...')
                                                            ->columnSpan(10),

                                                        Forms\Components\Toggle::make('is_correct')
                                                            ->label('Correct')
                                                            ->helperText('Mark as correct answer')
                                                            ->columnSpan(2),
                                                    ]),
                                            ])
                                            ->visible(fn (Get $get): bool => $get('question_type') === 'multiple_choice')
                                            ->defaultItems(4)
                                            ->addActionLabel('+ Add Choice')
                                            ->reorderable()
                                            ->reorderableWithButtons()
                                            ->collapsible()
                                            ->itemLabel(fn (array $state): ?string => $state['choice_text'] ?? 'New Choice')
                                            ->columnSpanFull()
                                            ->minItems(2)
                                            ->maxItems(10),

                                        Forms\Components\Textarea::make('explanation')
                                            ->label('Explanation (shown after answer)')
                                            ->rows(2)
                                            ->columnSpanFull(),

                                        Forms\Components\TagsInput::make('tags')
                                            ->label('Tags')
                                            ->placeholder('Add tags...')
                                            ->columnSpanFull(),
                                    ])
                                    ->columns(1),
                            ])
                            ->addActionLabel('+ Add Question')
                            ->reorderable()
                            ->reorderableWithButtons()
                            ->collapsible()
                            ->blockNumbers(false)
                            ->columnSpanFull()
                            ->blockPickerColumns(1)
                            ->blockPickerWidth('sm')
                            ->afterStateHydrated(function (Forms\Components\Builder $component, ?Exam $record) {
                                if (!$record) return;

                                $questionsData = $record->questions->map(function ($question) {
                                    $questionData = [
                                        'question_text' => $question->question_text,
                                        'question_type' => $question->question_type,
                                        'points' => $question->points,
                                        'difficulty_level' => $question->difficulty_level,
                                        'explanation' => $question->explanation,
                                        'tags' => $question->tags,
                                    ];

                                    if ($question->question_type === 'multiple_choice') {
                                        $questionData['choices'] = $question->choices->map(function ($choice) {
                                            return [
                                                'choice_text' => $choice->choice_text,
                                                'is_correct' => $choice->is_correct,
                                            ];
                                        })->toArray();
                                    }

                                    return [
                                        'type' => 'question',
                                        'data' => $questionData,
                                    ];
                                })->toArray();

                                $component->state($questionsData);
                            }),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Question Settings')
                    ->schema([
                        Forms\Components\TextInput::make('total_questions')
                            ->label('Number of Questions')
                            ->numeric()
                            ->required()
                            ->default(10)
                            ->minValue(1)
                            ->maxValue(100),

                        Forms\Components\TextInput::make('total_score')
                            ->label('Total Score')
                            ->numeric()
                            ->required()
                            ->default(100)
                            ->minValue(1),

                        Forms\Components\TextInput::make('time_limit')
                            ->label('Time Limit (minutes)')
                            ->numeric()
                            ->minValue(1)
                            ->helperText('Leave empty for no time limit'),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Randomization & Display')
                    ->schema([
                        Forms\Components\Toggle::make('randomize_questions')
                            ->label('Randomize Questions')
                            ->default(true),

                        Forms\Components\Toggle::make('randomize_choices')
                            ->label('Randomize Answer Choices')
                            ->default(true),

                        Forms\Components\Toggle::make('show_results_immediately')
                            ->label('Show Results Immediately')
                            ->default(false),

                        Forms\Components\Toggle::make('allow_multiple_attempts')
                            ->label('Allow Multiple Attempts')
                            ->default(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Availability')
                    ->schema([
                        Forms\Components\DateTimePicker::make('available_from')
                            ->label('Available From')
                            ->helperText('Leave empty to make available immediately'),

                        Forms\Components\DateTimePicker::make('available_until')
                            ->label('Available Until')
                            ->helperText('Leave empty for no end date'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subject.name')
                    ->label('Subject')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('grade_level')
                    ->label('Grade')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'exercise' => 'info',
                        'exam' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('visibility')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'private' => 'gray',
                        'school' => 'info',
                        'public' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('total_questions')
                    ->label('Questions')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('total_score')
                    ->label('Total Score')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('time_limit')
                    ->label('Time Limit')
                    ->formatStateUsing(fn (?int $state): string => $state ? "{$state} min" : 'No limit')
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\TextColumn::make('attempts_count')
                    ->label('Attempts')
                    ->counts('attempts')
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('available_from')
                    ->label('Available From')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('available_until')
                    ->label('Available Until')
                    ->dateTime('M j, Y g:i A')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('teacher.name')
                    ->label('Created By')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('subject_id')
                    ->label('Subject')
                    ->relationship('subject', 'name'),

                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'exercise' => 'Exercise',
                        'exam' => 'Exam',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),

                Tables\Filters\Filter::make('available_now')
                    ->label('Available Now')
                    ->query(fn (Builder $query): Builder => $query->available()),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                Tables\Actions\Action::make('view_attempts')
                    ->label('Attempts')
                    ->icon('heroicon-o-users')
                    ->url(fn (Exam $record): string => static::getUrl('view-attempts', ['record' => $record]))
                    ->color('success')
                    ->visible(fn (Exam $record): bool => $record->attempts()->count() > 0),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('team_id', filament()->getTenant()->id)
            ->with(['subject', 'teacher', 'attempts']);
    }

    public static function getRelations(): array
    {
        return [
            // Removed QuestionsRelationManager as we now use inline question builder
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExams::route('/'),
            'create' => Pages\CreateExam::route('/create'),
            'edit' => Pages\EditExam::route('/{record}/edit'),
            'view-attempts' => Pages\ViewAttempts::route('/{record}/attempts'),
            'attempt-details' => Pages\AttemptDetails::route('/{exam}/attempts/{user}'),
        ];
    }
}
