<?php

return [
    "model" => [
        "folder" => \App\Models\MediaManager\Folder::class,
        "media" => \App\Models\MediaManager\Media::class,
    ],

    "api" => [
        "active" => false,
        "middlewares" => [
            "api",
            "auth:sanctum"
        ],
        "prefix" => "api/media-manager",
        "resources" => [
            "folders" => \App\Filament\Resources\MediaManager\JsonResources\FoldersResource::class,
            "folder" => \App\Filament\Resources\MediaManager\JsonResources\FolderResource::class,
            "media" => \App\Filament\Resources\MediaManager\JsonResources\MediaResource::class
        ]
    ],

    "user" => [
      'column_name' => 'name', // Change the value if your field in users table is different from "name"
    ],

    "navigation_sort" => 0,

    // Plugin functionality settings
    "allow_sub_folders" => true,
    "allow_user_access" => true,
];
