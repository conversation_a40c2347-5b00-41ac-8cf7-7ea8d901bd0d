<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-book-open class="w-5 h-5 text-primary-600 mr-2" />
                วิชาที่สอน
            </div>
        </x-slot>

        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            @foreach($this->getCourses() as $course)
                @php
                    $circleData = $this->getCircleProgress($course['progress']);
                @endphp
                <div class="bg-gradient-to-br {{ $course['bg_class'] }} rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow cursor-pointer border {{ $course['border_class'] }}">
                    <div class="flex flex-col items-center">
                        <!-- Circular Progress -->
                        <div class="relative w-20 h-20 mb-2">
                            <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                                <!-- Background circle -->
                                <circle 
                                    cx="50" 
                                    cy="50" 
                                    r="40" 
                                    fill="none" 
                                    stroke="#e6e6e6" 
                                    stroke-width="8" 
                                    stroke-linecap="round"
                                />
                                <!-- Progress circle -->
                                <circle 
                                    cx="50" 
                                    cy="50" 
                                    r="40" 
                                    fill="none" 
                                    stroke="{{ $course['color'] }}" 
                                    stroke-width="8" 
                                    stroke-linecap="round"
                                    stroke-dasharray="{{ $circleData['strokeDasharray'] }}"
                                    stroke-dashoffset="{{ $circleData['strokeDashoffset'] }}"
                                    class="transition-all duration-500 ease-in-out"
                                />
                            </svg>
                            <!-- Progress text -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-sm font-semibold text-gray-700">{{ $course['progress'] }}%</span>
                            </div>
                        </div>
                        
                        <!-- Course info -->
                        <h3 class="font-medium text-center text-gray-900 dark:text-white">{{ $course['name'] }}</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 text-center">{{ $course['progress'] }}% แผนการสอน</p>
                        <p class="text-xs text-gray-500 dark:text-gray-500 text-center">
                            สอนไป {{ $course['completed_chapters'] }} บท / จาก {{ $course['total_chapters'] }} บท
                        </p>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
