// Student Dashboard JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle (inherited from layout)
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');
    const closeMenu = document.getElementById('close-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('active');
        });
    }

    if (closeMenu && mobileMenu) {
        closeMenu.addEventListener('click', function() {
            mobileMenu.classList.remove('active');
        });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        if (mobileMenu && mobileMenu.classList.contains('active')) {
            if (!mobileMenu.contains(event.target) && !menuToggle.contains(event.target)) {
                mobileMenu.classList.remove('active');
            }
        }
    });

    // Calendar functionality
    const currentMonthElement = document.getElementById('current-month');
    const prevMonthButton = document.getElementById('prev-month');
    const nextMonthButton = document.getElementById('next-month');
    const calendarDays = document.getElementById('calendar-days');

    let currentDate = new Date();
    const months = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
    ];

    function updateCalendar() {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();
        
        // Update month display
        currentMonthElement.textContent = `${months[month]} ${year + 543}`;
        
        // Clear calendar days
        calendarDays.innerHTML = '';
        
        // Get first day of month and number of days
        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        
        // Add empty cells for days before month starts
        for (let i = 0; i < firstDay; i++) {
            const emptyDay = document.createElement('div');
            calendarDays.appendChild(emptyDay);
        }
        
        // Add days of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;
            
            // Mark today as active
            const today = new Date();
            if (year === today.getFullYear() && 
                month === today.getMonth() && 
                day === today.getDate()) {
                dayElement.classList.add('active');
            }
            
            // Add event indicators for certain days
            if ([7, 15, 22, 28].includes(day)) {
                dayElement.classList.add('has-event');
            }
            
            dayElement.addEventListener('click', function() {
                // Remove active class from all days
                document.querySelectorAll('.calendar-day').forEach(d => d.classList.remove('active'));
                // Add active class to clicked day
                this.classList.add('active');
                
                // Update event list for selected day
                updateEventList(day);
            });
            
            calendarDays.appendChild(dayElement);
        }
    }

    function updateEventList(day) {
        const eventList = document.getElementById('event-list');
        
        // Sample events for different days
        const events = {
            7: [
                {
                    type: 'live-class',
                    title: 'Live Class: คณิตศาสตร์ - เรื่องเศษส่วน',
                    time: '17:00 - 18:00 น.',
                    color: 'blue'
                },
                {
                    type: 'activity-box',
                    title: 'กล่องกิจกรรม: วิทยาศาสตร์ - การทดลองเรื่องแสง',
                    time: 'จัดส่งวันนี้',
                    color: 'green'
                }
            ],
            15: [
                {
                    type: 'exam',
                    title: 'สอบกลางภาค: ภาษาไทย',
                    time: '09:00 - 11:00 น.',
                    color: 'red'
                }
            ],
            22: [
                {
                    type: 'workshop',
                    title: 'Workshop: การเขียนโปรแกรม',
                    time: '14:00 - 16:00 น.',
                    color: 'purple'
                }
            ],
            28: [
                {
                    type: 'field-trip',
                    title: 'ทัศนศึกษา: พิพิธภัณฑ์วิทยาศาสตร์',
                    time: '08:00 - 17:00 น.',
                    color: 'orange'
                }
            ]
        };

        const dayEvents = events[day] || [];
        
        if (dayEvents.length === 0) {
            eventList.innerHTML = '<p class="text-gray-500 text-center py-4">ไม่มีกิจกรรมในวันนี้</p>';
            return;
        }

        eventList.innerHTML = dayEvents.map(event => `
            <div class="flex items-start p-3 rounded-lg bg-${event.color}-50 mb-2">
                <div class="bg-${event.color}-100 p-2 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-${event.color}-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <h5 class="font-medium text-${event.color}-800">${event.title}</h5>
                    <p class="text-sm text-${event.color}-600">${event.time}</p>
                </div>
            </div>
        `).join('');
    }

    // Calendar navigation
    if (prevMonthButton) {
        prevMonthButton.addEventListener('click', function() {
            currentDate.setMonth(currentDate.getMonth() - 1);
            updateCalendar();
        });
    }

    if (nextMonthButton) {
        nextMonthButton.addEventListener('click', function() {
            currentDate.setMonth(currentDate.getMonth() + 1);
            updateCalendar();
        });
    }

    // Initialize calendar
    updateCalendar();

    // Subject card click handlers
    const subjectCards = document.querySelectorAll('.subject-card');
    subjectCards.forEach(card => {
        card.addEventListener('click', function() {
            const subject = this.querySelector('h4').textContent;
            showSubjectDetails(subject);
        });
    });

    function showSubjectDetails(subject) {
        // Create modal for subject details
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'flex';
        
        modal.innerHTML = `
            <div class="modal-content">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">${subject}</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">ความก้าวหน้าล่าสุด</h4>
                        <div class="progress-bar mb-2">
                            <div class="progress-fill bg-blue-500" style="width: 75%"></div>
                        </div>
                        <p class="text-sm text-gray-600">75% ของแบบฝึกหัดทั้งหมด</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">คะแนนเฉลี่ย</h4>
                        <p class="text-2xl font-bold text-green-600">92/100</p>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">แบบฝึกหัดที่แนะนำ</h4>
                        <ul class="space-y-2">
                            <li class="flex justify-between items-center">
                                <span>บทที่ 5: เศษส่วน</span>
                                <button class="text-blue-600 hover:text-blue-800">เริ่มทำ</button>
                            </li>
                            <li class="flex justify-between items-center">
                                <span>บทที่ 6: ทศนิยม</span>
                                <button class="text-blue-600 hover:text-blue-800">เริ่มทำ</button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal functionality
        const closeButton = modal.querySelector('.close-modal');
        closeButton.addEventListener('click', function() {
            document.body.removeChild(modal);
        });
        
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // Action button handlers
    const actionButtons = document.querySelectorAll('.action-button');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
            
            const action = this.textContent.trim();
            const originalText = this.innerHTML;
            
            // Show loading state
            this.innerHTML = '<svg class="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>กำลังโหลด...';
            this.disabled = true;
            
            // Simulate action
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
                
                // Show success message
                showNotification(`${action} สำเร็จ!`, 'success');
            }, 1500);
        });
    });

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;
        
        switch (type) {
            case 'success':
                notification.classList.add('bg-green-500', 'text-white');
                break;
            case 'error':
                notification.classList.add('bg-red-500', 'text-white');
                break;
            default:
                notification.classList.add('bg-blue-500', 'text-white');
        }
        
        notification.innerHTML = `
            <div class="flex items-center">
                <span>${message}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // Animate progress circles on load
    const progressCircles = document.querySelectorAll('.progress');
    progressCircles.forEach(circle => {
        const percentage = parseInt(circle.parentElement.querySelector('.percentage').textContent);
        const circumference = 2 * Math.PI * 45; // radius = 45
        const offset = circumference - (percentage / 100) * circumference;
        
        setTimeout(() => {
            circle.style.strokeDashoffset = offset;
        }, 500);
    });

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${index * 0.1}s`;
    });
});
