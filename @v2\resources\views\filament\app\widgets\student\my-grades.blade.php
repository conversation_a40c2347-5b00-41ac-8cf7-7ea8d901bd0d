<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-chart-bar class="w-5 h-5 text-primary-600 mr-2" />
                📈 ความก้าวหน้าแยกตามวิชา
            </div>
        </x-slot>

        <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4">
            @foreach($this->getSubjectProgress() as $subject)
                @php
                    $circleData = $this->getCircleProgress($subject['progress']);
                @endphp
                <div class="bg-gradient-to-br {{ $subject['bg_gradient'] }} rounded-2xl p-5 text-center cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg">
                    <div class="flex flex-col items-center">
                        <!-- Circular Progress -->
                        <div class="relative w-24 h-24 mb-3">
                            <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                                <!-- Background circle -->
                                <circle
                                    cx="50"
                                    cy="50"
                                    r="45"
                                    fill="none"
                                    stroke="rgba(255, 255, 255, 0.3)"
                                    stroke-width="8"
                                    stroke-linecap="round"
                                />
                                <!-- Progress circle -->
                                <circle
                                    cx="50"
                                    cy="50"
                                    r="45"
                                    fill="none"
                                    stroke="white"
                                    stroke-width="8"
                                    stroke-linecap="round"
                                    stroke-dasharray="{{ $circleData['strokeDasharray'] }}"
                                    stroke-dashoffset="{{ $circleData['strokeDashoffset'] }}"
                                    class="transition-all duration-1000 ease-in-out"
                                />
                            </svg>
                            <!-- Progress text -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-lg font-bold text-white">{{ $subject['progress'] }}%</span>
                            </div>
                        </div>

                        <!-- Subject info -->
                        <h4 class="font-semibold text-white mb-1">{{ $subject['name'] }}</h4>
                        <p class="text-sm text-white/90 mb-1">{{ $subject['exercises_completed'] }} แบบฝึกหัด</p>
                        <p class="text-xs text-white/80">{{ $subject['average_score'] }} จาก {{ $subject['max_score'] }} คะแนนเฉลี่ย</p>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
