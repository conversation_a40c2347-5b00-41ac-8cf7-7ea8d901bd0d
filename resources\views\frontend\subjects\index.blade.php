@extends('layouts.frontend')

@section('title', 'Subjects')

@section('content')
<div class="bg-white">
    <div class="relative bg-indigo-900">
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">Subjects</h1>
            <p class="mt-6 text-xl text-indigo-100 max-w-3xl">
                Explore different subjects and find courses and books organized by topic.
            </p>
        </div>
    </div>

    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        @if($subjects->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($subjects as $subject)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                        <div class="p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $subject->name }}</h3>
                            @if($subject->description)
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ strip_tags($subject->description) }}</p>
                            @endif
                            
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex space-x-4 text-sm text-gray-500">
                                    <span>{{ $subject->courses_count ?? 0 }} courses</span>
                                    <span>{{ $subject->books_count ?? 0 }} books</span>
                                </div>
                            </div>
                            
                            <a href="{{ route('frontend.subjects.show', $subject) }}" class="text-indigo-600 hover:text-indigo-500 font-medium text-sm">
                                Explore Subject →
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
            <div class="mt-12">{{ $subjects->links() }}</div>
        @else
            <div class="text-center py-12">
                <h3 class="mt-2 text-sm font-medium text-gray-900">No subjects available</h3>
                <p class="mt-1 text-sm text-gray-500">Check back later for new subjects.</p>
            </div>
        @endif
    </div>
</div>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
