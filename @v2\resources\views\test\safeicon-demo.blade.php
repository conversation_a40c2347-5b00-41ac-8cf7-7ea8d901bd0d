<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SafeIcon Demo - Development Testing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8" x-data="safeIconDemo()">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900 mb-2">🎯 SafeIcon Demo</h1>
                        <p class="text-gray-600">Browse and test the complete SafeIcon library</p>
                    </div>
                    <a href="{{ route('test.dashboard') }}" class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                        ← Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Search Icons</label>
                        <input type="text" 
                               x-model="searchTerm" 
                               placeholder="Search by name or category..."
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select x-model="selectedCategory" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">All Categories</option>
                            <template x-for="category in categories" :key="category">
                                <option :value="category" x-text="category"></option>
                            </template>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Size</label>
                        <select x-model="iconSize" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="w-4 h-4">Small (16px)</option>
                            <option value="w-6 h-6">Medium (24px)</option>
                            <option value="w-8 h-8">Large (32px)</option>
                            <option value="w-12 h-12">Extra Large (48px)</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4 flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        Showing <span x-text="filteredIcons.length"></span> of <span x-text="icons.length"></span> icons
                    </div>
                    <button @click="copyAllUsage()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">
                        Copy Usage Guide
                    </button>
                </div>
            </div>

            <!-- Icons Grid -->
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
                <template x-for="icon in filteredIcons" :key="icon.name">
                    <div class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow cursor-pointer"
                         @click="selectIcon(icon)">
                        <!-- Icon Preview -->
                        <div class="flex items-center justify-center h-16 mb-3">
                            <div :class="iconSize + ' text-gray-700'" x-html="icon.svg"></div>
                        </div>
                        
                        <!-- Icon Info -->
                        <div class="text-center">
                            <h3 class="text-sm font-medium text-gray-900 truncate" x-text="icon.name"></h3>
                            <p class="text-xs text-gray-500 mt-1" x-text="icon.category"></p>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Empty State -->
            <div x-show="filteredIcons.length === 0" class="bg-white rounded-lg shadow-sm p-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No icons found</h3>
                <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria.</p>
            </div>

            <!-- Icon Detail Modal -->
            <div x-show="selectedIcon" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="selectedIcon = null">
                <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                    <template x-if="selectedIcon">
                        <div>
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold" x-text="selectedIcon.name"></h3>
                                <button @click="selectedIcon = null" class="text-gray-400 hover:text-gray-600">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                            
                            <!-- Large Icon Preview -->
                            <div class="flex items-center justify-center h-24 mb-4 bg-gray-50 rounded-lg">
                                <div class="w-16 h-16 text-gray-700" x-html="selectedIcon.svg"></div>
                            </div>
                            
                            <!-- Icon Details -->
                            <div class="space-y-3 mb-4">
                                <div>
                                    <span class="text-sm font-medium text-gray-700">Category:</span>
                                    <span class="text-sm text-gray-900 ml-2" x-text="selectedIcon.category"></span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-700">Usage:</span>
                                    <div class="mt-1 p-2 bg-gray-100 rounded text-xs font-mono" x-text="selectedIcon.usage"></div>
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex space-x-2">
                                <button @click="copyIconCode(selectedIcon)" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    Copy SVG
                                </button>
                                <button @click="copyIconUsage(selectedIcon)" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                    Copy Usage
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <script>
        function safeIconDemo() {
            return {
                searchTerm: '',
                selectedCategory: '',
                iconSize: 'w-6 h-6',
                selectedIcon: null,
                icons: [
                    // Academic Icons
                    { name: 'Academic Cap', category: 'Academic', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"></path></svg>', usage: '<x-heroicon-o-academic-cap class="w-6 h-6" />' },
                    { name: 'Book Open', category: 'Academic', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>', usage: '<x-heroicon-o-book-open class="w-6 h-6" />' },
                    { name: 'Calculator', category: 'Academic', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>', usage: '<x-heroicon-o-calculator class="w-6 h-6" />' },
                    
                    // Navigation Icons
                    { name: 'Home', category: 'Navigation', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>', usage: '<x-heroicon-o-home class="w-6 h-6" />' },
                    { name: 'Menu', category: 'Navigation', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>', usage: '<x-heroicon-o-menu class="w-6 h-6" />' },
                    { name: 'Arrow Right', category: 'Navigation', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path></svg>', usage: '<x-heroicon-o-arrow-right class="w-6 h-6" />' },
                    
                    // Communication Icons
                    { name: 'Mail', category: 'Communication', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>', usage: '<x-heroicon-o-mail class="w-6 h-6" />' },
                    { name: 'Phone', category: 'Communication', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path></svg>', usage: '<x-heroicon-o-phone class="w-6 h-6" />' },
                    { name: 'Chat', category: 'Communication', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>', usage: '<x-heroicon-o-chat class="w-6 h-6" />' },
                    
                    // Action Icons
                    { name: 'Plus', category: 'Actions', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>', usage: '<x-heroicon-o-plus class="w-6 h-6" />' },
                    { name: 'Edit', category: 'Actions', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>', usage: '<x-heroicon-o-pencil class="w-6 h-6" />' },
                    { name: 'Delete', category: 'Actions', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>', usage: '<x-heroicon-o-trash class="w-6 h-6" />' },
                    
                    // Status Icons
                    { name: 'Check', category: 'Status', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>', usage: '<x-heroicon-o-check class="w-6 h-6" />' },
                    { name: 'X', category: 'Status', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>', usage: '<x-heroicon-o-x class="w-6 h-6" />' },
                    { name: 'Warning', category: 'Status', svg: '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 18.5c-.77.833.192 2.5 1.732 2.5z"></path></svg>', usage: '<x-heroicon-o-exclamation-triangle class="w-6 h-6" />' }
                ],

                get categories() {
                    return [...new Set(this.icons.map(icon => icon.category))].sort();
                },

                get filteredIcons() {
                    return this.icons.filter(icon => {
                        const matchesSearch = !this.searchTerm || 
                            icon.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                            icon.category.toLowerCase().includes(this.searchTerm.toLowerCase());
                        
                        const matchesCategory = !this.selectedCategory || icon.category === this.selectedCategory;
                        
                        return matchesSearch && matchesCategory;
                    });
                },

                selectIcon(icon) {
                    this.selectedIcon = icon;
                },

                copyIconCode(icon) {
                    navigator.clipboard.writeText(icon.svg).then(() => {
                        alert('SVG code copied to clipboard!');
                    });
                },

                copyIconUsage(icon) {
                    navigator.clipboard.writeText(icon.usage).then(() => {
                        alert('Usage code copied to clipboard!');
                    });
                },

                copyAllUsage() {
                    const usageGuide = `
# SafeIcon Usage Guide

## Basic Usage
<x-heroicon-o-icon-name class="w-6 h-6" />

## Available Sizes
- w-4 h-4 (16px)
- w-6 h-6 (24px) - Default
- w-8 h-8 (32px)
- w-12 h-12 (48px)

## Color Classes
- text-gray-600 (Default)
- text-blue-600
- text-green-600
- text-red-600
- text-yellow-600

## Example
<x-heroicon-o-academic-cap class="w-6 h-6 text-blue-600" />
                    `.trim();
                    
                    navigator.clipboard.writeText(usageGuide).then(() => {
                        alert('Usage guide copied to clipboard!');
                    });
                }
            }
        }
    </script>
</body>
</html>
