<?php

namespace App\Filament\Resources\TeachingScheduleResource\Pages;

use App\Filament\Resources\TeachingScheduleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTeachingSchedule extends EditRecord
{
    protected static string $resource = TeachingScheduleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
