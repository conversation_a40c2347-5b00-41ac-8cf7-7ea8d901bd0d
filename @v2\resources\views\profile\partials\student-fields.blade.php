<!-- Student-specific Fields -->

<!-- Emergency Contact Information -->
<div class="form-section">
    <h2 class="section-title">Emergency Contact Information</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="emergency_contact_name" class="form-label required">Emergency Contact Name</label>
            <input type="text" id="emergency_contact_name" name="emergency_contact_name" class="form-input" 
                   value="{{ old('emergency_contact_name', $profile->emergency_contact_name) }}" required>
            @error('emergency_contact_name')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="emergency_contact_phone" class="form-label required">Emergency Contact Phone</label>
            <input type="tel" id="emergency_contact_phone" name="emergency_contact_phone" class="form-input" 
                   value="{{ old('emergency_contact_phone', $profile->emergency_contact_phone) }}" required>
            @error('emergency_contact_phone')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="emergency_contact_relationship" class="form-label required">Relationship</label>
            <select id="emergency_contact_relationship" name="emergency_contact_relationship" class="form-input form-select" required>
                <option value="">Select Relationship</option>
                <option value="parent" {{ old('emergency_contact_relationship', $profile->emergency_contact_relationship) === 'parent' ? 'selected' : '' }}>Parent</option>
                <option value="guardian" {{ old('emergency_contact_relationship', $profile->emergency_contact_relationship) === 'guardian' ? 'selected' : '' }}>Guardian</option>
                <option value="sibling" {{ old('emergency_contact_relationship', $profile->emergency_contact_relationship) === 'sibling' ? 'selected' : '' }}>Sibling</option>
                <option value="relative" {{ old('emergency_contact_relationship', $profile->emergency_contact_relationship) === 'relative' ? 'selected' : '' }}>Relative</option>
                <option value="other" {{ old('emergency_contact_relationship', $profile->emergency_contact_relationship) === 'other' ? 'selected' : '' }}>Other</option>
            </select>
            @error('emergency_contact_relationship')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>

<!-- Academic Information -->
<div class="form-section">
    <h2 class="section-title">Academic Information</h2>
    <div class="form-grid">
        <div class="form-group">
            <label for="student_id" class="form-label">Student ID</label>
            <input type="text" id="student_id" name="student_id" class="form-input" 
                   value="{{ old('student_id', $profile->student_id) }}" 
                   placeholder="Enter your student ID">
            @error('student_id')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="grade_level" class="form-label">Grade Level</label>
            <select id="grade_level" name="grade_level" class="form-input form-select">
                <option value="">Select Grade Level</option>
                <option value="kindergarten" {{ old('grade_level', $profile->grade_level) === 'kindergarten' ? 'selected' : '' }}>Kindergarten</option>
                <option value="grade_1" {{ old('grade_level', $profile->grade_level) === 'grade_1' ? 'selected' : '' }}>Grade 1</option>
                <option value="grade_2" {{ old('grade_level', $profile->grade_level) === 'grade_2' ? 'selected' : '' }}>Grade 2</option>
                <option value="grade_3" {{ old('grade_level', $profile->grade_level) === 'grade_3' ? 'selected' : '' }}>Grade 3</option>
                <option value="grade_4" {{ old('grade_level', $profile->grade_level) === 'grade_4' ? 'selected' : '' }}>Grade 4</option>
                <option value="grade_5" {{ old('grade_level', $profile->grade_level) === 'grade_5' ? 'selected' : '' }}>Grade 5</option>
                <option value="grade_6" {{ old('grade_level', $profile->grade_level) === 'grade_6' ? 'selected' : '' }}>Grade 6</option>
                <option value="grade_7" {{ old('grade_level', $profile->grade_level) === 'grade_7' ? 'selected' : '' }}>Grade 7</option>
                <option value="grade_8" {{ old('grade_level', $profile->grade_level) === 'grade_8' ? 'selected' : '' }}>Grade 8</option>
                <option value="grade_9" {{ old('grade_level', $profile->grade_level) === 'grade_9' ? 'selected' : '' }}>Grade 9</option>
                <option value="grade_10" {{ old('grade_level', $profile->grade_level) === 'grade_10' ? 'selected' : '' }}>Grade 10</option>
                <option value="grade_11" {{ old('grade_level', $profile->grade_level) === 'grade_11' ? 'selected' : '' }}>Grade 11</option>
                <option value="grade_12" {{ old('grade_level', $profile->grade_level) === 'grade_12' ? 'selected' : '' }}>Grade 12</option>
                <option value="university" {{ old('grade_level', $profile->grade_level) === 'university' ? 'selected' : '' }}>University</option>
            </select>
            @error('grade_level')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>

        <div class="form-group">
            <label for="class_section" class="form-label">Class Section</label>
            <input type="text" id="class_section" name="class_section" class="form-input" 
                   value="{{ old('class_section', $profile->class_section) }}" 
                   placeholder="e.g., A, B, C">
            @error('class_section')
                <div class="error-message">{{ $message }}</div>
            @enderror
        </div>
    </div>
</div>
