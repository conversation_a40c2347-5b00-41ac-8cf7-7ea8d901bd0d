/* Homepage Specific Styles */

/* Font Family */
.font-prompt {
    font-family: 'Prompt', sans-serif;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Enhanced Hover Effects */
.hover-lift {
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), 
                box-shadow 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Smooth Button Transitions */
.btn-primary {
    background: linear-gradient(90deg, #06b6d4, #3b82f6);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
    transform: translateY(-1px);
}

.btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn-primary:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

.btn-secondary {
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Homepage specific navigation link styles */
.nav-link:hover {
    color: #06b6d4;
}

/* Card Hover Effects */
.course-card {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: rgba(6, 182, 212, 0.3);
}

/* Feature Icon Animation */
.feature-icon {
    transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

/* Testimonial Card Animation */
.testimonial-card {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.testimonial-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

/* Floating Animation */
.float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
}

/* Pulse Animation */
.pulse {
    animation: pulse 3s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 1s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Staggered Fade In */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* Category Badge Hover */
.category-badge {
    transition: all 0.3s ease;
}

.category-badge:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Stats Counter Animation */
.counter-value {
    transition: all 0.5s ease-out;
}

/* Instructor Card Hover */
.instructor-card {
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.instructor-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.social-icon {
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-3px);
    color: #06b6d4;
}

/* FAQ Accordion Animation */
.faq-item {
    transition: all 0.3s ease;
}

.faq-item:hover {
    background-color: rgba(241, 245, 249, 0.8);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
}

.faq-answer.active {
    max-height: 1000px;
    transition: max-height 1s ease-in-out;
}

.faq-icon {
    transition: transform 0.3s ease;
}

.faq-icon.active {
    transform: rotate(45deg);
}

/* Newsletter Input Animation */
.newsletter-input {
    transition: all 0.3s ease;
}

.newsletter-input:focus {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Homepage specific footer link styles - removed as now in layout.css */

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Homepage specific background shapes - now in layout.css */

/* Course Progress Bar */
.progress-bar {
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
}

.progress-value {
    height: 100%;
    background: linear-gradient(90deg, #06b6d4, #3b82f6);
    border-radius: 3px;
    transition: width 0.5s ease;
}

/* Rating Stars */
.stars {
    display: inline-flex;
    color: #fbbf24;
}

/* Homepage specific mobile menu styles - now in layout.css */

/* Course Level Badge */
.level-badge {
    transition: all 0.3s ease;
}

.level-badge:hover {
    transform: scale(1.05);
}

/* Pricing Toggle */
.pricing-toggle {
    transition: all 0.3s ease;
}

.toggle-dot {
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Blob Animation */
.blob {
    animation: blob-animation 15s infinite alternate;
    opacity: 0.1;
}

@keyframes blob-animation {
    0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
    50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
    100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
}

/* SVG Animations */
.path-animation {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: dash 3s linear forwards;
}

@keyframes dash {
    to {
        stroke-dashoffset: 0;
    }
}

.fill-animation {
    animation: fillIn 2s ease-in-out forwards;
    opacity: 0;
}

@keyframes fillIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

.rotate-animation {
    transform-origin: center;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.morph-animation {
    animation: morph 8s ease-in-out infinite alternate;
}

@keyframes morph {
    0% { d: path("M120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z"); }
    25% { d: path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z"); }
    50% { d: path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,100 160,80 180,100 C200,120 160,140 120,120 Z"); }
    75% { d: path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,100 160,80 180,100 C200,120 160,140 120,120 Z"); }
    100% { d: path("M120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z"); }
}

.wave-animation {
    animation: wave 3s ease-in-out infinite alternate;
}

@keyframes wave {
    0% { d: path("M0,100 C150,100 150,20 300,20 C450,20 450,100 600,100 L600,200 L0,200 Z"); }
    100% { d: path("M0,100 C150,20 150,100 300,100 C450,100 450,20 600,20 L600,200 L0,200 Z"); }
}

.bounce-animation {
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-20px); }
}

.pulse-glow {
    filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.3));
    animation: pulseGlow 3s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% { filter: drop-shadow(0 0 5px rgba(6, 182, 212, 0.3)); }
    50% { filter: drop-shadow(0 0 15px rgba(6, 182, 212, 0.7)); }
}

.draw-animation {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation: draw 5s ease-in-out forwards;
}

@keyframes draw {
    to {
        stroke-dashoffset: 0;
    }
}

.gradient-animation {
    animation: gradientShift 5s ease infinite;
}

@keyframes gradientShift {
    0% { stop-color: #06b6d4; }
    50% { stop-color: #3b82f6; }
    100% { stop-color: #06b6d4; }
}

.shake-animation {
    animation: shake 0.5s ease-in-out infinite;
    transform-origin: center;
}

@keyframes shake {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(5deg); }
    75% { transform: rotate(-5deg); }
}

.pop-animation {
    animation: pop 2s ease-in-out infinite;
    transform-origin: center;
}

@keyframes pop {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.orbit-animation {
    animation: orbit 10s linear infinite;
    transform-origin: 50% 50%;
}

@keyframes orbit {
    0% { transform: rotate(0deg) translateX(50px) rotate(0deg); }
    100% { transform: rotate(360deg) translateX(50px) rotate(-360deg); }
}

.dash-animation {
    stroke-dasharray: 10;
    animation: dash-move 20s linear infinite;
}

@keyframes dash-move {
    to {
        stroke-dashoffset: -1000;
    }
}

.flicker-animation {
    animation: flicker 3s linear infinite;
}

@keyframes flicker {
    0%, 19.999%, 22%, 62.999%, 64%, 64.999%, 70%, 100% { opacity: 1; }
    20%, 21.999%, 63%, 63.999%, 65%, 69.999% { opacity: 0.5; }
}

.spin-animation {
    animation: spin 10s linear infinite;
    transform-origin: center;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* SVG Hover Effects */
.hover-glow:hover {
    filter: drop-shadow(0 0 10px rgba(6, 182, 212, 0.7));
    transition: filter 0.3s ease;
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.1);
}

.hover-color-shift {
    transition: fill 0.3s ease, stroke 0.3s ease;
}

.hover-color-shift:hover {
    fill: #3b82f6;
    stroke: #06b6d4;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 18px;
    height: 18px;
    background-color: #ff6b6b;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}
