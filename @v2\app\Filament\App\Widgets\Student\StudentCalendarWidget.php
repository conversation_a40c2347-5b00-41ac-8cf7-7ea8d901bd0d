<?php

namespace App\Filament\App\Widgets\Student;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class StudentCalendarWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.student.student-calendar';
    
    protected int | string | array $columnSpan = 'full';

    public $currentMonth;
    public $currentYear;

    public function mount()
    {
        $this->currentMonth = Carbon::now()->month;
        $this->currentYear = Carbon::now()->year;
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('student');
    }

    public function getCalendarData(): array
    {
        $startOfMonth = Carbon::create($this->currentYear, $this->currentMonth, 1);
        $endOfMonth = $startOfMonth->copy()->endOfMonth();
        
        // Get first day of calendar (might be from previous month)
        $startOfCalendar = $startOfMonth->copy()->startOfWeek(Carbon::SUNDAY);
        
        // Get last day of calendar (might be from next month)
        $endOfCalendar = $endOfMonth->copy()->endOfWeek(Carbon::SATURDAY);
        
        $days = [];
        $current = $startOfCalendar->copy();
        
        while ($current <= $endOfCalendar) {
            $days[] = [
                'date' => $current->day,
                'is_current_month' => $current->month === $this->currentMonth,
                'is_today' => $current->isToday(),
                'events' => $this->getEventsForDate($current),
                'full_date' => $current->format('Y-m-d')
            ];
            $current->addDay();
        }
        
        return [
            'month_name' => $startOfMonth->locale('th')->translatedFormat('F Y'),
            'days' => $days,
            'day_names' => ['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส']
        ];
    }

    private function getEventsForDate(Carbon $date): array
    {
        // Placeholder data - replace with actual events
        $events = [];
        
        if ($date->day === 15) {
            $events = [
                ['type' => 'live-class', 'title' => 'Live Class: คณิตศาสตร์', 'time' => '17:00-18:00', 'color' => 'blue'],
            ];
        } elseif ($date->day === 16) {
            $events = [
                ['type' => 'activity-box', 'title' => 'กล่องกิจกรรม: วิทยาศาสตร์', 'time' => 'จัดส่งวันนี้', 'color' => 'green'],
            ];
        } elseif ($date->day === 18) {
            $events = [
                ['type' => 'live-class', 'title' => 'Live Class: วิทยาศาสตร์', 'time' => '16:00-17:00', 'color' => 'blue'],
            ];
        } elseif ($date->day === 20) {
            $events = [
                ['type' => 'test', 'title' => 'แบบทดสอบ: คณิตศาสตร์', 'time' => 'ตลอดทั้งวัน', 'color' => 'red'],
            ];
        }
        
        return $events;
    }

    public function getTodayActivities(): array
    {
        // Placeholder data - replace with actual today's activities
        return [
            [
                'type' => 'live-class',
                'title' => 'Live Class: คณิตศาสตร์ - เรื่องเศษส่วน',
                'time' => '17:00 - 18:00 น.',
                'color' => 'blue',
                'actions' => [
                    ['label' => 'เข้าร่วมคลาส', 'color' => 'blue', 'icon' => 'heroicon-o-play'],
                    ['label' => 'สรุปบทเรียน (.pdf)', 'color' => 'green', 'icon' => 'heroicon-o-document'],
                    ['label' => 'แบบทดสอบหลังเรียน', 'color' => 'purple', 'icon' => 'heroicon-o-clipboard-document-list'],
                ]
            ],
            [
                'type' => 'activity-box',
                'title' => 'กล่องกิจกรรม: วิทยาศาสตร์ - การทดลองเรื่องแสง',
                'time' => 'จัดส่งวันนี้',
                'color' => 'green',
                'actions' => [
                    ['label' => 'ติดตามพัสดุ', 'color' => 'green', 'icon' => 'heroicon-o-truck'],
                ]
            ],
        ];
    }

    public function previousMonth()
    {
        if ($this->currentMonth === 1) {
            $this->currentMonth = 12;
            $this->currentYear--;
        } else {
            $this->currentMonth--;
        }
    }

    public function nextMonth()
    {
        if ($this->currentMonth === 12) {
            $this->currentMonth = 1;
            $this->currentYear++;
        } else {
            $this->currentMonth++;
        }
    }
}
