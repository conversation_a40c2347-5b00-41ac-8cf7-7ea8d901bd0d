# Google Forms-like Question Builder

## Overview
The Exam/Exercise resource now features a Google Forms-like question builder that allows you to create and manage questions and choices directly within the exam form, without needing separate relation managers.

## Features

### ✅ **Inline Question Builder**
- **Dynamic Question Creation**: Add questions directly in the exam form using a visual builder
- **Question Types**: Support for Multiple Choice, Short Answer, and Essay questions
- **Drag & Drop Reordering**: Questions can be reordered by dragging and dropping
- **Collapsible Interface**: Questions can be collapsed for better organization

### ✅ **Google Forms-like Choice Management**
- **Dynamic Choice Addition**: Click "Add Choice" to add new answer options
- **Drag & Drop Sorting**: Reorder choices by dragging them
- **Visual Correct Answer**: Toggle switches to mark correct answers
- **Minimum/Maximum Limits**: 2-10 choices per question
- **Auto-numbering**: Choices are automatically numbered

### ✅ **Enhanced User Experience**
- **Visual Feedback**: Hover effects and smooth transitions
- **Responsive Design**: Works on desktop and mobile devices
- **Intuitive Icons**: Clear visual indicators for different actions
- **Real-time Updates**: Changes are reflected immediately

### ✅ **Language Switcher in Header**
- **Topbar Integration**: Language switcher now appears in the main topbar
- **Easy Access**: Switch between English and Thai languages
- **Persistent Selection**: Language preference is saved to user profile

## How to Use

### Creating Questions
1. Navigate to Exams/Exercises
2. Click "Create" or edit an existing exam
3. Scroll to the "Questions" section
4. Click "+ Add Question" to create a new question
5. Fill in the question details:
   - Question text
   - Question type (Multiple Choice, Short Answer, Essay)
   - Points value
   - Difficulty level

### Managing Multiple Choice Options
1. For Multiple Choice questions, the "Answer Choices" section appears
2. Click "+ Add Choice" to add new options
3. Enter choice text in the input fields
4. Toggle the "Correct" switch for the right answer(s)
5. Drag choices to reorder them
6. Use the remove button to delete unwanted choices

### Reordering Questions
1. Use the drag handle (⋮⋮) on the left side of each question
2. Drag questions up or down to reorder them
3. Questions are automatically renumbered

### Additional Features
- **Explanation Field**: Add explanations that show after answers
- **Tags**: Add tags for categorization and filtering
- **Question Settings**: Configure time limits, randomization, etc.

## Technical Implementation

### Key Components
- **Filament Builder Component**: Provides the main question builder interface
- **Filament Repeater Component**: Handles the choice management
- **Custom CSS**: Enhances visual appearance and drag feedback
- **State Hydration**: Loads existing questions when editing
- **Data Processing**: Saves questions and choices to database

### File Structure
```
app/Filament/Resources/Exam/
├── ExamResource.php (Main form with question builder)
├── Pages/
│   ├── CreateExam.php (Handles question creation)
│   └── EditExam.php (Handles question updates)
└── RelationManagers/ (Deprecated - using inline builder)

resources/css/
└── question-builder.css (Custom styling)

app/Providers/Filament/
└── AppPanelProvider.php (Language switcher integration)
```

### Database Schema
Questions and choices are stored in their respective tables with proper relationships:
- `questions` table: Stores question data with sort_order for positioning
- `choices` table: Stores choice data with sort_order for positioning
- Foreign keys maintain relationships between exams, questions, and choices

## Benefits

1. **Improved User Experience**: More intuitive and familiar interface
2. **Faster Question Creation**: No need to navigate between different pages
3. **Better Organization**: Visual drag-and-drop makes reordering easy
4. **Reduced Complexity**: Single form handles all question management
5. **Mobile Friendly**: Responsive design works on all devices

## Future Enhancements

- Question templates for quick creation
- Bulk import from CSV/Excel
- Question bank integration
- Advanced question types (matching, ranking, etc.)
- Question preview mode
- Collaborative editing features
