<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Blog\Post;
use Illuminate\Http\Request;

class PostController extends Controller
{
    public function index()
    {
        $posts = Post::whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->with(['team', 'user'])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        return view('frontend.posts.index', compact('posts'));
    }

    public function show(Post $post)
    {
        // Check if post is published
        if (!$post->published_at || $post->published_at > now()) {
            abort(404);
        }

        // Load relationships
        $post->load(['team', 'user']);

        return view('frontend.posts.show', compact('post'));
    }
}
