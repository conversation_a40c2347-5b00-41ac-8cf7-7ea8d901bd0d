<?php

namespace App\Livewire;

use App\Services\TaskNotificationService;
use Livewire\Component;

class TaskAlerts extends Component
{
    public $alerts = [];

    public function mount()
    {
        $this->checkForAlerts();
    }

    public function checkForAlerts()
    {
        if (auth()->check()) {
            $notificationService = app(TaskNotificationService::class);
            $this->alerts = $notificationService->getPendingBrowserAlerts(auth()->user());
        }
    }

    public function dismissAlert($index)
    {
        unset($this->alerts[$index]);
        $this->alerts = array_values($this->alerts); // Re-index array
    }

    public function render()
    {
        return view('livewire.task-alerts');
    }
}
