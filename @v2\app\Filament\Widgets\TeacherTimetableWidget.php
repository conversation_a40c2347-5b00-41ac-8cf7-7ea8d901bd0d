<?php

namespace App\Filament\Widgets;

use App\Models\Task;
use App\Models\TeachingSchedule;
use Carbon\Carbon;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TeacherTimetableWidget extends Widget
{
    protected static string $view = 'filament.widgets.teacher-timetable-widget';
    
    protected int | string | array $columnSpan = 'full';
    
    protected static ?int $sort = 1;

    public function getViewData(): array
    {
        $user = Auth::user();
        $today = Carbon::today();
        $startOfWeek = $today->copy()->startOfWeek();
        $endOfWeek = $today->copy()->endOfWeek();

        // Get current week's tasks and schedules
        $tasks = Task::where('team_id', $user->team_id)
            ->whereBetween('start_datetime', [$startOfWeek, $endOfWeek])
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->with(['assignedUser'])
            ->get();

        $schedules = TeachingSchedule::where('team_id', $user->team_id)
            ->where('user_id', $user->id)
            ->whereBetween('start_time', [$startOfWeek, $endOfWeek])
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->with(['subject', 'classroom'])
            ->get();

        // Get today's incomplete tasks for the table
        $todayTasks = Task::where('team_id', $user->team_id)
            ->whereDate('start_datetime', $today)
            ->whereNotIn('status', ['completed', 'cancelled'])
            ->orderBy('start_datetime')
            ->with(['assignedUser'])
            ->limit(5) // Limit to 5 tasks for compact display
            ->get();

        // Organize data by day and time slots
        $weekData = $this->organizeWeekData($tasks, $schedules, $startOfWeek);

        return [
            'weekData' => $weekData,
            'todayTasks' => $todayTasks,
            'startOfWeek' => $startOfWeek,
            'today' => $today,
            'totalTodayTasks' => Task::where('team_id', $user->team_id)
                ->whereDate('start_datetime', $today)
                ->whereNotIn('status', ['completed', 'cancelled'])
                ->count(),
        ];
    }

    private function organizeWeekData($tasks, $schedules, $startOfWeek): array
    {
        $weekData = [];
        $timeSlots = $this->generateTimeSlots();

        // Initialize week structure
        for ($i = 0; $i < 7; $i++) {
            $date = $startOfWeek->copy()->addDays($i);
            $weekData[$date->format('Y-m-d')] = [
                'date' => $date,
                'slots' => array_fill_keys($timeSlots, [])
            ];
        }

        // Add tasks to appropriate slots
        foreach ($tasks as $task) {
            $date = $task->start_datetime->format('Y-m-d');
            $timeSlot = $this->getTimeSlot($task->start_datetime);
            
            if (isset($weekData[$date]['slots'][$timeSlot])) {
                $weekData[$date]['slots'][$timeSlot][] = [
                    'type' => 'task',
                    'id' => $task->id,
                    'title' => $task->title,
                    'start_time' => $task->start_datetime,
                    'end_time' => $task->end_datetime,
                    'location' => $task->location,
                    'priority' => $task->priority,
                    'status' => $task->status,
                    'description' => $task->description,
                    'assigned_to' => $task->assignedUser ? $task->assignedUser->name : null,
                    'notes' => $task->notes,
                    'is_all_day' => $task->is_all_day,
                    'has_alert' => $task->has_alert,
                ];
            }
        }

        // Add schedules to appropriate slots
        foreach ($schedules as $schedule) {
            $date = $schedule->start_time->format('Y-m-d');
            $timeSlot = $this->getTimeSlot($schedule->start_time);
            
            if (isset($weekData[$date]['slots'][$timeSlot])) {
                $weekData[$date]['slots'][$timeSlot][] = [
                    'type' => 'schedule',
                    'id' => $schedule->id,
                    'title' => $schedule->subject->name ?? 'Teaching',
                    'start_time' => $schedule->start_time,
                    'end_time' => $schedule->end_time,
                    'location' => $schedule->classroom->name ?? '',
                    'status' => $schedule->status,
                    'notes' => $schedule->notes,
                ];
            }
        }

        return $weekData;
    }

    private function generateTimeSlots(): array
    {
        $slots = [];
        for ($hour = 8; $hour <= 18; $hour++) {
            $slots[] = sprintf('%02d:00', $hour);
        }
        return $slots;
    }

    private function getTimeSlot($datetime): string
    {
        return $datetime->format('H:00');
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && ($user->hasRole('teacher') || $user->hasRole('school'));
    }
}
