<?php

namespace App\Traits;

use TomatoPHP\FilamentSubscriptions\Models\Subscription;

trait HasSubscriptionLimits
{
    /**
     * Get the active subscription for this team/user
     */
    public function getActiveSubscription(): ?Subscription
    {
        return $this->subscriptions()
            ->where('starts_at', '<=', now())
            ->where(function ($query) {
                $query->whereNull('ends_at')
                      ->orWhere('ends_at', '>', now());
            })
            ->where(function ($query) {
                $query->whereNull('canceled_at')
                      ->orWhere('canceled_at', '>', now());
            })
            ->first();
    }

    /**
     * Check if the team can add more students
     */
    public function canAddStudents(int $count = 1): bool
    {
        $subscription = $this->getActiveSubscription();
        
        if (!$subscription) {
            return false; // No subscription, no students allowed
        }

        $currentStudentCount = $this->users()->whereHas('roles', function ($query) {
            $query->where('name', 'student');
        })->count();

        $limit = $this->getStudentLimit($subscription->plan->slug);
        
        return ($currentStudentCount + $count) <= $limit;
    }

    /**
     * Check if the team can add more parents
     */
    public function canAddParents(int $count = 1): bool
    {
        $subscription = $this->getActiveSubscription();
        
        if (!$subscription) {
            return false; // No subscription, no parents allowed
        }

        $currentParentCount = $this->users()->whereHas('roles', function ($query) {
            $query->where('name', 'parent');
        })->count();

        $limit = $this->getParentLimit($subscription->plan->slug);
        
        return ($currentParentCount + $count) <= $limit;
    }

    /**
     * Check if the team can add more teachers
     */
    public function canAddTeachers(int $count = 1): bool
    {
        $subscription = $this->getActiveSubscription();
        
        if (!$subscription) {
            return false; // No subscription, no teachers allowed
        }

        // Most plans allow unlimited teachers, but individual plans might have limits
        if ($this->isIndividualPlan($subscription->plan->slug)) {
            return false; // Individual plans don't allow additional teachers
        }

        return true; // School plans allow unlimited teachers
    }

    /**
     * Get student limit based on plan
     */
    public function getStudentLimit(string $planSlug): int
    {
        switch ($planSlug) {
            case 'individual-student':
            case 'individual-student-annual':
                return 1; // Individual student plans
            case 'individual-teacher':
            case 'individual-teacher-annual':
                return 50; // Individual teacher plans
            case 'school-basic':
            case 'school-basic-annual':
                return 100; // Basic school plans
            case 'school-premium':
                return 500; // Premium school plans
            default:
                return 0; // No limit or unknown plan
        }
    }

    /**
     * Get parent limit based on plan (usually 1 per student)
     */
    public function getParentLimit(string $planSlug): int
    {
        return $this->getStudentLimit($planSlug); // 1 parent per student
    }

    /**
     * Check if this is an individual plan
     */
    public function isIndividualPlan(string $planSlug): bool
    {
        return str_contains($planSlug, 'individual');
    }

    /**
     * Check if this is a school plan
     */
    public function isSchoolPlan(string $planSlug): bool
    {
        return str_contains($planSlug, 'school');
    }

    /**
     * Get subscription status information
     */
    public function getSubscriptionStatus(): array
    {
        $subscription = $this->getActiveSubscription();
        
        if (!$subscription) {
            return [
                'has_subscription' => false,
                'status' => 'no_subscription',
                'message' => 'No active subscription',
            ];
        }

        $plan = $subscription->plan;
        $studentCount = $this->users()->whereHas('roles', function ($query) {
            $query->where('name', 'student');
        })->count();
        
        $parentCount = $this->users()->whereHas('roles', function ($query) {
            $query->where('name', 'parent');
        })->count();

        $studentLimit = $this->getStudentLimit($plan->slug);
        $parentLimit = $this->getParentLimit($plan->slug);

        return [
            'has_subscription' => true,
            'status' => 'active',
            'plan_name' => $plan->name,
            'plan_slug' => $plan->slug,
            'student_count' => $studentCount,
            'student_limit' => $studentLimit,
            'parent_count' => $parentCount,
            'parent_limit' => $parentLimit,
            'can_add_students' => $this->canAddStudents(),
            'can_add_parents' => $this->canAddParents(),
            'can_add_teachers' => $this->canAddTeachers(),
            'subscription_ends_at' => $subscription->ends_at,
            'subscription_canceled_at' => $subscription->canceled_at,
        ];
    }
}
