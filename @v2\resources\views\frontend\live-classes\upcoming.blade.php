@extends('frontend.layouts.app')

@section('title', 'Upcoming Live Classes')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('frontend.live-classes.index') }}" 
                       class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </a>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Upcoming Live Classes</h1>
                        <p class="mt-2 text-gray-600">Classes scheduled for the future</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('frontend.live-classes.live') }}" 
                       class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-circle mr-2 animate-pulse"></i>
                        Live Now
                    </a>
                    <a href="{{ route('frontend.live-classes.index') }}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-list mr-2"></i>
                        All Classes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        @if($upcomingClasses->count() > 0)
            <div class="space-y-6">
                @foreach($upcomingClasses as $liveVideo)
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                        <div class="p-6">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-4">
                                        <div class="flex-shrink-0">
                                            <div class="w-16 h-16 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                <i class="fas fa-clock text-2xl text-yellow-600"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <h3 class="text-xl font-semibold text-gray-900 mb-1">{{ $liveVideo->title }}</h3>
                                            <div class="flex items-center space-x-4 text-sm text-gray-600">
                                                <span>
                                                    <i class="fas fa-user mr-1"></i>
                                                    {{ $liveVideo->teacher->name }}
                                                </span>
                                                @if($liveVideo->liveable)
                                                    <span>
                                                        <i class="fas fa-book mr-1"></i>
                                                        {{ $liveVideo->liveable->title }}
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    
                                    @if($liveVideo->description)
                                        <p class="mt-3 text-gray-600 line-clamp-2">{{ $liveVideo->description }}</p>
                                    @endif
                                </div>

                                <div class="flex-shrink-0 ml-6">
                                    <div class="text-right">
                                        <div class="text-lg font-semibold text-gray-900">
                                            {{ $liveVideo->scheduled_start_time->format('M j, Y') }}
                                        </div>
                                        <div class="text-sm text-gray-600">
                                            {{ $liveVideo->scheduled_start_time->format('g:i A') }} - {{ $liveVideo->scheduled_end_time->format('g:i A') }}
                                        </div>
                                        <div class="mt-2 text-sm text-blue-600 font-medium">
                                            @php
                                                $timeUntil = $liveVideo->scheduled_start_time->diffForHumans();
                                            @endphp
                                            {{ $timeUntil }}
                                        </div>
                                    </div>
                                    
                                    <div class="mt-4 flex space-x-2">
                                        <a href="{{ route('frontend.live-classes.show', $liveVideo) }}" 
                                           class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Details
                                        </a>
                                        <button onclick="addToCalendar({{ $liveVideo->id }})" 
                                                class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                            <i class="fas fa-calendar-plus mr-1"></i>
                                            Add to Calendar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <i class="fas fa-clock text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Upcoming Classes</h3>
                <p class="text-gray-600 mb-6">There are no live classes scheduled at the moment.</p>
                <div class="flex justify-center space-x-4">
                    <a href="{{ route('frontend.live-classes.index') }}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        View All Classes
                    </a>
                    <a href="{{ route('frontend.live-classes.live') }}" 
                       class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        Check Live Classes
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection

@push('scripts')
<script>
function addToCalendar(liveVideoId) {
    // Get the live video data
    const liveVideo = @json($upcomingClasses->keyBy('id'));
    const video = liveVideo[liveVideoId];
    
    if (!video) return;
    
    // Create calendar event
    const startDate = new Date(video.scheduled_start_time);
    const endDate = new Date(video.scheduled_end_time);
    
    // Format dates for calendar
    const formatDate = (date) => {
        return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
    };
    
    const title = encodeURIComponent(video.title);
    const description = encodeURIComponent(`Live class with ${video.teacher.name}\n\nJoin at: ${window.location.origin}/live-classes/${video.id}`);
    const startTime = formatDate(startDate);
    const endTime = formatDate(endDate);
    
    // Google Calendar URL
    const googleCalendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startTime}/${endTime}&details=${description}`;
    
    // Open in new window
    window.open(googleCalendarUrl, '_blank');
}

// Auto-refresh every 5 minutes to update "time until" display
setInterval(() => {
    window.location.reload();
}, 300000);
</script>
@endpush
