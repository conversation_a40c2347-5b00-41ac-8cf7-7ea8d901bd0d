<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Auth Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status { padding: 10px; border-radius: 4px; margin: 10px 0; }
        .status.enabled { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.disabled { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .config { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .test-button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .test-button:hover { background: #0056b3; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Social Authentication Debug Panel</h1>
        
        <div class="card">
            <h2>Environment Information</h2>
            <div class="config">
                <strong>App URL:</strong> {{ $app_url }}<br>
                <strong>Current URL:</strong> {{ $current_url }}<br>
                <strong>Environment:</strong> {{ config('app.env') }}
            </div>
            
            @if($app_url !== $current_url)
                <div class="status disabled">
                    <strong>⚠️ URL Mismatch Warning:</strong> Your APP_URL ({{ $app_url }}) doesn't match the current URL ({{ $current_url }}). 
                    This may cause OAuth redirect issues.
                </div>
            @endif
        </div>

        <div class="card">
            <h2>Enabled Providers</h2>
            @if(count($enabled_providers) > 0)
                @foreach($enabled_providers as $provider => $config)
                    <div class="status enabled">
                        <strong>✅ {{ ucfirst($provider) }}</strong> - Enabled
                    </div>
                @endforeach
            @else
                <div class="status disabled">
                    <strong>❌ No providers enabled</strong>
                </div>
            @endif
        </div>

        <div class="card">
            <h2>Google OAuth Configuration</h2>
            @if(isset($enabled_providers['google']))
                <div class="status enabled">
                    <strong>✅ Google OAuth is enabled</strong>
                </div>
                <div class="config">
                    <strong>Client ID:</strong> {{ $google_config['client_id'] ?? 'Not set' }}<br>
                    <strong>Client Secret:</strong> {{ $google_config['client_secret'] ? str_repeat('*', strlen($google_config['client_secret'])) : 'Not set' }}<br>
                    <strong>Redirect URI:</strong> {{ $google_config['redirect'] ?? 'Not set' }}
                </div>
                
                <h3>Required Redirect URIs in Google Console:</h3>
                <div class="config">
                    <strong>Production:</strong> https://edutest.space/auth/google/callback<br>
                    <strong>Local Development:</strong><br>
                    • http://127.0.0.1:8000/auth/google/callback<br>
                    • http://localhost:8000/auth/google/callback
                </div>
                
                <button class="test-button" onclick="testGoogle()">🧪 Test Google OAuth</button>
            @else
                <div class="status disabled">
                    <strong>❌ Google OAuth is disabled</strong>
                </div>
            @endif
        </div>

        <div class="card">
            <h2>LINE OAuth Configuration</h2>
            @if(isset($enabled_providers['line']))
                <div class="status enabled">
                    <strong>✅ LINE OAuth is enabled</strong>
                </div>
                <div class="config">
                    <strong>Client ID:</strong> {{ $line_config['client_id'] ?? 'Not set' }}<br>
                    <strong>Client Secret:</strong> {{ $line_config['client_secret'] ? str_repeat('*', strlen($line_config['client_secret'])) : 'Not set' }}<br>
                    <strong>Redirect URI:</strong> {{ $line_config['redirect'] ?? 'Not set' }}
                </div>
                
                <h3>Required Redirect URIs in LINE Console:</h3>
                <div class="config">
                    <strong>Production:</strong> https://edutest.space/auth/line/callback<br>
                    <strong>Local Development:</strong><br>
                    • http://127.0.0.1:8000/auth/line/callback<br>
                    • http://localhost:8000/auth/line/callback
                </div>
                
                <button class="test-button" onclick="testLine()">🧪 Test LINE OAuth</button>
            @else
                <div class="status disabled">
                    <strong>❌ LINE OAuth is disabled</strong>
                </div>
            @endif
        </div>

        <div class="card">
            <h2>Quick Tests</h2>
            <button class="test-button" onclick="window.location.href='/login'">🔑 Go to Login Page</button>
            <button class="test-button" onclick="testSocialButtons()">🧪 Test Social Buttons</button>
            <button class="test-button" onclick="checkJavaScript()">📜 Check JavaScript</button>
        </div>

        <div class="card">
            <h2>Troubleshooting Steps</h2>
            <ol>
                <li><strong>Check OAuth Console Settings:</strong>
                    <ul>
                        <li>Google: <a href="https://console.developers.google.com/" target="_blank">Google Console</a></li>
                        <li>LINE: <a href="https://developers.line.biz/" target="_blank">LINE Developers</a></li>
                    </ul>
                </li>
                <li><strong>Verify Redirect URIs:</strong> Make sure the redirect URIs in your OAuth applications match the ones shown above</li>
                <li><strong>Check Environment Variables:</strong> Ensure your .env file has the correct client IDs and secrets</li>
                <li><strong>Clear Cache:</strong> Run <code>php artisan config:clear && php artisan cache:clear</code></li>
                <li><strong>Check Logs:</strong> Look at <code>storage/logs/laravel.log</code> for any error messages</li>
            </ol>
        </div>
    </div>

    <script>
        function testGoogle() {
            window.open('/test/google', '_blank');
        }
        
        function testLine() {
            window.open('/test/line', '_blank');
        }
        
        function testSocialButtons() {
            window.open('/login', '_blank');
        }
        
        function checkJavaScript() {
            console.log('Checking social login JavaScript...');
            
            // Check if login.js is loaded
            const scripts = Array.from(document.scripts);
            const loginScript = scripts.find(script => script.src.includes('login.js'));
            
            if (loginScript) {
                console.log('✅ login.js is loaded');
            } else {
                console.log('❌ login.js is not loaded');
            }
            
            alert('Check the browser console for JavaScript debug information');
        }
    </script>
</body>
</html>
