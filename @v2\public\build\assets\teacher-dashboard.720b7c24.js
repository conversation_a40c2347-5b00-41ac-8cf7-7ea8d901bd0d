document.addEventListener("DOMContentLoaded",function(){const d=document.getElementById("menu-toggle"),a=document.getElementById("mobile-menu"),u=document.getElementById("close-menu");d&&a&&d.addEventListener("click",function(){a.classList.add("active")}),u&&a&&u.addEventListener("click",function(){a.classList.remove("active")});const c=document.getElementById("mobile-tab-toggle"),r=document.getElementById("mobile-tab-menu");c&&r&&c.addEventListener("click",function(){r.classList.toggle("hidden");const e=this.querySelector(".fa-chevron-down, .fa-chevron-up");e&&(e.classList.toggle("fa-chevron-down"),e.classList.toggle("fa-chevron-up"))});const m=document.querySelectorAll(".tab-btn"),g=document.querySelectorAll(".tab-content");m.forEach(e=>{e.addEventListener("click",function(){const t=this.getAttribute("data-tab"),s=this.innerHTML;if(m.forEach(n=>{n.classList.remove("active","bg-blue-100","text-blue-600"),n.classList.add("text-gray-600","hover:bg-gray-100")}),g.forEach(n=>{n.classList.remove("active")}),this.classList.add("active","bg-blue-100","text-blue-600"),this.classList.remove("text-gray-600","hover:bg-gray-100"),c){const n=c.querySelector("span");if(n&&(n.innerHTML=s),r){r.classList.add("hidden");const i=c.querySelector(".fa-chevron-up, .fa-chevron-down");i&&(i.classList.remove("fa-chevron-up"),i.classList.add("fa-chevron-down"))}}const o=document.getElementById(t);o&&o.classList.add("active")})}),document.querySelectorAll(".course-card").forEach(e=>{e.addEventListener("click",function(){const t=this.querySelector("h3").textContent;v(t)})});function v(e){const t=document.createElement("div");t.className="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",t.style.display="flex",t.innerHTML=`
            <div class="modal-content bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">${e}</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">\u0E08\u0E33\u0E19\u0E27\u0E19\u0E19\u0E31\u0E01\u0E40\u0E23\u0E35\u0E22\u0E19</h4>
                            <p class="text-2xl font-bold text-blue-600">32 \u0E04\u0E19</p>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-medium mb-2">\u0E04\u0E27\u0E32\u0E21\u0E01\u0E49\u0E32\u0E27\u0E2B\u0E19\u0E49\u0E32</h4>
                            <p class="text-2xl font-bold text-green-600">75%</p>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-medium mb-2">\u0E1A\u0E17\u0E40\u0E23\u0E35\u0E22\u0E19\u0E25\u0E48\u0E32\u0E2A\u0E38\u0E14</h4>
                        <ul class="space-y-2">
                            <li class="flex justify-between items-center">
                                <span>\u0E1A\u0E17\u0E17\u0E35\u0E48 8: \u0E2A\u0E21\u0E01\u0E32\u0E23\u0E40\u0E0A\u0E34\u0E07\u0E40\u0E2A\u0E49\u0E19</span>
                                <span class="text-green-600">\u0E40\u0E2A\u0E23\u0E47\u0E08\u0E41\u0E25\u0E49\u0E27</span>
                            </li>
                            <li class="flex justify-between items-center">
                                <span>\u0E1A\u0E17\u0E17\u0E35\u0E48 9: \u0E23\u0E30\u0E1A\u0E1A\u0E2A\u0E21\u0E01\u0E32\u0E23</span>
                                <span class="text-yellow-600">\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E2D\u0E19</span>
                            </li>
                            <li class="flex justify-between items-center">
                                <span>\u0E1A\u0E17\u0E17\u0E35\u0E48 10: \u0E2D\u0E2A\u0E21\u0E01\u0E32\u0E23</span>
                                <span class="text-gray-400">\u0E22\u0E31\u0E07\u0E44\u0E21\u0E48\u0E40\u0E23\u0E34\u0E48\u0E21</span>
                            </li>
                        </ul>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">\u0E14\u0E39\u0E23\u0E32\u0E22\u0E25\u0E30\u0E40\u0E2D\u0E35\u0E22\u0E14</button>
                        <button class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E1A\u0E17\u0E40\u0E23\u0E35\u0E22\u0E19</button>
                        <button class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E41\u0E1A\u0E1A\u0E17\u0E14\u0E2A\u0E2D\u0E1A</button>
                    </div>
                </div>
            </div>
        `,document.body.appendChild(t),t.querySelector(".close-modal").addEventListener("click",function(){document.body.removeChild(t)}),t.addEventListener("click",function(o){o.target===t&&document.body.removeChild(t)})}document.querySelectorAll(".quick-access-btn").forEach(e=>{e.addEventListener("click",function(){const t=this.textContent.trim();l(`${t} - \u0E1F\u0E35\u0E40\u0E08\u0E2D\u0E23\u0E4C\u0E19\u0E35\u0E49\u0E08\u0E30\u0E40\u0E1B\u0E34\u0E14\u0E43\u0E0A\u0E49\u0E07\u0E32\u0E19\u0E40\u0E23\u0E47\u0E27\u0E46 \u0E19\u0E35\u0E49`,"info")})}),document.querySelectorAll(".add-homework-btn").forEach(e=>{e.addEventListener("click",function(t){t.stopPropagation(),h()})});function h(){const e=document.createElement("div");e.className="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",e.style.display="flex",e.innerHTML=`
            <div class="modal-content bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold">\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E01\u0E32\u0E23\u0E1A\u0E49\u0E32\u0E19</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">\u0E2B\u0E31\u0E27\u0E02\u0E49\u0E2D\u0E01\u0E32\u0E23\u0E1A\u0E49\u0E32\u0E19</label>
                        <input type="text" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" placeholder="\u0E40\u0E0A\u0E48\u0E19 \u0E41\u0E1A\u0E1A\u0E1D\u0E36\u0E01\u0E2B\u0E31\u0E14\u0E1A\u0E17\u0E17\u0E35\u0E48 5">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">\u0E23\u0E32\u0E22\u0E25\u0E30\u0E40\u0E2D\u0E35\u0E22\u0E14</label>
                        <textarea class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50" rows="3" placeholder="\u0E23\u0E32\u0E22\u0E25\u0E30\u0E40\u0E2D\u0E35\u0E22\u0E14\u0E01\u0E32\u0E23\u0E1A\u0E49\u0E32\u0E19..."></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">\u0E01\u0E33\u0E2B\u0E19\u0E14\u0E2A\u0E48\u0E07</label>
                        <input type="date" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50">
                    </div>
                    <div class="flex space-x-2">
                        <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex-1">\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E01\u0E32\u0E23\u0E1A\u0E49\u0E32\u0E19</button>
                        <button type="button" class="close-modal bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400">\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01</button>
                    </div>
                </form>
            </div>
        `,document.body.appendChild(e),e.querySelectorAll(".close-modal").forEach(o=>{o.addEventListener("click",function(){document.body.removeChild(e)})}),e.addEventListener("click",function(o){o.target===e&&document.body.removeChild(e)}),e.querySelector("form").addEventListener("submit",function(o){o.preventDefault(),l("\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E01\u0E32\u0E23\u0E1A\u0E49\u0E32\u0E19\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27","success"),document.body.removeChild(e)})}const f=document.querySelector(".calendar-prev"),b=document.querySelector(".calendar-next");f&&f.addEventListener("click",function(){l("\u0E40\u0E1B\u0E25\u0E35\u0E48\u0E22\u0E19\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32","info")}),b&&b.addEventListener("click",function(){l("\u0E40\u0E1B\u0E25\u0E35\u0E48\u0E22\u0E19\u0E40\u0E14\u0E37\u0E2D\u0E19\u0E16\u0E31\u0E14\u0E44\u0E1B","info")}),document.querySelectorAll(".calendar-event").forEach(e=>{e.addEventListener("click",function(t){t.stopPropagation();const s=this.textContent;l(`\u0E04\u0E25\u0E34\u0E01\u0E17\u0E35\u0E48\u0E01\u0E34\u0E08\u0E01\u0E23\u0E23\u0E21: ${s}`,"info")})});function l(e,t="info"){const s=document.createElement("div");switch(s.className="fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full",t){case"success":s.classList.add("bg-green-500","text-white");break;case"error":s.classList.add("bg-red-500","text-white");break;case"warning":s.classList.add("bg-yellow-500","text-white");break;default:s.classList.add("bg-blue-500","text-white")}s.innerHTML=`
            <div class="flex items-center">
                <span>${e}</span>
                <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `,document.body.appendChild(s),setTimeout(()=>{s.classList.remove("translate-x-full")},100),setTimeout(()=>{s.classList.add("translate-x-full"),setTimeout(()=>{s.parentElement&&s.remove()},300)},5e3)}document.querySelectorAll(".circle-progress-value").forEach(e=>{const t=e.getAttribute("stroke-dasharray");if(t){const[s,o]=t.split(" ").map(Number);setTimeout(()=>{e.style.strokeDasharray=`${s} ${o}`},500)}}),document.querySelectorAll(".bg-white").forEach((e,t)=>{e.classList.add("fade-in"),e.style.animationDelay=`${t*.1}s`}),document.querySelectorAll("form").forEach(e=>{e.addEventListener("submit",function(t){t.preventDefault();const s=e.querySelectorAll("[required]");let o=!0;s.forEach(n=>{n.value.trim()?n.classList.remove("border-red-500"):(o=!1,n.classList.add("border-red-500"))}),o?l("\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E40\u0E23\u0E35\u0E22\u0E1A\u0E23\u0E49\u0E2D\u0E22\u0E41\u0E25\u0E49\u0E27","success"):l("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E43\u0E2B\u0E49\u0E04\u0E23\u0E1A\u0E16\u0E49\u0E27\u0E19","error")})}),document.querySelectorAll('input[type="search"]').forEach(e=>{e.addEventListener("input",function(){const t=this.value.toLowerCase();console.log("Searching for:",t)})})});
