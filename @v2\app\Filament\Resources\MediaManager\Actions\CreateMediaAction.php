<?php

namespace App\Filament\Resources\MediaManager\Actions;

use App\Models\MediaManager\Folder;
use App\Traits\MediaManagerHelpers;
use Filament\Actions;
use Filament\Forms;
use Filament\Notifications\Notification;

class CreateMediaAction
{
    use MediaManagerHelpers;
    public static function make(int $folder_id): Actions\Action
    {
        return Actions\Action::make('create_media')
            ->mountUsing(function () use ($folder_id){
                session()->put('folder_id', $folder_id);
            })
            ->label(trans('filament-media-manager::messages.media.actions.create.label'))
            ->icon('heroicon-o-plus')
            ->form([
                Forms\Components\FileUpload::make('file')
                    ->label(trans('filament-media-manager::messages.media.actions.create.form.file'))
                    ->maxSize('100000')
                    ->columnSpanFull()
                    ->required()
                    ->storeFiles(false),
                Forms\Components\TextInput::make('title')
                    ->label(trans('filament-media-manager::messages.media.actions.create.form.title'))
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('description')
                    ->label(trans('filament-media-manager::messages.media.actions.create.form.description'))
                    ->columnSpanFull(),
            ])
            ->action(function (array $data) use ($folder_id) {
                $folder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->find($folder_id);
                $user = auth()->user();
                
                if ($folder && $user) {
                    $teamId = self::determineTeamId($user, $folder);
                    $customProperties = [
                        'title' => $data['title'] ?? '',
                        'description' => $data['description'] ?? '',
                        'created_by_user' => $user->name,
                        'team_name' => self::getTeamName($teamId),
                        'team_id' => $teamId, // Add team_id for path generation
                        'parent_folder_id' => $folder->id, // Add parent folder reference
                        'parent_folder_name' => $folder->name,
                    ];

                    if ($folder->model) {
                        $media = $folder->model->addMedia($data['file'])
                            ->withCustomProperties($customProperties)
                            ->toMediaCollection($folder->collection);
                    } else {
                        $media = $folder->addMedia($data['file'])
                            ->withCustomProperties($customProperties)
                            ->toMediaCollection($folder->collection);
                    }

                    // Update the media record with our custom fields
                    if ($media) {
                        $media->update([
                            'created_by' => $user->id,
                            'user_id' => $user->id,
                            'team_id' => $teamId,
                        ]);
                    }
                }

                Notification::make()
                    ->title(trans('filament-media-manager::messages.media.notifications.create-media'))
                    ->success()
                    ->send();
            });
    }
}
