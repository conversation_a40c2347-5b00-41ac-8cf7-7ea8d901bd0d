<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Live Streaming API Routes
use App\Http\Controllers\LiveStreamController;

Route::middleware(['auth:sanctum'])->group(function () {
    // Update stream status
    Route::post('/live-videos/{liveVideo}/status', [LiveStreamController::class, 'updateStatus']);

    // Upload recording
    Route::post('/live-videos/upload-recording', [LiveStreamController::class, 'uploadRecording']);

    // Get viewer count
    Route::get('/live-videos/{liveVideo}/viewer-count', [LiveStreamController::class, 'getViewerCount']);
});
