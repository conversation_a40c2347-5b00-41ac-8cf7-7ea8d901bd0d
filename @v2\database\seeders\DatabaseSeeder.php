<?php

namespace Database\Seeders;

use App\Models\Assignment;
use App\Models\AssignmentSubmission;
use App\Models\Book;
use App\Models\ClassRoom;
use App\Models\Course;
use App\Models\Lesson;
use App\Models\Subject;
use App\Models\Task;
use App\Models\TeachingSchedule;
use App\Models\Team;
use App\Models\User;
use App\Models\Address;
use App\Models\Blog\Author;
use App\Models\Blog\Category as BlogCategory;
use App\Models\Blog\Link;
use App\Models\Blog\Post;
use App\Models\Comment;
use App\Models\Shop\Brand;
use App\Models\Shop\Category as ShopCategory;
use App\Models\Shop\Customer;
use App\Models\Shop\Order;
use App\Models\Shop\OrderItem;
use App\Models\Shop\Payment;
use App\Models\Shop\Product;
use App\Models\Exam\Exam;
use App\Models\Exam\Question;
use App\Models\Exam\Choice;
use App\Models\Exam\Attempt;
use App\Models\Exam\Answer;
use Closure;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Console\Helper\ProgressBar;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        DB::raw('SET time_zone=\'+07:00\'');

        // Clear images
        Storage::deleteDirectory('public');

        // User Roles and Permissions (for authentication system)
        $this->command->warn(PHP_EOL . 'Creating user roles and permissions...');
        $this->call(RoleSeeder::class);
        $this->command->info('User roles and permissions created.');

        // Tenant Roles and Permissions
        $this->command->warn(PHP_EOL . 'Creating tenant roles and permissions...');
        $this->call(TenantRolesSeeder::class);
        $this->command->info('Tenant roles and permissions created.');

        // Create 2 Teams (Schools)
        $this->command->warn(PHP_EOL . 'Creating teams (schools)...');
        $teams = $this->createTeams();
        $this->command->info('Teams created.');

        // Create Super Admin (global access)
        $this->command->warn(PHP_EOL . 'Creating super admin user...');
        $superAdmin = $this->createSuperAdmin();
        $this->command->info('Super admin user created.');

        // Create users and sample data for each team
        foreach ($teams as $team) {
            $this->command->warn(PHP_EOL . "Creating users and sample data for {$team->name}...");

            // Create users for each role in this team
            $users = $this->createUsersForTeam($team);

            // Create academic resources for this team
            $this->createAcademicResourcesForTeam($team, $users);

            // Create shop data for this team
            $this->createShopDataForTeam($team);

            // Create blog data for this team
            $this->createBlogDataForTeam($team);

            // Create exercises and questions for this team
            $this->createExercisesForTeam($team, $users);

            $this->command->info("Sample data for {$team->name} created.");
        }

        // Post Types (Blog, News, Knowledge)
        $this->command->warn(PHP_EOL . 'Creating post types data for all teams...');
        $this->call(PostTypeSeeder::class);
        $this->command->info('Post types data created.');

        // Media Manager
        $this->command->warn(PHP_EOL . 'Creating media manager folders and media...');
        $this->call(MediaSeeder::class);
        $this->command->info('Media manager data created.');

        // Additional Questions and Choices (using new seeder)
        $this->command->warn(PHP_EOL . 'Creating additional questions and choices...');
        $this->call(QuestionSeeder::class);
        $this->command->info('Additional questions and choices created.');

        // Live Videos
        $this->command->warn(PHP_EOL . 'Creating live videos...');
        $this->call(LiveVideoSeeder::class);
        $this->command->info('Live videos created.');

        // Subscription Plans
        $this->command->warn(PHP_EOL . 'Creating subscription plans...');
        $this->call(SubscriptionPlansSeeder::class);
        $this->command->info('Subscription plans created.');
    }

    private function createTeams(): SupportCollection
    {
        $teams = collect();

        // Create 2 teams (schools)
        $teamData = [
            [
                'name' => 'Greenwood Elementary School',
                'slug' => 'greenwood-elementary',
                'description' => 'A progressive elementary school focused on holistic education',
                'is_active' => true,
            ],
            [
                'name' => 'Riverside High School',
                'slug' => 'riverside-high',
                'description' => 'A comprehensive high school preparing students for college and careers',
                'is_active' => true,
            ]
        ];

        foreach ($teamData as $data) {
            $team = Team::create($data);
            $teams->push($team);
        }

        return $teams;
    }

    private function createSuperAdmin(): User
    {
        $superAdmin = User::factory()->create([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'team_id' => null, // Super admins have team_id = null for global access
        ]);

        $superAdmin->assignRole('super_admin');
        return $superAdmin;
    }

    private function createUsersForTeam(Team $team): array
    {
        $users = [];

        // School Administrator
        $schoolAdmin = User::factory()->create([
            'name' => "School Admin - {$team->name}",
            'email' => "school@{$team->slug}.edu",
            'team_id' => $team->id,
        ]);
        $schoolAdmin->assignRole('school');
        $users['school'] = $schoolAdmin;

        // Teacher
        $teacher = User::factory()->create([
            'name' => "Teacher - {$team->name}",
            'email' => "teacher@{$team->slug}.edu",
            'team_id' => $team->id,
        ]);
        $teacher->assignRole('teacher');
        $users['teacher'] = $teacher;

        // Parent
        $parent = User::factory()->create([
            'name' => "Parent - {$team->name}",
            'email' => "parent@{$team->slug}.edu",
            'team_id' => $team->id,
        ]);
        $parent->assignRole('parent');
        $users['parent'] = $parent;

        // Student
        $student = User::factory()->create([
            'name' => "Student - {$team->name}",
            'email' => "student@{$team->slug}.edu",
            'team_id' => $team->id,
        ]);
        $student->assignRole('student');
        $users['student'] = $student;

        return $users;
    }

    private function createAcademicResourcesForTeam(Team $team, array $users): void
    {
        // Create Subjects
        $subjects = $this->createSubjects($team);

        // Create ClassRooms
        $classrooms = $this->createClassRooms($team);

        // Create Books and Courses with Lessons
        $books = $this->createBooks($team, $subjects);
        $courses = $this->createCourses($team, $subjects);

        // Create Lessons for Books and Courses
        $this->createLessons($books, $courses);

        // Create Assignments and Submissions
        $assignments = $this->createAssignments($team, $subjects, $users['teacher']);
        $this->createAssignmentSubmissions($assignments, $users['student']);

        // Create Tasks
        $this->createTasks($team, $users);

        // Create Teaching Schedules
        $this->createTeachingSchedules($team, $users['teacher'], $classrooms, $subjects);
    }

    private function createSubjects(Team $team): SupportCollection
    {
        $subjectData = [
            ['name' => 'Mathematics', 'code' => 'MATH', 'color' => '#3B82F6', 'credits' => 4, 'level' => 'high'],
            ['name' => 'English Language Arts', 'code' => 'ELA', 'color' => '#10B981', 'credits' => 4, 'level' => 'high'],
            ['name' => 'Science', 'code' => 'SCI', 'color' => '#F59E0B', 'credits' => 3, 'level' => 'high'],
        ];

        $subjects = collect();
        foreach ($subjectData as $index => $data) {
            $subject = Subject::create([
                'team_id' => $team->id,
                'name' => $data['name'],
                'code' => $data['code'],
                'description' => "Comprehensive {$data['name']} curriculum",
                'color' => $data['color'],
                'credits' => $data['credits'],
                'level' => $data['level'],
                'grade_levels' => ['9', '10', '11', '12'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
            $subjects->push($subject);
        }

        return $subjects;
    }

    private function createClassRooms(Team $team): SupportCollection
    {
        $classroomData = [
            ['grade_level' => '9', 'room_number' => '101', 'room_name' => 'Freshman Classroom A', 'capacity' => 30],
            ['grade_level' => '10', 'room_number' => '201', 'room_name' => 'Sophomore Classroom B', 'capacity' => 28],
            ['grade_level' => '11', 'room_number' => '301', 'room_name' => 'Junior Classroom C', 'capacity' => 25],
        ];

        $classrooms = collect();
        foreach ($classroomData as $data) {
            $classroom = ClassRoom::create([
                'team_id' => $team->id,
                'grade_level' => $data['grade_level'],
                'room_number' => $data['room_number'],
                'room_name' => $data['room_name'],
                'remark' => "Grade {$data['grade_level']} classroom",
                'is_active' => true,
                'capacity' => $data['capacity'],
            ]);
            $classrooms->push($classroom);
        }

        return $classrooms;
    }

    private function createBooks(Team $team, SupportCollection $subjects): SupportCollection
    {
        $books = collect();

        foreach ($subjects as $subject) {
            for ($i = 1; $i <= 2; $i++) {
                $book = Book::create([
                    'team_id' => $team->id,
                    'subject_id' => $subject->id,
                    'title' => "{$subject->name} Textbook Volume {$i}",
                    'author' => 'Academic Publishers',
                    'description' => "Comprehensive {$subject->name} textbook covering essential topics",
                    'isbn' => '978-' . str_pad(rand(1000000000, 9999999999), 10, '0', STR_PAD_LEFT),
                    'publisher' => 'Educational Press',
                    'published_date' => now()->subYears(rand(1, 3)),
                    'edition' => rand(1, 5),
                    'pages' => rand(200, 500),
                    'language' => 'English',
                    'is_active' => true,
                ]);
                $books->push($book);
            }
        }

        return $books;
    }

    private function createCourses(Team $team, SupportCollection $subjects): SupportCollection
    {
        $courses = collect();

        foreach ($subjects as $subject) {
            $course = Course::create([
                'team_id' => $team->id,
                'subject_id' => $subject->id,
                'title' => "Advanced {$subject->name} Course",
                'description' => "Comprehensive course covering advanced topics in {$subject->name}",
                'type' => rand(0, 1) ? 'online' : 'onsite',
                'price' => rand(100, 500),
                'currency' => 'USD',
                'duration_hours' => rand(40, 120),
                'difficulty_level' => 'intermediate',
                'max_students' => rand(20, 30),
                'start_date' => now()->addDays(rand(7, 30)),
                'end_date' => now()->addDays(rand(60, 120)),
                'instructor' => 'Professional Instructor',
                'requirements' => 'Basic knowledge of the subject',
                'objectives' => "Master advanced concepts in {$subject->name}",
                'is_active' => true,
                'is_featured' => rand(0, 1),
                'sort_order' => $subject->sort_order,
            ]);
            $courses->push($course);
        }

        return $courses;
    }

    private function createLessons(SupportCollection $books, SupportCollection $courses): void
    {
        // Create lessons for books
        foreach ($books as $book) {
            for ($i = 1; $i <= 3; $i++) {
                Lesson::create([
                    'lessonable_id' => $book->id,
                    'lessonable_type' => Book::class,
                    'title' => "Chapter {$i}: Introduction to {$book->subject->name}",
                    'chapter' => "Chapter {$i}",
                    'content' => "Detailed content for chapter {$i} covering fundamental concepts.",
                    'objectives' => "Students will understand key concepts in chapter {$i}",
                    'summary' => "Summary of chapter {$i} main points",
                    'duration_minutes' => rand(45, 90),
                    'sort_order' => $i,
                    'is_published' => true,
                ]);
            }
        }

        // Create lessons for courses
        foreach ($courses as $course) {
            for ($i = 1; $i <= 2; $i++) {
                Lesson::create([
                    'lessonable_id' => $course->id,
                    'lessonable_type' => Course::class,
                    'title' => "Module {$i}: Advanced {$course->subject->name}",
                    'chapter' => "Module {$i}",
                    'content' => "Advanced content for module {$i} with practical applications.",
                    'objectives' => "Students will master advanced techniques in module {$i}",
                    'summary' => "Summary of module {$i} key learnings",
                    'duration_minutes' => rand(60, 120),
                    'sort_order' => $i,
                    'is_published' => true,
                ]);
            }
        }
    }

    private function createAssignments(Team $team, SupportCollection $subjects, User $teacher): SupportCollection
    {
        $assignments = collect();

        foreach ($subjects as $subject) {
            // Get a lesson for this subject
            $lesson = Lesson::whereHas('lessonable', function ($query) use ($subject) {
                $query->where('subject_id', $subject->id);
            })->first();

            for ($i = 1; $i <= 2; $i++) {
                $assignment = Assignment::create([
                    'team_id' => $team->id,
                    'subject_id' => $subject->id,
                    'lesson_id' => $lesson?->id,
                    'user_id' => $teacher->id,
                    'title' => "{$subject->name} Assignment {$i}",
                    'type' => rand(0, 1) ? 'homework' : 'quiz',
                    'total_score' => rand(50, 100),
                    'due_date' => now()->addDays(rand(7, 14)),
                    'description' => "Assignment {$i} for {$subject->name} covering key concepts",
                    'instructions' => "Complete all questions and submit before the due date",
                    'is_active' => true,
                ]);

                // Attach some exams to assignments
                $availableExams = Exam::where('team_id', $team->id)
                    ->where('subject_id', $subject->id)
                    ->get();

                if ($availableExams->count() > 0) {
                    // Attach 1-2 random exams to each assignment
                    $examCount = min(rand(1, 2), $availableExams->count());
                    $selectedExams = $availableExams->random($examCount);

                    foreach ($selectedExams as $index => $exam) {
                        $assignment->exams()->attach($exam->id, [
                            'sort_order' => $index + 1,
                            'team_id' => $team->id,
                        ]);
                    }
                }

                $assignments->push($assignment);
            }
        }

        return $assignments;
    }

    private function createAssignmentSubmissions(SupportCollection $assignments, User $student): void
    {
        foreach ($assignments as $assignment) {
            AssignmentSubmission::create([
                'team_id' => $assignment->team_id,
                'assignment_id' => $assignment->id,
                'user_id' => $student->id,
                'score' => rand(70, 95),
                'submitted_at' => now()->subDays(rand(1, 5)),
                'status' => 'graded',
                'content' => 'Student submission content for the assignment',
                'feedback' => 'Good work! Keep up the excellent effort.',
                'graded_at' => now()->subDays(rand(0, 3)),
                'graded_by' => $assignment->user_id,
            ]);
        }
    }

    private function createTasks(Team $team, array $users): void
    {
        $taskData = [
            [
                'user' => $users['teacher'],
                'title' => 'Prepare lesson plan',
                'description' => 'Create detailed lesson plan for next week',
                'priority' => 'high',
                'status' => 'pending',
            ],
            [
                'user' => $users['teacher'],
                'title' => 'Grade assignments',
                'description' => 'Review and grade student assignments',
                'priority' => 'medium',
                'status' => 'in_progress',
            ],
            [
                'user' => $users['student'],
                'title' => 'Complete homework',
                'description' => 'Finish math homework exercises',
                'priority' => 'high',
                'status' => 'pending',
            ],
        ];

        foreach ($taskData as $data) {
            Task::create([
                'team_id' => $team->id,
                'user_id' => $data['user']->id,
                'assigned_to' => $data['user']->id,
                'title' => $data['title'],
                'description' => $data['description'],
                'start_datetime' => now()->addHours(rand(1, 24)),
                'end_datetime' => now()->addHours(rand(25, 48)),
                'priority' => $data['priority'],
                'status' => $data['status'],
                'color' => '#3B82F6',
                'has_alert' => true,
                'alert_datetime' => now()->addHours(rand(1, 12)),
                'alert_minutes_before' => 30,
                'is_recurring' => false,
                'alert_sound' => true,
                'alert_email' => true,
                'alert_line' => false,
                'is_all_day' => false,
                'location' => 'School Campus',
                'notes' => 'Important task to complete',
            ]);
        }
    }

    private function createTeachingSchedules(Team $team, User $teacher, SupportCollection $classrooms, SupportCollection $subjects): void
    {
        $scheduleData = [
            [
                'subject' => $subjects->first(),
                'classroom' => $classrooms->first(),
                'start_time' => now()->setTime(9, 0, 0),
                'end_time' => now()->setTime(10, 30, 0),
                'notes' => 'Morning mathematics class',
            ],
            [
                'subject' => $subjects->skip(1)->first(),
                'classroom' => $classrooms->skip(1)->first(),
                'start_time' => now()->setTime(11, 0, 0),
                'end_time' => now()->setTime(12, 30, 0),
                'notes' => 'English language arts session',
            ],
            [
                'subject' => $subjects->last(),
                'classroom' => $classrooms->last(),
                'start_time' => now()->setTime(14, 0, 0),
                'end_time' => now()->setTime(15, 30, 0),
                'notes' => 'Afternoon science lab',
            ],
        ];

        foreach ($scheduleData as $data) {
            // Get a lesson for this subject
            $lesson = Lesson::whereHas('lessonable', function ($query) use ($data) {
                $query->where('subject_id', $data['subject']->id);
            })->first();

            TeachingSchedule::create([
                'user_id' => $teacher->id,
                'classroom_id' => $data['classroom']->id,
                'subject_id' => $data['subject']->id,
                'lesson_id' => $lesson?->id,
                'team_id' => $team->id,
                'start_time' => $data['start_time'],
                'end_time' => $data['end_time'],
                'notes' => $data['notes'],
                'status' => 'scheduled',
                'is_recurring' => true,
                'recurring_pattern' => [
                    'frequency' => 'weekly',
                    'interval' => 1,
                    'days' => ['monday', 'wednesday', 'friday'],
                    'start_date' => now()->format('Y-m-d'),
                    'end_date' => now()->addMonths(3)->format('Y-m-d'),
                ],
            ]);
        }
    }

    private function createShopDataForTeam(Team $team): void
    {
        // Create Brands
        $brands = collect();
        for ($i = 1; $i <= 3; $i++) {
            $brand = Brand::create([
                'team_id' => $team->id,
                'name' => "Brand {$i} - {$team->name}",
                'slug' => "brand-{$i}-" . $team->slug,
                'website' => "https://brand{$i}.{$team->slug}.com",
                'description' => "Premium brand {$i} for {$team->name}",
                'is_visible' => true,
            ]);
            $brands->push($brand);
        }

        // Create Categories
        $categories = collect();
        $categoryNames = ['Electronics', 'Books', 'Clothing'];
        foreach ($categoryNames as $index => $name) {
            $category = ShopCategory::create([
                'team_id' => $team->id,
                'name' => $name,
                'slug' => strtolower($name) . '-' . $team->slug,
                'description' => "{$name} category for {$team->name}",
                'is_visible' => true,
            ]);
            $categories->push($category);
        }

        // Create Customers
        $customers = collect();
        for ($i = 1; $i <= 5; $i++) {
            $customer = Customer::create([
                'team_id' => $team->id,
                'name' => "Customer {$i} - {$team->name}",
                'email' => "customer{$i}@{$team->slug}.com",
                'phone' => "+1234567890{$i}",
                'birthday' => now()->subYears(rand(20, 60)),
                'gender' => rand(0, 1) ? 'male' : 'female',
            ]);
            $customers->push($customer);
        }

        // Create Products
        $products = collect();
        $productCounter = 1;
        foreach ($categories as $category) {
            for ($i = 1; $i <= 2; $i++) {
                $product = Product::create([
                    'team_id' => $team->id,
                    'shop_brand_id' => $brands->random()->id,
                    'name' => "{$category->name} Product {$i}",
                    'slug' => strtolower($category->name) . "-product-{$i}-" . $team->slug,
                    'sku' => strtoupper($team->slug) . "-{$category->name}-{$i}",
                    'barcode' => $team->id . '23456789' . str_pad($productCounter, 4, '0', STR_PAD_LEFT),
                    'description' => "High-quality {$category->name} product {$i} for {$team->name}",
                    'qty' => rand(10, 100),
                    'security_stock' => rand(5, 20),
                    'featured' => rand(0, 1),
                    'is_visible' => true,
                    'old_price' => rand(50, 200),
                    'price' => rand(30, 150),
                    'cost' => rand(20, 100),
                    'product_type' => 'physical',
                    'published_at' => now(),
                ]);
                $products->push($product);

                // Attach categories
                $product->categories()->attach($category->id);

                $productCounter++;
            }
        }

        // Create Orders
        for ($i = 1; $i <= 3; $i++) {
            $order = Order::create([
                'team_id' => $team->id,
                'shop_customer_id' => $customers->random()->id,
                'number' => 'ORD-' . strtoupper($team->slug) . '-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'currency' => 'usd',
                'total_price' => rand(100, 500),
                'status' => ['new', 'processing', 'shipped', 'delivered'][rand(0, 3)],
                'shipping_price' => rand(10, 50),
                'shipping_method' => 'standard',
                'notes' => "Order {$i} for {$team->name}",
            ]);

            // Create Order Items
            for ($j = 1; $j <= rand(1, 3); $j++) {
                OrderItem::create([
                    'team_id' => $team->id,
                    'shop_order_id' => $order->id,
                    'shop_product_id' => $products->random()->id,
                    'qty' => rand(1, 5),
                    'unit_price' => rand(20, 100),
                ]);
            }

            // Create Payment
            Payment::create([
                'team_id' => $team->id,
                'order_id' => $order->id,
                'reference' => 'PAY-' . strtoupper($team->slug) . '-' . str_pad($i, 4, '0', STR_PAD_LEFT),
                'currency' => 'usd',
                'amount' => $order->total_price,
                'provider' => 'stripe',
                'method' => 'credit_card',
            ]);
        }
    }

    private function createBlogDataForTeam(Team $team): void
    {
        // Create Blog Categories
        $blogCategories = collect();
        $categoryNames = ['Education', 'Technology', 'News'];
        foreach ($categoryNames as $name) {
            $category = BlogCategory::create([
                'team_id' => $team->id,
                'name' => $name,
                'slug' => strtolower($name) . '-' . $team->slug,
                'description' => "{$name} category for {$team->name} blog",
                'is_visible' => true,
            ]);
            $blogCategories->push($category);
        }

        // Create Blog Authors
        for ($i = 1; $i <= 2; $i++) {
            $author = Author::create([
                'team_id' => $team->id,
                'name' => "Author {$i} - {$team->name}",
                'email' => "author{$i}@{$team->slug}.com",
                'bio' => "Experienced author {$i} writing for {$team->name}",
                'github_handle' => "author{$i}_{$team->slug}",
                'twitter_handle' => "author{$i}_{$team->slug}",
            ]);

            // Create Posts for each author
            for ($j = 1; $j <= 2; $j++) {
                $post = Post::create([
                    'team_id' => $team->id,
                    'blog_author_id' => $author->id,
                    'blog_category_id' => $blogCategories->random()->id,
                    'title' => "Blog Post {$j} by {$author->name}",
                    'slug' => "blog-post-{$j}-author-{$i}-" . $team->slug,
                    'content' => "This is the content of blog post {$j} written by {$author->name} for {$team->name}. It contains valuable insights and information.",
                    'published_at' => now()->subDays(rand(1, 30)),
                ]);

                // Create Comments for posts
                for ($k = 1; $k <= rand(1, 3); $k++) {
                    Comment::create([
                        'team_id' => $team->id,
                        'commentable_id' => $post->id,
                        'commentable_type' => Post::class,
                        'customer_id' => null, // We'll leave this null for now
                        'title' => "Comment {$k} on {$post->title}",
                        'content' => "This is comment {$k} on the blog post.",
                        'is_visible' => true,
                    ]);
                }
            }
        }

        // Create Blog Links
        for ($i = 1; $i <= 3; $i++) {
            Link::create([
                'team_id' => $team->id,
                'url' => "https://example{$i}.com",
                'title' => [
                    'en' => "Useful Link {$i} for {$team->name}",
                ],
                'description' => [
                    'en' => "Description for useful link {$i}",
                ],
                'color' => ['#3B82F6', '#10B981', '#F59E0B'][rand(0, 2)],
            ]);
        }
    }

    private function createExercisesForTeam(Team $team, array $users): void
    {
        $subjects = Subject::where('team_id', $team->id)->get();
        $teacher = $users['teacher'];
        $student = $users['student'];

        foreach ($subjects as $subject) {
            // Create 2 exercises per subject
            for ($i = 1; $i <= 2; $i++) {
                $exam = Exam::create([
                    'team_id' => $team->id,
                    'title' => "{$subject->name} Exercise {$i}",
                    'description' => "Practice exercise {$i} for {$subject->name}",
                    'user_id' => $teacher->id,
                    'subject_id' => $subject->id,
                    'grade_level' => $i === 1 ? 'Grade 1' : 'Grade 2',
                    'type' => 'exercise',
                    'visibility' => $i === 1 ? 'public' : 'school',
                    'total_questions' => 5,
                    'total_score' => 100,
                    'time_limit' => 30,
                    'randomize_questions' => true,
                    'randomize_choices' => true,
                    'show_results_immediately' => true,
                    'allow_multiple_attempts' => true,
                    'is_active' => true,
                ]);

                // Create questions for this exercise
                $this->createQuestionsForExam($exam, $subject, $teacher);

                // Create a sample student attempt
                $this->createSampleAttempt($exam, $student);
            }

            // Create 1 exam per subject
            $exam = Exam::create([
                'team_id' => $team->id,
                'title' => "{$subject->name} Final Exam",
                'description' => "Final examination for {$subject->name}",
                'user_id' => $teacher->id,
                'subject_id' => $subject->id,
                'grade_level' => 'Grade 3',
                'type' => 'exam',
                'visibility' => 'private',
                'total_questions' => 10,
                'total_score' => 100,
                'time_limit' => 60,
                'randomize_questions' => true,
                'randomize_choices' => true,
                'show_results_immediately' => false,
                'allow_multiple_attempts' => false,
                'available_from' => now()->addDays(7),
                'available_until' => now()->addDays(14),
                'is_active' => true,
            ]);

            // Create questions for this exam
            $this->createQuestionsForExam($exam, $subject, $teacher);
        }
    }

    private function createQuestionsForExam(Exam $exam, Subject $subject, User $teacher): void
    {
        // Create multiple choice questions
        for ($i = 1; $i <= 3; $i++) {
            $question = Question::create([
                'team_id' => $exam->team_id,
                'question_text' => "What is the correct answer for {$subject->name} question {$i}?",
                'question_type' => 'multiple_choice',
                'user_id' => $teacher->id,
                'subject_id' => $subject->id,
                'points' => 20,
                'explanation' => "This is the explanation for question {$i}",
                'difficulty_level' => ['easy', 'medium', 'hard'][rand(0, 2)],
                'is_active' => true,
            ]);

            // Create choices for multiple choice question
            $choices = [
                ['text' => 'Option A - Correct Answer', 'is_correct' => true],
                ['text' => 'Option B - Wrong Answer', 'is_correct' => false],
                ['text' => 'Option C - Wrong Answer', 'is_correct' => false],
                ['text' => 'Option D - Wrong Answer', 'is_correct' => false],
            ];

            foreach ($choices as $index => $choiceData) {
                Choice::create([
                    'question_id' => $question->id,
                    'choice_text' => $choiceData['text'],
                    'is_correct' => $choiceData['is_correct'],
                    'sort_order' => $index + 1,
                ]);
            }
        }

        // Create short answer questions
        for ($i = 1; $i <= 2; $i++) {
            Question::create([
                'team_id' => $exam->team_id,
                'question_text' => "Explain the concept of {$subject->name} topic {$i} in your own words.",
                'question_type' => 'short_answer',
                'user_id' => $teacher->id,
                'subject_id' => $subject->id,
                'points' => 20,
                'explanation' => "Students should demonstrate understanding of the key concepts",
                'difficulty_level' => 'medium',
                'is_active' => true,
            ]);
        }
    }

    private function createSampleAttempt(Exam $exam, User $student): void
    {
        $attempt = Attempt::create([
            'team_id' => $exam->team_id,
            'exam_id' => $exam->id,
            'user_id' => $student->id,
            'attempt_number' => 1,
            'status' => 'submitted',
            'started_at' => now()->subHours(2),
            'submitted_at' => now()->subHours(1),
        ]);

        // Get questions for this exam's subject
        $questions = Question::where('team_id', $exam->team_id)
            ->where('subject_id', $exam->subject_id)
            ->limit($exam->total_questions)
            ->get();

        foreach ($questions as $question) {
            if ($question->question_type === 'multiple_choice') {
                // Randomly select a choice (80% chance of correct answer)
                $correctChoice = $question->choices()->where('is_correct', true)->first();
                $allChoices = $question->choices()->get();
                $selectedChoice = (rand(1, 10) <= 8) ? $correctChoice : $allChoices->random();

                $answer = Answer::create([
                    'team_id' => $exam->team_id,
                    'attempt_id' => $attempt->id,
                    'question_id' => $question->id,
                    'selected_choice_id' => $selectedChoice->id,
                    'max_score' => $question->points,
                    'answered_at' => now()->subHours(rand(1, 2)),
                ]);

                $answer->checkCorrectness();
            } else {
                // Create written answer
                Answer::create([
                    'team_id' => $exam->team_id,
                    'attempt_id' => $attempt->id,
                    'question_id' => $question->id,
                    'answer_text' => "This is a sample student answer for the {$question->question_type} question.",
                    'max_score' => $question->points,
                    'answered_at' => now()->subHours(rand(1, 2)),
                ]);
            }
        }

        // Update attempt scores
        $attempt->updateScores();
    }

    protected function withProgressBar(int $amount, Closure $createCollectionOfOne): Collection
    {
        $progressBar = new ProgressBar($this->command->getOutput(), $amount);

        $progressBar->start();

        $items = new Collection;

        foreach (range(1, $amount) as $i) {
            $items = $items->merge(
                $createCollectionOfOne()
            );
            $progressBar->advance();
        }

        $progressBar->finish();

        $this->command->getOutput()->writeln('');

        return $items;
    }
}
