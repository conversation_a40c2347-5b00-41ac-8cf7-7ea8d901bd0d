<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'team_id',
        'user_id',
        'assigned_to',
        'title',
        'description',
        'start_datetime',
        'end_datetime',
        'priority',
        'status',
        'color',
        'has_alert',
        'alert_datetime',
        'alert_minutes_before',
        'is_recurring',
        'recurring_pattern',
        'recurring_end_date',
        'alert_sound',
        'alert_email',
        'alert_line',
        'line_user_id',
        'position_x',
        'position_y',
        'width',
        'height',
        'metadata',
        'is_all_day',
        'location',
        'notes',
    ];

    protected $casts = [
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
        'alert_datetime' => 'datetime',
        'recurring_end_date' => 'date',
        'has_alert' => 'boolean',
        'is_recurring' => 'boolean',
        'alert_sound' => 'boolean',
        'alert_email' => 'boolean',
        'alert_line' => 'boolean',
        'is_all_day' => 'boolean',
        'recurring_pattern' => 'array',
        'metadata' => 'array',
        'position_x' => 'integer',
        'position_y' => 'integer',
        'width' => 'integer',
        'height' => 'integer',
        'alert_minutes_before' => 'integer',
    ];

    /**
     * Get the team that owns the task
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get the user who created the task
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user assigned to the task
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Scope a query to only include tasks for a specific team
     */
    public function scopeForTeam(Builder $query, int $teamId): Builder
    {
        return $query->where('team_id', $teamId);
    }

    /**
     * Scope a query to only include tasks for a specific user
     */
    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where(function (Builder $q) use ($userId) {
            $q->where('user_id', $userId)->orWhere('assigned_to', $userId);
        });
    }

    /**
     * Scope a query to only include tasks for a specific date
     */
    public function scopeForDate(Builder $query, string $date): Builder
    {
        return $query->whereDate('start_datetime', $date);
    }

    /**
     * Scope a query to only include tasks within a date range
     */
    public function scopeBetweenDates(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('start_datetime', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include upcoming tasks
     */
    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where('start_datetime', '>', now());
    }

    /**
     * Scope a query to only include today's tasks
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('start_datetime', today());
    }

    /**
     * Scope a query to only include tasks by status
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include tasks by priority
     */
    public function scopeByPriority(Builder $query, string $priority): Builder
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope a query to only include tasks with alerts
     */
    public function scopeWithAlerts(Builder $query): Builder
    {
        return $query->where('has_alert', true);
    }

    /**
     * Scope a query to only include recurring tasks
     */
    public function scopeRecurring(Builder $query): Builder
    {
        return $query->where('is_recurring', true);
    }

    /**
     * Get the duration of the task in minutes
     */
    public function getDurationAttribute(): ?int
    {
        if (!$this->end_datetime) {
            return null;
        }

        return $this->start_datetime->diffInMinutes($this->end_datetime);
    }

    /**
     * Get the formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration) {
            return 'No end time';
        }

        $minutes = $this->duration;

        if ($minutes < 60) {
            return $minutes . ' minutes';
        }

        $hours = floor($minutes / 60);
        $remainingMinutes = $minutes % 60;

        if ($remainingMinutes === 0) {
            return $hours . ' hour' . ($hours > 1 ? 's' : '');
        }

        return $hours . 'h ' . $remainingMinutes . 'm';
    }

    /**
     * Get the formatted time range
     */
    public function getTimeRangeAttribute(): string
    {
        if ($this->is_all_day) {
            return 'All day';
        }

        $start = $this->start_datetime->format('H:i');

        if (!$this->end_datetime) {
            return $start;
        }

        return $start . ' - ' . $this->end_datetime->format('H:i');
    }

    /**
     * Get the formatted date
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->start_datetime->format('M j, Y');
    }

    /**
     * Get the day of week
     */
    public function getDayOfWeekAttribute(): string
    {
        return $this->start_datetime->format('l');
    }

    /**
     * Check if the task is today
     */
    public function isToday(): bool
    {
        return $this->start_datetime->isToday();
    }

    /**
     * Check if the task is in the past
     */
    public function isPast(): bool
    {
        return $this->start_datetime->isPast();
    }

    /**
     * Check if the task is currently active
     */
    public function isActive(): bool
    {
        if (!$this->end_datetime) {
            return false;
        }

        $now = now();
        return $now->between($this->start_datetime, $this->end_datetime);
    }

    /**
     * Check if the task is overdue
     */
    public function isOverdue(): bool
    {
        return $this->isPast() && $this->status !== 'completed';
    }

    /**
     * Get the alert time
     */
    public function getAlertTimeAttribute(): ?Carbon
    {
        if (!$this->has_alert) {
            return null;
        }

        if ($this->alert_datetime) {
            return $this->alert_datetime;
        }

        if ($this->alert_minutes_before) {
            return $this->start_datetime->subMinutes($this->alert_minutes_before);
        }

        return null;
    }

    /**
     * Check if alert should be triggered
     */
    public function shouldTriggerAlert(): bool
    {
        if (!$this->has_alert || !$this->alert_time) {
            return false;
        }

        return now()->gte($this->alert_time) && $this->status !== 'completed';
    }

    /**
     * Get notification methods as array
     */
    public function getNotificationMethodsAttribute(): array
    {
        $methods = [];

        if ($this->alert_sound) {
            $methods[] = 'sound';
        }

        if ($this->alert_email) {
            $methods[] = 'email';
        }

        if ($this->alert_line) {
            $methods[] = 'line';
        }

        return $methods;
    }

    /**
     * Get priority color for UI
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => '#10B981',      // Green
            'medium' => '#F59E0B',   // Yellow
            'high' => '#EF4444',     // Red
            'urgent' => '#DC2626',   // Dark Red
            default => '#6B7280',    // Gray
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'pending' => '#6B7280',     // Gray
            'in_progress' => '#3B82F6', // Blue
            'completed' => '#10B981',   // Green
            'cancelled' => '#EF4444',   // Red
            default => '#6B7280',       // Gray
        };
    }
}
