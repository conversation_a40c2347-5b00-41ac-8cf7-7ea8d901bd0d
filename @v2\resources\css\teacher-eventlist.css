/* Teacher Event List Specific Styles */

/* Card Shadow */
.card-shadow {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.card-shadow:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Gradient Background */
.gradient-bg {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
    transition: all 0.3s ease;
}

.gradient-bg:hover {
    background: linear-gradient(135deg, #4338CA 0%, #6D28D9 100%);
    transform: translateY(-1px);
}

/* Activity Card Styles */
.activity-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

/* Checkbox Styles */
.checkbox-item {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    z-index: 10;
}

.checkbox-item:checked + label {
    background-color: #EEF2FF;
    border-color: #4F46E5;
    transform: scale(1.02);
}

.checkbox-item:checked + label .check-icon {
    display: flex !important;
    animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Progress Bar Styles */
.progress-bar {
    height: 8px;
    border-radius: 4px;
    background-color: #E5E7EB;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    border-radius: 4px;
    background: linear-gradient(90deg, #4F46E5 0%, #7C3AED 100%);
    transition: width 1s ease-in-out;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Form Styles */
.form-input {
    transition: all 0.2s ease;
}

.form-input:focus {
    transform: scale(1.02);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #4338CA 0%, #6D28D9 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.btn-secondary {
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Status Badge Styles */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-active {
    background-color: #3B82F6;
    color: white;
    animation: pulse-blue 2s infinite;
}

.status-completed {
    background-color: #6B7280;
    color: white;
}

@keyframes pulse-blue {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Activity Tag Styles */
.activity-tag {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.activity-tag:hover {
    transform: scale(1.05);
}

/* Icon Styles */
.icon-primary {
    color: #4F46E5;
    transition: color 0.2s ease;
}

.icon-primary:hover {
    color: #4338CA;
}

/* Loading States */
.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Form Animation */
.form-slide-in {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Card Animation */
.card-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Modal Styles */
.modal-overlay {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    transition: opacity 0.3s ease;
}

.modal-content {
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

/* Notification Styles */
.notification {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .activity-card {
        margin-bottom: 1rem;
    }
    
    .form-slide-in {
        animation: none;
    }
    
    .card-fade-in {
        animation: none;
    }
    
    .progress-bar {
        height: 6px;
    }
    
    .activity-tag {
        font-size: 0.6rem;
        padding: 0.125rem 0.375rem;
    }
    
    .status-badge {
        font-size: 0.6rem;
        padding: 0.125rem 0.375rem;
    }
}

/* Focus States */
.focus-ring:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Error States */
.error-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Success States */
.success-bounce {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}
