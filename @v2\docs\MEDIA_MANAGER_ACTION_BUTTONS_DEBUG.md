# Media Manager Action Buttons Debug Guide

This guide helps you troubleshoot and fix missing action buttons in the Media Manager.

## Problem

Action buttons (Add Media, New Folder, Delete Folder, Edit Folder) disappear in the media manager interface at URLs like `/backend/admin-team/media-manager/medias?folder_id=30`.

## Root Cause

The issue is caused by the user access control logic in `getHeaderActions()` method that determines whether to show action buttons based on:

1. User access control configuration (`allow_user_access`)
2. Folder ownership and permissions
3. User roles and team membership

## Quick Fixes

### Option 1: Temporary Debug Override

Edit `config/media-manager-debug.php`:

```php
return [
    // Set to true to force show actions for all users
    'force_show_actions' => true,
    
    // Set to true to disable user access control completely
    'disable_user_access_control' => true,
    
    // Set to true to enable debug logging
    'enable_debug_logging' => true,
];
```

### Option 2: Disable User Access Control

Edit `config/filament-media-manager.php`:

```php
return [
    // ... other settings
    
    // Set to false to disable user access control
    "allow_user_access" => false,
];
```

### Option 3: Check User Roles

Ensure your user has the correct roles:

```php
// In tinker or a controller
$user = auth()->user();
$user->assignRole('super_admin'); // or 'team_admin'
```

## Debugging Steps

### Step 1: Enable Debug Logging

Set `'enable_debug_logging' => true` in `config/media-manager-debug.php`, then check the logs:

```bash
tail -f storage/logs/laravel.log
```

Look for entries like:
```
Media Manager Actions Debug: {
    "folder_id": 30,
    "folder_user_id": "null",
    "current_user_id": "1",
    "folder_user_type": "null",
    "current_user_type": "App\\Models\\User",
    "allow_user_access": true,
    "user_team_id": "1",
    "folder_team_id": "null",
    "folder_is_public": "null",
    "user_roles": ["super_admin"]
}
```

### Step 2: Check Folder Properties

```php
// In tinker
$folder = \App\Models\MediaManager\Folder::find(30);
dd([
    'id' => $folder->id,
    'user_id' => $folder->user_id,
    'user_type' => $folder->user_type,
    'team_id' => $folder->team_id,
    'is_public' => $folder->is_public,
    'has_user_access' => $folder->has_user_access,
]);
```

### Step 3: Check User Properties

```php
// In tinker
$user = auth()->user();
dd([
    'id' => $user->id,
    'team_id' => $user->team_id,
    'roles' => $user->roles->pluck('name')->toArray(),
    'class' => get_class($user),
]);
```

## Common Issues and Solutions

### Issue 1: Folder Has No Owner

**Problem**: Folder has `user_id = null` but user access control is enabled.

**Solution**: Either:
- Set folder as public: `$folder->update(['is_public' => true]);`
- Assign folder to user: `$folder->update(['user_id' => auth()->id(), 'user_type' => get_class(auth()->user())]);`
- Disable user access control

### Issue 2: User Not Super Admin

**Problem**: User doesn't have super_admin or team_admin role.

**Solution**:
```php
$user = auth()->user();
$user->assignRole('super_admin');
```

### Issue 3: Team Mismatch

**Problem**: Folder belongs to different team than user.

**Solution**: Either:
- Move folder to user's team: `$folder->update(['team_id' => auth()->user()->team_id]);`
- Make folder public: `$folder->update(['is_public' => true]);`
- Assign user to folder's team

### Issue 4: Missing Role Permissions

**Problem**: User has role but role doesn't have permissions.

**Solution**:
```php
// Check if roles exist
$superAdmin = \Spatie\Permission\Models\Role::where('name', 'super_admin')->first();
$teamAdmin = \Spatie\Permission\Models\Role::where('name', 'team_admin')->first();

// Create roles if they don't exist
if (!$superAdmin) {
    \Spatie\Permission\Models\Role::create(['name' => 'super_admin']);
}
if (!$teamAdmin) {
    \Spatie\Permission\Models\Role::create(['name' => 'team_admin']);
}
```

## Permanent Solutions

### Solution 1: Update Access Control Logic

The access control logic has been updated to be more permissive:

1. Super admins always have access
2. Team admins have access to team content
3. Public folders are accessible to all
4. Folders without owners are accessible to all
5. Team members can access team folders

### Solution 2: Proper Folder Setup

Ensure folders are properly configured:

```php
// For team folders
$folder->update([
    'team_id' => $teamId,
    'is_public' => false,
    'user_id' => null, // No specific owner
]);

// For personal folders
$folder->update([
    'user_id' => $userId,
    'user_type' => get_class($user),
    'team_id' => $user->team_id,
    'is_public' => false,
]);

// For public folders
$folder->update([
    'is_public' => true,
    'user_id' => null,
    'team_id' => null,
]);
```

### Solution 3: Role-Based Access

Ensure users have appropriate roles:

```php
// For super admins
$user->assignRole('super_admin');

// For team admins
$user->assignRole('team_admin');

// For regular users (no special role needed)
```

## Testing

After applying fixes, test by:

1. Visiting the media manager URL
2. Checking if action buttons appear
3. Trying to create media/folders
4. Checking different user roles
5. Testing with different folder types

## Files Modified

- `app/Filament/Resources/MediaManager/MediaResource/Pages/ListMedia.php` - Enhanced access control logic
- `config/media-manager-debug.php` - Debug configuration options
- Enhanced logging and debugging capabilities

## Prevention

To prevent this issue in the future:

1. Always assign proper roles to users
2. Set up folders with correct ownership/team assignment
3. Use public folders for shared content
4. Test access control after user/team changes
5. Monitor logs for access control issues
