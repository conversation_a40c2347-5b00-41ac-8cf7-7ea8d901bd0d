<?php

namespace App\Filament\App\Widgets\Teacher;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TeacherCourseProgressWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.teacher.teacher-course-progress';
    
    protected int | string | array $columnSpan = 'full';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('teacher');
    }

    public function getCourses(): array
    {
        // Placeholder data - replace with actual course data from database
        return [
            [
                'name' => 'ภาษาไทย',
                'progress' => 100,
                'completed_chapters' => 10,
                'total_chapters' => 10,
                'color' => '#3b82f6', // blue
                'bg_class' => 'from-blue-50 to-blue-100',
                'border_class' => 'border-blue-200'
            ],
            [
                'name' => 'คณิตศาสตร์',
                'progress' => 50,
                'completed_chapters' => 5,
                'total_chapters' => 10,
                'color' => '#4ade80', // green
                'bg_class' => 'from-green-50 to-green-100',
                'border_class' => 'border-green-200'
            ],
            [
                'name' => 'วิทยาศาสตร์',
                'progress' => 30,
                'completed_chapters' => 3,
                'total_chapters' => 10,
                'color' => '#facc15', // yellow
                'bg_class' => 'from-yellow-50 to-yellow-100',
                'border_class' => 'border-yellow-200'
            ],
            [
                'name' => 'สังคมศึกษา',
                'progress' => 20,
                'completed_chapters' => 2,
                'total_chapters' => 10,
                'color' => '#f87171', // red
                'bg_class' => 'from-red-50 to-red-100',
                'border_class' => 'border-red-200'
            ],
        ];
    }

    public function getCircleProgress($progress): array
    {
        $circumference = 2 * pi() * 40; // radius = 40
        $strokeDasharray = $circumference;
        $strokeDashoffset = $circumference - ($progress / 100) * $circumference;
        
        return [
            'strokeDasharray' => $strokeDasharray,
            'strokeDashoffset' => $strokeDashoffset
        ];
    }
}
