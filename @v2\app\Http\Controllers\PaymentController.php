<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use TomatoPHP\FilamentSubscriptions\Models\Subscription;
use TomatoPHP\FilamentSubscriptions\Models\Plan;
use TomatoPHP\FilamentPayments\Models\Payment;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    public function checkout(Request $request)
    {
        $subscriptionId = $request->get('subscription');
        $action = $request->get('action', 'new');

        $subscription = Subscription::findOrFail($subscriptionId);

        // Ensure user owns this subscription
        if ($subscription->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to subscription');
        }

        $plan = $subscription->plan;

        return view('payment.checkout', [
            'subscription' => $subscription,
            'plan' => $plan,
            'action' => $action,
            'amount' => $plan->price,
        ]);
    }

    public function process(Request $request)
    {
        $request->validate([
            'subscription_id' => 'required|exists:subscriptions,id',
            'payment_method' => 'required|string',
            'amount' => 'required|numeric|min:0',
        ]);

        $subscription = Subscription::findOrFail($request->subscription_id);

        // Ensure user owns this subscription
        if ($subscription->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to subscription');
        }

        // Create payment record
        $payment = Payment::create([
            'user_id' => Auth::id(),
            'subscription_id' => $subscription->id,
            'amount' => $request->amount,
            'currency' => 'USD',
            'payment_method' => $request->payment_method,
            'status' => 'pending',
            'description' => 'Subscription payment for ' . $subscription->plan->name,
        ]);

        // Here you would integrate with actual payment gateway
        // For demo purposes, we'll mark it as completed
        $payment->update(['status' => 'completed']);

        // Update subscription status
        $subscription->update([
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addDays($subscription->plan->interval_count),
        ]);

        return redirect()->route('profile.edit')
            ->with('success', 'Payment processed successfully!');
    }

    public function success(Request $request)
    {
        return view('payment.success');
    }

    public function cancel(Request $request)
    {
        return view('payment.cancel');
    }
}
