<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Exam\Exam;
use App\Models\Exam\Question;
use App\Models\Exam\Choice;
use App\Models\Exam\Attempt;
use App\Models\Exam\Answer;

class TestModels extends Command
{
    protected $signature = 'test:models';
    protected $description = 'Test if the renamed exam models work correctly';

    public function handle()
    {
        $this->info('Testing renamed exam models...');

        try {
            // Test table names
            $this->info('Testing table names:');
            $this->line('- Exam table: ' . (new Exam())->getTable());
            $this->line('- Question table: ' . (new Question())->getTable());
            $this->line('- Choice table: ' . (new Choice())->getTable());
            $this->line('- Attempt table: ' . (new Attempt())->getTable());
            $this->line('- Answer table: ' . (new Answer())->getTable());

            // Test relationships
            $this->info('Testing model relationships...');
            
            // Create a test exam
            $exam = new Exam();
            $this->line('- Exam->attempts relationship: ' . get_class($exam->attempts()));
            $this->line('- Exam->questions relationship: ' . get_class($exam->questions()));

            // Create a test question
            $question = new Question();
            $this->line('- Question->choices relationship: ' . get_class($question->choices()));
            $this->line('- Question->answers relationship: ' . get_class($question->answers()));

            // Create a test attempt
            $attempt = new Attempt();
            $this->line('- Attempt->answers relationship: ' . get_class($attempt->answers()));
            $this->line('- Attempt->exam relationship: ' . get_class($attempt->exam()));

            // Create a test answer
            $answer = new Answer();
            $this->line('- Answer->attempt relationship: ' . get_class($answer->attempt()));
            $this->line('- Answer->question relationship: ' . get_class($answer->question()));

            $this->info('✅ All model tests passed!');

        } catch (\Exception $e) {
            $this->error('❌ Model test failed: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
