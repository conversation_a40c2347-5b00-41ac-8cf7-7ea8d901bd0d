@extends('layouts.frontend')

@section('title', 'ระบบจัดการตารางสอนครู - EduNest')

@push('styles')
    @vite('resources/css/teacher-timetable.css')
@endpush

@push('scripts')
    @vite('resources/js/teacher-timetable.js')
@endpush

@section('content')

<!-- Background Shapes -->
<div class="shape w-96 h-96 bg-cyan-500 top-0 left-0 blob"></div>
<div class="shape w-96 h-96 bg-blue-500 bottom-0 right-0 blob" style="animation-delay: -5s;"></div>

<!-- Page Header -->
<section class="relative pt-20 pb-8 bg-gradient-to-r from-cyan-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4 fade-in">
                ระบบจัดการ<span class="gradient-text">ตารางสอนครู</span>
            </h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto fade-in delay-100">
                สร้างและจัดการตารางสอน เลือกสื่อการสอนที่เหมาะสม และวางแผนการเรียนการสอนอย่างมีประสิทธิภาพ
            </p>
        </div>
    </div>
</section>

<!-- Main Content -->
<main class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-2">เพิ่มตารางสอนใหม่</h2>
            <p class="text-gray-500">กรอกข้อมูลตารางสอนและเลือกสื่อการสอนที่ต้องการ</p>
        </div>
        
        <div class="form-section bg-white rounded-xl p-6 mb-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <label class="block text-gray-700 font-medium mb-2" for="date">วันที่สอน</label>
                    <input type="date" id="date" class="w-full input-field focus-ring">
                </div>
                
                <div>
                    <label class="block text-gray-700 font-medium mb-2" for="time">เวลาที่สอน</label>
                    <select id="time" class="w-full input-field focus-ring">
                        <option value="" disabled selected>เลือกเวลาที่สอน</option>
                        <option value="08:30-09:30">08:30 - 09:30</option>
                        <option value="09:30-10:30">09:30 - 10:30</option>
                        <option value="10:30-11:30">10:30 - 11:30</option>
                        <option value="11:30-12:30">11:30 - 12:30</option>
                        <option value="13:00-14:00">13:00 - 14:00</option>
                        <option value="14:00-15:00">14:00 - 15:00</option>
                        <option value="15:00-16:00">15:00 - 16:00</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-gray-700 font-medium mb-2" for="class">ระดับชั้น / ห้อง</label>
                    <select id="class" class="w-full input-field focus-ring">
                        <option value="" disabled selected>เลือกระดับชั้น / ห้อง</option>
                        <option value="ป.5/1">ป.5/1</option>
                        <option value="ป.5/2">ป.5/2</option>
                        <option value="ป.6/1">ป.6/1</option>
                        <option value="ป.6/2">ป.6/2</option>
                        <option value="ม.1/1">ม.1/1</option>
                        <option value="ม.1/2">ม.1/2</option>
                        <option value="ม.2/1">ม.2/1</option>
                        <option value="ม.2/2">ม.2/2</option>
                        <option value="ม.3/1">ม.3/1</option>
                        <option value="ม.3/2">ม.3/2</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-gray-700 font-medium mb-2" for="subject">วิชาที่สอน</label>
                    <select id="subject" class="w-full input-field focus-ring">
                        <option value="" disabled selected>เลือกวิชาที่สอน</option>
                        <option value="คณิตศาสตร์">คณิตศาสตร์</option>
                        <option value="วิทยาศาสตร์">วิทยาศาสตร์</option>
                        <option value="ภาษาไทย">ภาษาไทย</option>
                        <option value="ภาษาอังกฤษ">ภาษาอังกฤษ</option>
                        <option value="สังคมศึกษา">สังคมศึกษา</option>
                        <option value="ประวัติศาสตร์">ประวัติศาสตร์</option>
                        <option value="สุขศึกษา">สุขศึกษา</option>
                        <option value="ศิลปะ">ศิลปะ</option>
                        <option value="การงานอาชีพ">การงานอาชีพ</option>
                    </select>
                </div>
            </div>
            
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">จัดการสื่อการสอน</h3>
                
                <div class="flex border-b border-gray-200 mb-6">
                    <button id="tab-default" class="tab tab-active" onclick="switchTab('default')">ใช้สื่อตามบทเรียน</button>
                    <button id="tab-custom" class="tab" onclick="switchTab('custom')">เลือกจัดการสื่อเอง</button>
                </div>
                
                <div id="content-default" class="py-3">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" for="book">หนังสือเรียน</label>
                            <select id="book" class="w-full input-field focus-ring">
                                <option value="" disabled selected>เลือกหนังสือเรียน</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-gray-700 font-medium mb-2" for="chapter">บทเรียน</label>
                            <select id="chapter" class="w-full input-field focus-ring">
                                <option value="" disabled selected>เลือกบทเรียน</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="materials-section" class="hidden bg-gray-50 rounded-xl p-6 mb-6">
                        <h4 class="font-medium text-gray-700 mb-4">สื่อการสอนที่มีให้</h4>
                        
                        <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
                            <div class="material-card bg-white rounded-xl p-4 flex flex-col items-center text-center hover-lift">
                                <div class="material-icon w-12 h-12 rounded-full bg-red-50 flex items-center justify-center mb-3">
                                    <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm text-gray-600 mb-3">แผนการสอน</span>
                                <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดาวน์โหลด PDF</button>
                            </div>
                            
                            <div class="material-card bg-white rounded-xl p-4 flex flex-col items-center text-center hover-lift">
                                <div class="material-icon w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mb-3">
                                    <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm text-gray-600 mb-3">ใบงาน</span>
                                <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดาวน์โหลด PDF</button>
                            </div>
                            
                            <div class="material-card bg-white rounded-xl p-4 flex flex-col items-center text-center hover-lift">
                                <div class="material-icon w-12 h-12 rounded-full bg-green-50 flex items-center justify-center mb-3">
                                    <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm text-gray-600 mb-3">PowerPoint</span>
                                <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดาวน์โหลด PPTX</button>
                            </div>
                            
                            <div class="material-card bg-white rounded-xl p-4 flex flex-col items-center text-center hover-lift">
                                <div class="material-icon w-12 h-12 rounded-full bg-purple-50 flex items-center justify-center mb-3">
                                    <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <span class="text-sm text-gray-600 mb-3">วิดีโอ</span>
                                <button class="text-xs bg-white text-purple-600 border border-purple-200 px-3 py-1.5 rounded-full hover:bg-purple-50 transition-colors">ดูตัวอย่าง</button>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <button id="add-homework-btn" class="flex items-center text-sm font-medium text-purple-600 hover:text-purple-800 transition-colors hover-scale">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                เพิ่มการบ้านหรือกิจกรรม
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="content-custom" class="py-3 hidden">
                    <h4 class="font-medium text-gray-700 mb-4">คลังสื่อของฉัน</h4>
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                        <div class="media-item p-4 flex items-center cursor-pointer hover-lift">
                            <div class="w-10 h-10 rounded-lg bg-red-50 flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium">แผนการสอนคณิตศาสตร์</p>
                                <p class="text-xs text-gray-500">PDF • 2.3 MB</p>
                            </div>
                        </div>
                        
                        <div class="media-item p-4 flex items-center cursor-pointer hover-lift">
                            <div class="w-10 h-10 rounded-lg bg-green-50 flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium">สไลด์บทที่ 3</p>
                                <p class="text-xs text-gray-500">PPTX • 5.7 MB</p>
                            </div>
                        </div>
                        
                        <div class="media-item p-4 flex items-center cursor-pointer hover-lift">
                            <div class="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium">ใบงานเรื่องเศษส่วน</p>
                                <p class="text-xs text-gray-500">PDF • 1.1 MB</p>
                            </div>
                        </div>
                    </div>
                    
                    <button class="flex items-center text-sm font-medium text-purple-600 hover:text-purple-800 transition-colors hover-scale">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        เพิ่มสื่อใหม่
                    </button>
                </div>
            </div>
            
            <div id="preview-section" class="hidden preview-section rounded-xl p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">ตัวอย่างตารางสอน</h3>
                
                <div class="bg-white rounded-xl p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">วันที่สอน</p>
                            <p class="font-medium" id="preview-date">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">เวลาที่สอน</p>
                            <p class="font-medium" id="preview-time">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">ระดับชั้น / ห้อง</p>
                            <p class="font-medium" id="preview-class">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">วิชาที่สอน</p>
                            <p class="font-medium" id="preview-subject">-</p>
                        </div>
                    </div>
                    
                    <div class="border-t border-gray-100 pt-4">
                        <p class="text-sm text-gray-500 mb-3">สื่อการสอนที่เลือก</p>
                        <div class="flex flex-wrap gap-2" id="preview-materials">
                            <!-- Materials will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end">
                <button id="save-btn" class="btn btn-primary px-6 py-2.5 hover-lift">
                    บันทึกตารางสอน
                </button>
            </div>
        </div>
    </div>
</main>

<!-- Modal สำหรับเพิ่มการบ้าน -->
<div id="homework-modal" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 hidden modal">
    <div class="bg-white rounded-2xl w-full max-w-md mx-4 modal-content">
        <div class="px-6 py-4 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-800">เพิ่มการบ้านหรือกิจกรรม</h3>
        </div>
        
        <div class="px-6 py-6">
            <div class="mb-4">
                <label class="block text-gray-700 font-medium mb-2" for="homework-title">หัวข้อการบ้าน</label>
                <input type="text" id="homework-title" class="w-full input-field focus-ring" placeholder="เช่น แบบฝึกหัดบทที่ 3">
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 font-medium mb-2" for="homework-desc">รายละเอียด</label>
                <textarea id="homework-desc" rows="4" class="w-full input-field focus-ring" placeholder="รายละเอียดการบ้านหรือกิจกรรม"></textarea>
            </div>
            
            <div class="mb-4">
                <label class="block text-gray-700 font-medium mb-2" for="homework-due">กำหนดส่ง</label>
                <input type="date" id="homework-due" class="w-full input-field focus-ring">
            </div>
        </div>
        
        <div class="px-6 py-4 border-t border-gray-100 flex justify-end space-x-3">
            <button id="close-modal-btn" class="btn btn-outline px-4 py-2">ยกเลิก</button>
            <button id="add-homework-confirm-btn" class="btn btn-primary px-4 py-2">เพิ่มการบ้าน</button>
        </div>
    </div>
</div>

<!-- Modal แจ้งเตือนบันทึกสำเร็จ -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 hidden modal">
    <div class="bg-white rounded-2xl w-full max-w-sm mx-4 p-6 text-center modal-content">
        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-green-50 flex items-center justify-center">
            <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
        </div>
        
        <h3 class="text-lg font-semibold text-gray-800 mb-2">บันทึกสำเร็จ</h3>
        <p class="text-gray-600 mb-6">บันทึกตารางสอนเรียบร้อยแล้ว</p>
        
        <button id="success-close-btn" class="w-full btn btn-primary py-2.5">ตกลง</button>
    </div>
</div>

@endsection
