# Wholesale Pricing Guide

## Overview

Both Physical and Digital products now support wholesale/volume pricing tiers, allowing different prices based on quantity purchased. Digital products also have their security stock hidden and automatically set to 0.

## Changes Made

### 1. Digital Product Security Stock

#### Before:
```php
Forms\Components\TextInput::make('security_stock')
    ->label('Security Stock')
    ->helperText('Not applicable for digital products (set to 0)')
    ->numeric()
    ->default(0)
    ->rules(['integer', 'min:0'])
    ->required()
```

#### After:
```php
Forms\Components\Hidden::make('security_stock')
    ->default(0)
```

**Benefit:** Simplifies digital product forms by removing irrelevant inventory fields.

### 2. Wholesale Pricing System

#### Database Schema:
- **New Column**: `wholesale_pricing` (JSON) stores pricing tiers
- **Structure**: Array of objects with `min_quantity`, `price`, and `label`

#### Model Updates:
```php
// Added to casts
'wholesale_pricing' => 'array',

// Added to fillable
'wholesale_pricing',
```

### 3. Physical Product Wholesale Pricing

#### Form Component:
```php
Forms\Components\Repeater::make('wholesale_pricing')
    ->label('Wholesale Pricing Tiers')
    ->schema([
        Forms\Components\TextInput::make('min_quantity')
            ->label('Minimum Quantity')
            ->numeric()
            ->required()
            ->placeholder('e.g., 10'),

        Forms\Components\TextInput::make('price')
            ->label('Wholesale Price')
            ->numeric()
            ->required()
            ->placeholder('e.g., 30.00'),

        Forms\Components\TextInput::make('label')
            ->label('Tier Label (Optional)')
            ->placeholder('e.g., Bulk Discount'),
    ])
    ->addActionLabel('Add Wholesale Tier')
    ->helperText('Set different prices for bulk purchases (e.g., Buy 10+ for $30 each)')
```

### 4. Digital Product Volume Licensing

#### Form Component:
```php
Forms\Components\Repeater::make('wholesale_pricing')
    ->label('Volume Licensing Tiers')
    ->schema([
        Forms\Components\TextInput::make('min_quantity')
            ->label('Minimum Licenses')
            ->numeric()
            ->required()
            ->placeholder('e.g., 5'),

        Forms\Components\TextInput::make('price')
            ->label('Price per License')
            ->numeric()
            ->required()
            ->placeholder('e.g., 25.00'),

        Forms\Components\TextInput::make('label')
            ->label('Tier Label (Optional)')
            ->placeholder('e.g., Team License'),
    ])
    ->addActionLabel('Add Volume Tier')
    ->helperText('Set different prices for volume licensing (e.g., Buy 5+ licenses for $25 each)')
```

## Pricing Tier Examples

### Physical Product Wholesale Tiers

#### Example: Office Chair ($100 regular price)
```json
[
    {
        "min_quantity": 5,
        "price": 90.00,
        "label": "Small Bulk"
    },
    {
        "min_quantity": 10,
        "price": 80.00,
        "label": "Wholesale"
    },
    {
        "min_quantity": 25,
        "price": 70.00,
        "label": "Volume Discount"
    },
    {
        "min_quantity": 50,
        "price": 60.00,
        "label": "Bulk Wholesale"
    }
]
```

#### Pricing Structure:
- **1-4 units**: $100.00 each (regular price)
- **5-9 units**: $90.00 each (10% discount)
- **10-24 units**: $80.00 each (20% discount)
- **25-49 units**: $70.00 each (30% discount)
- **50+ units**: $60.00 each (40% discount)

### Digital Product Volume Licensing

#### Example: Software License ($50 regular price)
```json
[
    {
        "min_quantity": 3,
        "price": 42.50,
        "label": "Team License"
    },
    {
        "min_quantity": 10,
        "price": 35.00,
        "label": "Business License"
    },
    {
        "min_quantity": 25,
        "price": 27.50,
        "label": "Enterprise License"
    },
    {
        "min_quantity": 100,
        "price": 20.00,
        "label": "Site License"
    }
]
```

#### Pricing Structure:
- **1-2 licenses**: $50.00 each (regular price)
- **3-9 licenses**: $42.50 each (15% discount)
- **10-24 licenses**: $35.00 each (30% discount)
- **25-99 licenses**: $27.50 each (45% discount)
- **100+ licenses**: $20.00 each (60% discount)

## Table Display

### Wholesale Tiers Column

#### Physical Products:
```php
Tables\Columns\TextColumn::make('wholesale_pricing')
    ->label('Wholesale Tiers')
    ->formatStateUsing(function (?array $state): string {
        if (!$state || empty($state)) return 'None';
        
        $tiers = [];
        foreach ($state as $tier) {
            if (isset($tier['min_quantity']) && isset($tier['price'])) {
                $tiers[] = $tier['min_quantity'] . '+: $' . number_format($tier['price'], 2);
            }
        }
        
        return !empty($tiers) ? implode(' | ', array_slice($tiers, 0, 2)) : 'None';
    })
```

#### Digital Products:
```php
Tables\Columns\TextColumn::make('wholesale_pricing')
    ->label('Volume Tiers')
    // Same formatting logic
```

#### Display Examples:
- **Physical**: "5+: $90.00 | 10+: $80.00"
- **Digital**: "3+: $42.50 | 10+: $35.00"
- **No Tiers**: "None"

## Seeder Data

### Automatic Tier Generation

#### Physical Products:
- **5+ units**: 10% discount
- **10+ units**: 20% discount
- **25+ units**: 30% discount
- **50+ units**: 40% discount

#### Digital Products:
- **3+ licenses**: 15% discount
- **10+ licenses**: 30% discount
- **25+ licenses**: 45% discount
- **100+ licenses**: 60% discount

### Smart Filtering:
- **Minimum Price**: No tier below $1.00
- **Low-Value Products**: Products under $10 don't get wholesale tiers
- **Realistic Discounts**: Graduated discount percentages

## User Experience

### Form Behavior

#### Physical Products:
1. **Regular Pricing**: Set base price, compare price, cost
2. **Add Wholesale Tiers**: Click "Add Wholesale Tier"
3. **Configure Tier**: Set minimum quantity, wholesale price, optional label
4. **Multiple Tiers**: Add as many tiers as needed
5. **Collapsible**: Section can be collapsed to save space

#### Digital Products:
1. **Regular Pricing**: Set base price, compare price, cost
2. **Add Volume Tiers**: Click "Add Volume Tier"
3. **Configure Licensing**: Set minimum licenses, price per license, optional label
4. **Multiple Tiers**: Add as many licensing tiers as needed
5. **Hidden Security Stock**: Automatically set to 0, not visible in form

### Table View:
- **Regular Price**: Shows base price
- **Wholesale/Volume Tiers**: Shows first 2 tiers in compact format
- **Toggleable**: Column can be hidden/shown as needed
- **Hidden by Default**: Keeps table clean, can be enabled when needed

## Business Benefits

### Physical Products:
- **Bulk Sales**: Encourage larger orders with volume discounts
- **Wholesale Channels**: Support B2B sales with tiered pricing
- **Inventory Movement**: Move larger quantities faster
- **Competitive Pricing**: Offer competitive rates for bulk buyers

### Digital Products:
- **Volume Licensing**: Support team and enterprise sales
- **Scalable Pricing**: Price scales with organization size
- **License Management**: Clear pricing for different license types
- **Revenue Growth**: Encourage larger license purchases

## Technical Implementation

### Database Structure:
```sql
ALTER TABLE shop_products 
ADD COLUMN wholesale_pricing JSON NULL 
AFTER cost;
```

### JSON Structure:
```json
[
    {
        "min_quantity": 10,
        "price": 30.00,
        "label": "Wholesale"
    }
]
```

### Validation Rules:
- **min_quantity**: Required integer, minimum 1
- **price**: Required decimal, regex pattern for currency
- **label**: Optional string, max 255 characters

This wholesale pricing system provides flexible, scalable pricing options for both physical and digital products while maintaining clean, intuitive user interfaces.
