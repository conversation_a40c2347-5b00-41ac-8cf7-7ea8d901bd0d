<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Course;
use App\Models\Book;
use App\Models\Blog\Post;
use App\Models\Exam\Exam;
use App\Models\Subject;
use Carbon\Carbon;

class SitemapController extends Controller
{
    public function webpage()
    {
        // Get all pages for the sitemap webpage
        $pages = $this->getAllPages();

        return view('sitemap.index', compact('pages'));
    }

    public function index()
    {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Add homepage
        $sitemap .= $this->addUrl(route('homepage'), Carbon::now(), 'daily', '1.0');

        // Add static pages
        $sitemap .= $this->addUrl(route('downloads'), Carbon::now(), 'weekly', '0.8');

        // Add courses
        $courses = Course::where('is_active', true)
            ->get();

        foreach ($courses as $course) {
            $sitemap .= $this->addUrl(
                route('frontend.courses.show', $course->id),
                $course->updated_at,
                'weekly',
                '0.8'
            );
        }

        // Add books
        $books = Book::where('is_active', true)
            ->get();

        foreach ($books as $book) {
            $sitemap .= $this->addUrl(
                route('frontend.books.show', $book->id),
                $book->updated_at,
                'weekly',
                '0.8'
            );
        }

        // Add public exams
        $exams = Exam::where('is_active', true)
            ->where('visibility', 'public')
            ->get();

        foreach ($exams as $exam) {
            $sitemap .= $this->addUrl(
                route('frontend.exams.show', $exam->id),
                $exam->updated_at,
                'weekly',
                '0.7'
            );
        }

        // Add blog posts
        $posts = Post::whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->get();

        foreach ($posts as $post) {
            $sitemap .= $this->addUrl(
                route('frontend.posts.show', $post->id),
                $post->updated_at,
                'weekly',
                '0.6'
            );
        }

        // Add subjects
        $subjects = Subject::where('is_active', true)->get();

        foreach ($subjects as $subject) {
            $sitemap .= $this->addUrl(
                route('frontend.subjects.show', $subject->id),
                $subject->updated_at,
                'monthly',
                '0.5'
            );
        }

        $sitemap .= '</urlset>';

        return response($sitemap, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
        ]);
    }

    private function addUrl($url, $lastmod = null, $changefreq = 'weekly', $priority = '0.5')
    {
        $xml = '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url) . '</loc>' . "\n";
        
        if ($lastmod) {
            $xml .= '    <lastmod>' . $lastmod->toISOString() . '</lastmod>' . "\n";
        }
        
        $xml .= '    <changefreq>' . $changefreq . '</changefreq>' . "\n";
        $xml .= '    <priority>' . $priority . '</priority>' . "\n";
        $xml .= '  </url>' . "\n";

        return $xml;
    }

    private function getAllPages(): array
    {
        $pages = [];

        // Static pages
        $pages['Static Pages'] = [
            [
                'title' => 'Homepage',
                'url' => route('homepage'),
                'description' => 'Main homepage with featured content',
                'last_modified' => now(),
            ],
            [
                'title' => 'Downloads',
                'url' => route('downloads'),
                'description' => 'Download educational materials and resources',
                'last_modified' => now(),
            ],
            [
                'title' => 'Pricing Plans',
                'url' => route('pricing'),
                'description' => 'View subscription plans and pricing',
                'last_modified' => now(),
            ],
        ];

        // Courses
        $courses = Course::where('is_active', true)
            ->get();

        if ($courses->count() > 0) {
            $pages['Courses'] = $courses->map(function ($course) {
                return [
                    'title' => $course->title,
                    'url' => route('frontend.courses.show', $course->id),
                    'description' => strip_tags($course->description),
                    'last_modified' => $course->updated_at,
                ];
            })->toArray();
        }

        // Books
        $books = Book::where('is_active', true)
            ->get();

        if ($books->count() > 0) {
            $pages['Books'] = $books->map(function ($book) {
                return [
                    'title' => $book->title,
                    'url' => route('frontend.books.show', $book->id),
                    'description' => strip_tags($book->description ?? ''),
                    'last_modified' => $book->updated_at,
                ];
            })->toArray();
        }

        // Public Exams
        $exams = Exam::where('is_active', true)
            ->where('visibility', 'public')
            ->get();

        if ($exams->count() > 0) {
            $pages['Exams & Exercises'] = $exams->map(function ($exam) {
                return [
                    'title' => $exam->title,
                    'url' => route('frontend.exams.show', $exam->id),
                    'description' => strip_tags($exam->description ?? ''),
                    'last_modified' => $exam->updated_at,
                ];
            })->toArray();
        }

        // Blog Posts
        $posts = Post::whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->get();

        if ($posts->count() > 0) {
            $pages['Blog Posts'] = $posts->map(function ($post) {
                return [
                    'title' => $post->title,
                    'url' => route('frontend.posts.show', $post->id),
                    'description' => strip_tags($post->excerpt ?? $post->content),
                    'last_modified' => $post->updated_at,
                ];
            })->toArray();
        }

        // Subjects
        $subjects = Subject::where('is_active', true)->get();

        if ($subjects->count() > 0) {
            $pages['Subjects'] = $subjects->map(function ($subject) {
                return [
                    'title' => $subject->name,
                    'url' => route('frontend.subjects.show', $subject->id),
                    'description' => strip_tags($subject->description ?? ''),
                    'last_modified' => $subject->updated_at,
                ];
            })->toArray();
        }

        return $pages;
    }
}
