<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Course;
use App\Models\Book;
use App\Models\Blog\Post;
use App\Models\Exam\Exam;
use App\Models\Subject;
use Carbon\Carbon;

class SitemapController extends Controller
{
    public function index()
    {
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Add homepage
        $sitemap .= $this->addUrl(route('homepage'), Carbon::now(), 'daily', '1.0');

        // Add static pages
        $sitemap .= $this->addUrl(route('downloads'), Carbon::now(), 'weekly', '0.8');

        // Add courses
        $courses = Course::where('is_active', true)
            ->where('is_public', true)
            ->get();

        foreach ($courses as $course) {
            $sitemap .= $this->addUrl(
                route('courses.show', $course->slug),
                $course->updated_at,
                'weekly',
                '0.8'
            );
        }

        // Add books
        $books = Book::where('is_active', true)
            ->where('is_public', true)
            ->get();

        foreach ($books as $book) {
            $sitemap .= $this->addUrl(
                route('books.show', $book->slug),
                $book->updated_at,
                'weekly',
                '0.8'
            );
        }

        // Add public exams
        $exams = Exam::where('is_active', true)
            ->where('is_public', true)
            ->get();

        foreach ($exams as $exam) {
            $sitemap .= $this->addUrl(
                route('exams.show', $exam->slug),
                $exam->updated_at,
                'weekly',
                '0.7'
            );
        }

        // Add blog posts
        $posts = Post::where('is_published', true)
            ->where('published_at', '<=', now())
            ->get();

        foreach ($posts as $post) {
            $sitemap .= $this->addUrl(
                route('posts.show', $post->slug),
                $post->updated_at,
                'weekly',
                '0.6'
            );
        }

        // Add subjects
        $subjects = Subject::where('is_active', true)->get();

        foreach ($subjects as $subject) {
            $sitemap .= $this->addUrl(
                route('subjects.show', $subject->id),
                $subject->updated_at,
                'monthly',
                '0.5'
            );
        }

        $sitemap .= '</urlset>';

        return response($sitemap, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
        ]);
    }

    private function addUrl($url, $lastmod = null, $changefreq = 'weekly', $priority = '0.5')
    {
        $xml = '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url) . '</loc>' . "\n";
        
        if ($lastmod) {
            $xml .= '    <lastmod>' . $lastmod->toISOString() . '</lastmod>' . "\n";
        }
        
        $xml .= '    <changefreq>' . $changefreq . '</changefreq>' . "\n";
        $xml .= '    <priority>' . $priority . '</priority>' . "\n";
        $xml .= '  </url>' . "\n";

        return $xml;
    }
}
