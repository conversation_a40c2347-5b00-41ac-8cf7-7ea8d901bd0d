<?php

namespace App\Filament\Resources\MediaManager\Actions;

use App\Models\MediaManager\Folder;
use App\Models\MediaManager\Media;
use Filament\Actions;
use Filament\Notifications\Notification;

class DeleteFolderAction
{
    public static function make(int $folder_id): Actions\Action
    {
        return Actions\Action::make('delete_folder')
            ->label('Delete Folder')
            ->icon('heroicon-o-trash')
            ->color('danger')
            ->requiresConfirmation()
            ->modalHeading('Delete Folder')
            ->modalDescription('Are you sure you want to delete this folder? This will also delete all media files in this folder.')
            ->modalSubmitActionLabel('Yes, Delete')
            ->action(function () use ($folder_id) {
                try {
                    $folder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->find($folder_id);
                    
                    if (!$folder) {
                        Notification::make()
                            ->title('Folder not found')
                            ->danger()
                            ->send();
                        return;
                    }

                    // Delete all media files in this folder
                    $mediaFiles = Media::withoutGlobalScopes()
                        ->where('collection_name', $folder->collection)
                        ->get();

                    foreach ($mediaFiles as $media) {
                        try {
                            // Delete the actual file from storage
                            if ($media->getPath() && file_exists($media->getPath())) {
                                unlink($media->getPath());
                            }
                            
                            // Delete the media record
                            $media->delete();
                        } catch (\Exception $e) {
                            // Log the error but continue with other files
                            \Log::error('Error deleting media file: ' . $e->getMessage(), [
                                'media_id' => $media->id,
                                'file_path' => $media->getPath(),
                            ]);
                        }
                    }

                    // Delete sub-folders recursively
                    $subFolders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                        ->where('parent_id', $folder_id)
                        ->get();

                    foreach ($subFolders as $subFolder) {
                        // Recursively delete sub-folders
                        self::deleteFolder($subFolder->id);
                    }

                    // Delete the folder itself
                    $folder->delete();

                    Notification::make()
                        ->title('Folder Deleted')
                        ->body('Folder and all its contents have been deleted successfully.')
                        ->success()
                        ->send();

                    // Redirect back to folders list
                    if(filament()->getTenant()) {
                        $tenantSlug = filament()->getTenant()->slug ?? filament()->getTenant()->id;
                        return redirect()->to(url(filament()->getCurrentPanel()->getId() . '/' . $tenantSlug . '/folders'));
                    } else {
                        return redirect()->to(url(filament()->getCurrentPanel()->getId() . '/folders'));
                    }

                } catch (\Exception $e) {
                    \Log::error('Error deleting folder: ' . $e->getMessage(), [
                        'folder_id' => $folder_id,
                        'error' => $e->getTraceAsString(),
                    ]);

                    Notification::make()
                        ->title('Error Deleting Folder')
                        ->body('An error occurred while deleting the folder: ' . $e->getMessage())
                        ->danger()
                        ->send();
                }
            });
    }

    /**
     * Recursively delete a folder and all its contents
     */
    private static function deleteFolder(int $folderId): void
    {
        $folder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->find($folderId);
        
        if (!$folder) {
            return;
        }

        // Delete all media files in this folder
        $mediaFiles = Media::withoutGlobalScopes()
            ->where('collection_name', $folder->collection)
            ->get();

        foreach ($mediaFiles as $media) {
            try {
                // Delete the actual file from storage
                if ($media->getPath() && file_exists($media->getPath())) {
                    unlink($media->getPath());
                }
                
                // Delete the media record
                $media->delete();
            } catch (\Exception $e) {
                \Log::error('Error deleting media file in recursive delete: ' . $e->getMessage(), [
                    'media_id' => $media->id,
                    'folder_id' => $folderId,
                ]);
            }
        }

        // Delete sub-folders recursively
        $subFolders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
            ->where('parent_id', $folderId)
            ->get();

        foreach ($subFolders as $subFolder) {
            self::deleteFolder($subFolder->id);
        }

        // Delete the folder itself
        $folder->delete();
    }
}
