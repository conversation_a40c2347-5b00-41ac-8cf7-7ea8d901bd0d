@extends('layouts.frontend')

@section('title', 'มอบหมายงานให้นักเรียน - EduNest')

@push('styles')
    @vite('resources/css/teacher-eventlist.css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
@endpush

@push('scripts')
    @vite('resources/js/teacher-eventlist.js')
@endpush

@section('content')

<!-- Background Shapes -->
<div class="shape w-96 h-96 bg-cyan-500 top-0 left-0 blob"></div>
<div class="shape w-96 h-96 bg-blue-500 bottom-0 right-0 blob" style="animation-delay: -5s;"></div>

<!-- Page Header -->
<section class="relative pt-20 pb-8 bg-gradient-to-r from-cyan-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4 fade-in">
                มอบหมาย<span class="gradient-text">งานให้นักเรียน</span>
            </h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto fade-in delay-100">
                สร้างและจัดการกิจกรรมการเรียนรู้สำหรับนักเรียน ติดตามความก้าวหน้าและประเมินผลการเรียน
            </p>
        </div>
    </div>
</section>

<!-- Main Content -->
<main class="container mx-auto px-4 py-6">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Form Section -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl p-6 card-shadow">
                <h2 class="text-xl font-bold mb-6 text-gray-800 flex items-center">
                    <i class="fas fa-tasks mr-2 text-indigo-600"></i>
                    มอบหมายกิจกรรมใหม่
                </h2>
                
                <form id="assignmentForm">
                    <!-- Teaching Date -->
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            <i class="far fa-calendar-alt mr-1 text-indigo-500"></i>
                            วันที่สอน
                        </label>
                        <input type="date" class="form-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" required>
                    </div>
                    
                    <!-- Due Date -->
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            <i class="far fa-calendar-check mr-1 text-indigo-500"></i>
                            วันกำหนดส่งงาน
                        </label>
                        <div class="flex space-x-2">
                            <input type="date" class="form-input w-3/4 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" required>
                            <select class="form-input w-1/4 px-2 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option>16:00</option>
                                <option>17:00</option>
                                <option>18:00</option>
                                <option>19:00</option>
                                <option>20:00</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Class Selection -->
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            <i class="fas fa-users mr-1 text-indigo-500"></i>
                            ระดับชั้น / ห้องเรียน
                        </label>
                        <select class="form-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="classSelect" required>
                            <option value="" disabled selected>เลือกระดับชั้น / ห้องเรียน</option>
                            <option value="ป.5/1">ป.5/1</option>
                            <option value="ป.5/2">ป.5/2</option>
                            <option value="ป.6/1">ป.6/1</option>
                            <option value="ม.1/1">ม.1/1</option>
                            <option value="ม.2/2">ม.2/2</option>
                        </select>
                    </div>
                    
                    <!-- Subject Selection -->
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            <i class="fas fa-book mr-1 text-indigo-500"></i>
                            วิชาที่สอน
                        </label>
                        <select class="form-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="subjectSelect" required>
                            <option value="" disabled selected>เลือกวิชา</option>
                            <option value="ภาษาไทย">ภาษาไทย</option>
                            <option value="คณิตศาสตร์">คณิตศาสตร์</option>
                            <option value="วิทยาศาสตร์">วิทยาศาสตร์</option>
                            <option value="สังคมศึกษา">สังคมศึกษา</option>
                            <option value="ภาษาอังกฤษ">ภาษาอังกฤษ</option>
                        </select>
                    </div>
                    
                    <!-- Book Selection -->
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            <i class="fas fa-book-open mr-1 text-indigo-500"></i>
                            หนังสือ
                        </label>
                        <select class="form-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="bookSelect">
                            <option value="ไม่ระบุ">ไม่ระบุ</option>
                        </select>
                    </div>
                    
                    <!-- Lesson Selection (conditional) -->
                    <div class="mb-4" id="lessonSelectContainer" style="display: none;">
                        <label class="block text-gray-700 text-sm font-medium mb-2">
                            <i class="fas fa-bookmark mr-1 text-indigo-500"></i>
                            บทเรียน
                        </label>
                        <select class="form-input w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500" id="lessonSelect">
                            <option value="" disabled selected>เลือกบทเรียน</option>
                        </select>
                    </div>
                    
                    <!-- Activity Selection -->
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-medium mb-3">
                            <i class="fas fa-clipboard-list mr-1 text-indigo-500"></i>
                            กิจกรรมที่ต้องการมอบหมาย
                        </label>
                        
                        <div class="grid grid-cols-1 gap-3">
                            <!-- Activity 1 -->
                            <div class="relative">
                                <input type="checkbox" id="activity1" class="checkbox-item">
                                <label for="activity1" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover-lift">
                                    <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                        <div class="check-icon hidden text-indigo-500">
                                            <i class="fas fa-check"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="font-medium">แบบทดสอบที่ครูสร้างเอง</span>
                                    </div>
                                </label>
                            </div>
                            
                            <!-- Activity 2 -->
                            <div class="relative">
                                <input type="checkbox" id="activity2" class="checkbox-item">
                                <label for="activity2" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover-lift">
                                    <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                        <div class="check-icon hidden text-indigo-500">
                                            <i class="fas fa-check"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="font-medium">แบบฝึกหัดจาก EduNest</span>
                                    </div>
                                </label>
                            </div>
                            
                            <!-- Activity 3 -->
                            <div class="relative">
                                <input type="checkbox" id="activity3" class="checkbox-item">
                                <label for="activity3" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover-lift">
                                    <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                        <div class="check-icon hidden text-indigo-500">
                                            <i class="fas fa-check"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="font-medium">เล่นเกมฝึกทักษะ</span>
                                    </div>
                                </label>
                            </div>
                            
                            <!-- Activity 4 -->
                            <div class="relative">
                                <input type="checkbox" id="activity4" class="checkbox-item">
                                <label for="activity4" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover-lift">
                                    <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                        <div class="check-icon hidden text-indigo-500">
                                            <i class="fas fa-check"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="font-medium">ดูวิดีโอการสอน</span>
                                    </div>
                                </label>
                            </div>
                            
                            <!-- Activity 5 -->
                            <div class="relative">
                                <input type="checkbox" id="activity5" class="checkbox-item">
                                <label for="activity5" class="flex items-center p-3 border-2 border-gray-200 rounded-lg cursor-pointer hover-lift">
                                    <div class="w-6 h-6 rounded-md border-2 border-indigo-500 mr-3 flex items-center justify-center">
                                        <div class="check-icon hidden text-indigo-500">
                                            <i class="fas fa-check"></i>
                                        </div>
                                    </div>
                                    <div>
                                        <span class="font-medium">แบบทดสอบท้ายบท</span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="flex justify-center">
                        <button type="submit" class="gradient-bg text-white font-medium py-3 px-6 rounded-lg hover:opacity-90 transition-all flex items-center">
                            <i class="fas fa-paper-plane mr-2"></i>
                            มอบหมายกิจกรรม
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Assigned Activities Section -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl p-6 card-shadow">
                <h2 class="text-xl font-bold mb-6 text-gray-800 flex items-center">
                    <i class="fas fa-clipboard-check mr-2 text-indigo-600"></i>
                    กิจกรรมที่มอบหมายแล้ว
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Activity Card 1 -->
                    <div class="activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow">
                        <div class="bg-indigo-100 px-4 py-3 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h3 class="font-bold text-indigo-800">ป.5/1 - วิชาคณิตศาสตร์</h3>
                                <span class="status-badge status-active">กำลังดำเนินการ</span>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <i class="far fa-calendar-alt icon-primary mr-2"></i>
                                <span class="text-sm">วันที่สอน: 4 มิ.ย. 2567</span>
                            </div>
                            
                            <div class="flex items-center mb-3">
                                <i class="far fa-calendar-check icon-primary mr-2"></i>
                                <span class="text-sm">กำหนดส่ง: 6 มิ.ย. 2567 (16:00 น.)</span>
                            </div>
                            
                            <div class="mb-3">
                                <h4 class="text-sm font-medium mb-2">รายการกิจกรรม:</h4>
                                <div class="flex flex-wrap gap-2">
                                    <span class="activity-tag bg-indigo-50 text-indigo-700">แบบฝึกหัดจาก EduNest</span>
                                    <span class="activity-tag bg-indigo-50 text-indigo-700">เกมฝึกทักษะ</span>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-100 pt-3 mb-3">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium">
                                        <i class="fas fa-users icon-primary mr-1"></i>
                                        จำนวนนักเรียนในห้อง: 38 คน
                                    </span>
                                    <span class="text-sm font-medium text-indigo-600">ส่งแล้ว: 22 คน</span>
                                </div>
                                
                                <div class="progress-bar mb-2">
                                    <div class="progress-fill" style="width: 58%"></div>
                                </div>
                                
                                <div class="text-sm text-red-500 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    คะแนนไม่ผ่านเกณฑ์: 4 คน
                                </div>
                            </div>
                            
                            <div class="flex flex-wrap gap-2">
                                <button class="btn-secondary bg-indigo-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-indigo-700 transition-colors flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    ดูรายละเอียด
                                </button>
                                <button class="btn-secondary bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    ปิดกิจกรรม
                                </button>
                                <button class="btn-secondary bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center">
                                    <i class="fas fa-download mr-1"></i>
                                    เก็บข้อมูล
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Activity Card 2 -->
                    <div class="activity-card bg-white border border-gray-200 rounded-xl overflow-hidden card-shadow">
                        <div class="bg-blue-100 px-4 py-3 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h3 class="font-bold text-blue-800">ม.2/2 - วิชาวิทยาศาสตร์</h3>
                                <span class="status-badge bg-blue-600 text-white">กำลังดำเนินการ</span>
                            </div>
                        </div>
                        
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <i class="far fa-calendar-alt text-blue-500 mr-2"></i>
                                <span class="text-sm">วันที่สอน: 3 มิ.ย. 2567</span>
                            </div>
                            
                            <div class="flex items-center mb-3">
                                <i class="far fa-calendar-check text-blue-500 mr-2"></i>
                                <span class="text-sm">กำหนดส่ง: 7 มิ.ย. 2567 (18:00 น.)</span>
                            </div>
                            
                            <div class="mb-3">
                                <h4 class="text-sm font-medium mb-2">รายการกิจกรรม:</h4>
                                <div class="flex flex-wrap gap-2">
                                    <span class="activity-tag bg-blue-50 text-blue-700">แบบทดสอบที่ครูสร้างเอง</span>
                                    <span class="activity-tag bg-blue-50 text-blue-700">ดูวิดีโอการสอน</span>
                                    <span class="activity-tag bg-blue-50 text-blue-700">แบบทดสอบท้ายบท</span>
                                </div>
                            </div>
                            
                            <div class="border-t border-gray-100 pt-3 mb-3">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium">
                                        <i class="fas fa-users text-blue-500 mr-1"></i>
                                        จำนวนนักเรียนในห้อง: 42 คน
                                    </span>
                                    <span class="text-sm font-medium text-blue-600">ส่งแล้ว: 35 คน</span>
                                </div>
                                
                                <div class="progress-bar mb-2">
                                    <div class="progress-fill" style="width: 83%; background: linear-gradient(90deg, #2563EB 0%, #3B82F6 100%);"></div>
                                </div>
                                
                                <div class="text-sm text-red-500 flex items-center">
                                    <i class="fas fa-exclamation-circle mr-1"></i>
                                    คะแนนไม่ผ่านเกณฑ์: 2 คน
                                </div>
                            </div>
                            
                            <div class="flex flex-wrap gap-2">
                                <button class="btn-secondary bg-blue-600 text-white text-sm px-3 py-1.5 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    ดูรายละเอียด
                                </button>
                                <button class="btn-secondary bg-gray-200 text-gray-700 text-sm px-3 py-1.5 rounded-lg hover:bg-gray-300 transition-colors flex items-center">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    ปิดกิจกรรม
                                </button>
                                <button class="btn-secondary bg-green-100 text-green-700 text-sm px-3 py-1.5 rounded-lg hover:bg-green-200 transition-colors flex items-center">
                                    <i class="fas fa-download mr-1"></i>
                                    เก็บข้อมูล
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

@endsection
