<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center">
                <x-heroicon-o-chart-bar class="w-5 h-5 text-primary-600 mr-2" />
                📈 ความก้าวหน้าของลูก
            </div>
        </x-slot>

        <div class="space-y-8">
            @foreach($this->getChildrenProgress() as $child)
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
                    <!-- Child Header -->
                    <div class="flex justify-between items-center mb-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $child['name'] }}</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $child['grade'] }} • เกรดเฉลี่ย {{ $child['gpa'] }} • เข้าเรียน {{ $child['attendance'] }}%</p>
                        </div>
                        <div class="text-right">
                            <span class="px-3 py-1 rounded-full text-sm font-medium
                                @if($child['overall_performance'] === 'excellent') bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                @elseif($child['overall_performance'] === 'good') bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200
                                @endif">
                                @if($child['overall_performance'] === 'excellent') ดีเยี่ยม
                                @elseif($child['overall_performance'] === 'good') ดี
                                @endif
                            </span>
                        </div>
                    </div>

                    <!-- Subject Progress -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @foreach($child['subjects'] as $subject)
                            @php
                                $circleData = $this->getCircleProgress($subject['progress']);
                            @endphp
                            <div class="bg-gradient-to-br {{ $subject['bg_gradient'] }} rounded-2xl p-5 text-center cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg">
                                <div class="flex flex-col items-center">
                                    <!-- Circular Progress -->
                                    <div class="relative w-20 h-20 mb-3">
                                        <svg class="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                                            <!-- Background circle -->
                                            <circle 
                                                cx="50" 
                                                cy="50" 
                                                r="45" 
                                                fill="none" 
                                                stroke="rgba(255, 255, 255, 0.3)" 
                                                stroke-width="8" 
                                                stroke-linecap="round"
                                            />
                                            <!-- Progress circle -->
                                            <circle 
                                                cx="50" 
                                                cy="50" 
                                                r="45" 
                                                fill="none" 
                                                stroke="white" 
                                                stroke-width="8" 
                                                stroke-linecap="round"
                                                stroke-dasharray="{{ $circleData['strokeDasharray'] }}"
                                                stroke-dashoffset="{{ $circleData['strokeDashoffset'] }}"
                                                class="transition-all duration-1000 ease-in-out"
                                            />
                                        </svg>
                                        <!-- Progress text -->
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <span class="text-sm font-bold text-white">{{ $subject['progress'] }}%</span>
                                        </div>
                                    </div>
                                    
                                    <!-- Subject info -->
                                    <h4 class="font-semibold text-white mb-1 text-sm">{{ $subject['name'] }}</h4>
                                    <p class="text-xs text-white/90 mb-1">{{ $subject['exercises_completed'] }} แบบฝึกหัด</p>
                                    <p class="text-xs text-white/80">{{ $subject['average_score'] }} คะแนนเฉลี่ย</p>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Quick Actions for Parent -->
                    <div class="mt-6 flex flex-wrap gap-2">
                        <button class="px-3 py-1.5 bg-primary-600 hover:bg-primary-700 text-white text-sm rounded-full transition-colors flex items-center">
                            <x-heroicon-o-chat-bubble-left-right class="w-4 h-4 mr-1" />
                            ส่งข้อความหาครู
                        </button>
                        <button class="px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-sm rounded-full transition-colors flex items-center">
                            <x-heroicon-o-chart-bar class="w-4 h-4 mr-1" />
                            ดูรายงานผลการเรียน
                        </button>
                        <button class="px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-full transition-colors flex items-center">
                            <x-heroicon-o-calendar-days class="w-4 h-4 mr-1" />
                            ดูตารางเรียน
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
