@extends('layouts.frontend')

@section('title', $book->title)

@section('content')
<div class="bg-white">
    <div class="relative bg-gray-900">
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <div class="flex items-center mb-4">
                @if($book->subject)
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 mr-4">
                        {{ $book->subject->name }}
                    </span>
                @endif
                @if($book->team)
                    <span class="text-gray-300">by {{ $book->team->name }}</span>
                @endif
            </div>
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">{{ $book->title }}</h1>
            @if($book->author)
                <p class="mt-4 text-lg text-gray-300">Author: {{ $book->author }}</p>
            @endif
            @if($book->description)
                <p class="mt-6 text-xl text-gray-300 max-w-3xl">{{ strip_tags($book->description) }}</p>
            @endif
        </div>
    </div>

    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Book</h2>
                    <div class="prose max-w-none">
                        {!! $book->description !!}
                    </div>
                </div>

                @if($book->lessons && $book->lessons->count() > 0)
                    <div class="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Chapters</h2>
                        <div class="space-y-4">
                            @foreach($book->lessons as $lesson)
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                    <h3 class="font-semibold text-gray-900">{{ $lesson->title }}</h3>
                                    @if($lesson->description)
                                        <p class="text-sm text-gray-600 mt-1">{{ strip_tags($lesson->description) }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Book Information</h3>
                    
                    <div class="space-y-4">
                        @if($book->author)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Author</span>
                                <span class="text-sm font-medium text-gray-900">{{ $book->author }}</span>
                            </div>
                        @endif
                        
                        @if($book->lessons)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">Chapters</span>
                                <span class="text-sm font-medium text-gray-900">{{ $book->lessons->count() }}</span>
                            </div>
                        @endif
                        
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Last Updated</span>
                            <span class="text-sm font-medium text-gray-900">{{ $book->updated_at->format('M d, Y') }}</span>
                        </div>
                    </div>

                    @auth
                        <div class="mt-6">
                            <button class="w-full bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                Start Reading
                            </button>
                        </div>
                    @else
                        <div class="mt-6">
                            <a href="{{ route('login') }}" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-center block">
                                Login to Read
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
