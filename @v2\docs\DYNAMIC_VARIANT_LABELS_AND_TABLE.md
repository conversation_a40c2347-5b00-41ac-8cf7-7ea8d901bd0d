# Dynamic Variant Labels & Table Format Guide

## Overview

The product variants system has been enhanced with dynamic label updates and table-like formatting. When teams change option labels, the variant form fields update in real-time. This functionality is now available for both physical and digital products.

## Changes Made

### 1. Dynamic Label Updates

#### Live Label Synchronization:
```php
Forms\Components\TextInput::make('variant_option1_label')
    ->label('Option 1 Label')
    ->placeholder('e.g., Color, Material, Style')
    ->live(onBlur: true) // Real-time updates

Forms\Components\TextInput::make('option1')
    ->label(function (Forms\Get $get) {
        return $get('../../variant_option1_label') ?: 'Option 1';
    })
    ->required()
```

#### Dynamic Section Title:
```php
Forms\Components\Repeater::make('product_variants')
    ->label(function (Forms\Get $get) {
        $option1 = $get('variant_option1_label') ?: 'Option 1';
        $option2 = $get('variant_option2_label') ?: 'Option 2';
        return "Product Variants ({$option1} × {$option2})";
    })
```

### 2. Table-Like Format

#### Grid Layout for Variants:
```php
Forms\Components\Grid::make(7) // Physical products
    ->schema([
        Forms\Components\TextInput::make('option1')
            ->columnSpan(1),
        Forms\Components\TextInput::make('option2')
            ->columnSpan(1),
        Forms\Components\TextInput::make('option3')
            ->columnSpan(1),
        Forms\Components\TextInput::make('sku_suffix')
            ->columnSpan(1),
        Forms\Components\TextInput::make('price_adjustment')
            ->columnSpan(1),
        Forms\Components\TextInput::make('stock_quantity')
            ->columnSpan(1),
        Forms\Components\Toggle::make('is_available')
            ->columnSpan(1),
    ])
```

### 3. Digital Product Variants

#### Added to DigitalProductResource:
```php
Forms\Components\Section::make('Digital Product Variants')
    ->schema([
        // Label configuration
        Forms\Components\Group::make()
            ->schema([
                Forms\Components\TextInput::make('variant_option1_label')
                    ->placeholder('e.g., Version, Platform, License Type'),
                Forms\Components\TextInput::make('variant_option2_label')
                    ->placeholder('e.g., Platform, Language, Format'),
                Forms\Components\TextInput::make('variant_option3_label')
                    ->placeholder('e.g., Language, Edition, Support Level'),
            ]),
        
        // Variant repeater with 6-column grid (no stock quantity)
        Forms\Components\Repeater::make('product_variants')
            ->schema([
                Forms\Components\Grid::make(6)
                    ->schema([
                        // option1, option2, option3, sku_suffix, price_adjustment, is_available
                    ])
            ])
    ])
```

## Benefits

### 1. Real-Time User Experience

#### Immediate Feedback:
- **Label Changes**: Form fields update instantly when labels are changed
- **Context Awareness**: Section titles reflect current label configuration
- **Visual Consistency**: All variant fields use the same custom labels

#### Intuitive Interface:
- **Table Format**: Variants displayed in organized, spreadsheet-like layout
- **Compact View**: More variants visible at once
- **Easy Scanning**: Column-based layout for quick data entry

### 2. Unified System

#### Both Product Types:
- **Physical Products**: 7-column layout (includes stock quantity)
- **Digital Products**: 6-column layout (no stock needed)
- **Consistent Labels**: Same labeling system for both types
- **Shared Functionality**: Dynamic updates work for both

## Examples

### 1. Physical Product - Clothing Store

#### Label Configuration:
- **Option 1 Label**: "Color" → Form updates to show "Color" instead of "Option 1"
- **Option 2 Label**: "Size" → Form updates to show "Size" instead of "Option 2"
- **Section Title**: Updates to "Product Variants (Color × Size)"

#### Table Layout:
| Color | Size | Option 3 | SKU Suffix | Price +/- | Stock | Available |
|-------|------|----------|------------|-----------|-------|-----------|
| Black | M    |          | -BLK-M     | 0.00      | 25    | ✓         |
| Red   | L    |          | -RED-L     | 0.00      | 15    | ✓         |
| Blue  | XXL  |          | -BLU-XXL   | 5.00      | 8     | ✓         |

### 2. Digital Product - Software Company

#### Label Configuration:
- **Option 1 Label**: "License Type" → Form shows "License Type"
- **Option 2 Label**: "Platform" → Form shows "Platform"
- **Option 3 Label**: "Edition" → Form shows "Edition"
- **Section Title**: Updates to "Digital Product Variants (License Type × Platform)"

#### Table Layout:
| License Type | Platform      | Edition    | SKU Suffix      | Price +/- | Available |
|-------------|---------------|------------|-----------------|-----------|-----------|
| Personal    | Windows       | Standard   | -PERS-WIN-STD   | 0.00      | ✓         |
| Commercial  | Mac           | Pro        | -COMM-MAC-PRO   | 50.00     | ✓         |
| Enterprise  | Multi-Platform| Enterprise | -ENT-MULTI-ENT  | 200.00    | ✓         |

### 3. Digital Product - Online Course

#### Label Configuration:
- **Option 1 Label**: "Course Level" → Form shows "Course Level"
- **Option 2 Label**: "Language" → Form shows "Language"
- **Option 3 Label**: null (not used)
- **Section Title**: Updates to "Digital Product Variants (Course Level × Language)"

#### Table Layout:
| Course Level | Language | Option 3 | SKU Suffix  | Price +/- | Available |
|-------------|----------|----------|-------------|-----------|-----------|
| Basic       | English  |          | -BASIC-EN   | 0.00      | ✓         |
| Premium     | English  |          | -PREM-EN    | 30.00     | ✓         |
| Basic       | Spanish  |          | -BASIC-ES   | 0.00      | ✓         |

## Digital Product Variant Types

### 1. Software Applications

#### Common Labels:
- **License Type**: Personal, Commercial, Enterprise
- **Platform**: Windows, Mac, Linux, Multi-Platform
- **Edition**: Standard, Pro, Enterprise

#### Sample Variants:
```json
[
    {
        "option1": "Personal",
        "option2": "Windows", 
        "option3": "Standard",
        "sku_suffix": "-PERS-WIN-STD",
        "price_adjustment": 0.00,
        "is_available": true
    },
    {
        "option1": "Enterprise",
        "option2": "Multi-Platform",
        "option3": "Enterprise", 
        "sku_suffix": "-ENT-MULTI-ENT",
        "price_adjustment": 200.00,
        "is_available": true
    }
]
```

### 2. Online Courses

#### Common Labels:
- **Course Level**: Basic, Intermediate, Advanced, Premium
- **Language**: English, Spanish, French, German
- **Format**: Video, Text, Interactive

#### Sample Variants:
```json
[
    {
        "option1": "Premium",
        "option2": "English",
        "option3": "Interactive",
        "sku_suffix": "-PREM-EN-INT",
        "price_adjustment": 50.00,
        "is_available": true
    }
]
```

### 3. E-books

#### Common Labels:
- **Format**: PDF, EPUB, MOBI, Audiobook
- **Language**: English, Spanish, French
- **Edition**: Standard, Illustrated, Annotated

#### Sample Variants:
```json
[
    {
        "option1": "EPUB",
        "option2": "English",
        "option3": "Illustrated",
        "sku_suffix": "-EPUB-EN-ILL",
        "price_adjustment": 5.00,
        "is_available": true
    }
]
```

### 4. Audio Content

#### Common Labels:
- **Format**: MP3, FLAC, WAV
- **Quality**: 128kbps, 320kbps, Lossless
- **Version**: Original, Remastered, Extended

#### Sample Variants:
```json
[
    {
        "option1": "FLAC",
        "option2": "Lossless",
        "option3": "Remastered",
        "sku_suffix": "-FLAC-LOSS-REM",
        "price_adjustment": 10.00,
        "is_available": true
    }
]
```

## Technical Implementation

### Real-Time Updates

#### Live Binding:
```php
// Label input with live updates
->live(onBlur: true)

// Dynamic label function
->label(function (Forms\Get $get) {
    return $get('../../variant_option1_label') ?: 'Option 1';
})

// Dynamic section title
->label(function (Forms\Get $get) {
    $option1 = $get('variant_option1_label') ?: 'Option 1';
    $option2 = $get('variant_option2_label') ?: 'Option 2';
    return "Product Variants ({$option1} × {$option2})";
})
```

### Grid Layout

#### Physical Products (7 columns):
1. Option 1 (e.g., Color)
2. Option 2 (e.g., Size)
3. Option 3 (e.g., Material)
4. SKU Suffix
5. Price Adjustment
6. Stock Quantity
7. Available Toggle

#### Digital Products (6 columns):
1. Option 1 (e.g., License Type)
2. Option 2 (e.g., Platform)
3. Option 3 (e.g., Edition)
4. SKU Suffix
5. Price Adjustment
6. Available Toggle

### Database Structure

#### Same for Both Product Types:
```sql
-- Variant labels
variant_option1_label: "Color" / "License Type"
variant_option2_label: "Size" / "Platform"
variant_option3_label: "Material" / "Edition"

-- Variant data
product_variants: [
    {
        "option1": "Black" / "Personal",
        "option2": "L" / "Windows",
        "option3": "Cotton" / "Standard",
        "sku_suffix": "-BLK-L-COT" / "-PERS-WIN-STD",
        "price_adjustment": 0.00,
        "stock_quantity": 25, // Physical only
        "is_available": true
    }
]
```

## User Workflow

### Setting Up Dynamic Labels:
1. **Enter Option 1 Label**: Type "Color" → Form fields update to show "Color"
2. **Enter Option 2 Label**: Type "Size" → Form fields update to show "Size"
3. **Enter Option 3 Label**: Type "Material" → Form fields update to show "Material"
4. **Section Title Updates**: Automatically shows "Product Variants (Color × Size)"

### Adding Variants in Table Format:
1. **Click Add Variant**: New row appears in table format
2. **Fill Row Data**: Enter values across columns like a spreadsheet
3. **Visual Organization**: Each variant is a row, each attribute is a column
4. **Easy Comparison**: Compare variants side-by-side in table format

This enhanced system provides real-time feedback and organized table-like data entry for both physical and digital product variants!
