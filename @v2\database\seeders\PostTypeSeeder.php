<?php

namespace Database\Seeders;

use App\Models\Blog\Author;
use App\Models\Blog\Category;
use App\Models\Blog\Post;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PostTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $postTypes = config('post-types.types', []);

        // Get all teams (tenants)
        $teams = Team::all();

        foreach ($teams as $team) {
            $this->seedPostTypesForTeam($team, $postTypes);
        }
    }

    /**
     * Seed post types for a specific team
     */
    private function seedPostTypesForTeam(Team $team, array $postTypes): void
    {
        // Get team users
        $teamUsers = User::where('team_id', $team->id)->get();

        if ($teamUsers->isEmpty()) {
            return; // Skip if no users in team
        }

        foreach ($postTypes as $type => $config) {
            if (!($config['enabled'] ?? true)) {
                continue;
            }

            // Create categories for this type
            $categories = $this->createCategoriesForType($team, $type, $config);

            // Create authors for this type (use existing team users as authors)
            $authors = $this->createAuthorsForType($team, $teamUsers);

            // Create posts for this type
            $this->createPostsForType($team, $type, $config, $categories, $authors);
        }
    }

    /**
     * Create categories for a specific post type
     */
    private function createCategoriesForType(Team $team, string $type, array $config): array
    {
        $categoryNames = $this->getCategoryNamesForType($type);
        $categories = [];

        foreach ($categoryNames as $name) {
            $slug = Str::slug($name . '-' . $type . '-' . $team->slug);

            // Check if category already exists
            $existingCategory = Category::where('team_id', $team->id)
                ->where('type', $type)
                ->where('slug', $slug)
                ->first();

            if (!$existingCategory) {
                $category = Category::create([
                    'team_id' => $team->id,
                    'type' => $type,
                    'name' => $name,
                    'slug' => $slug,
                    'description' => "Category for {$config['name']} posts: {$name}",
                    'is_visible' => true,
                ]);

                $categories[] = $category;
            } else {
                $categories[] = $existingCategory;
            }
        }

        return $categories;
    }

    /**
     * Create authors for a team (convert users to authors)
     */
    private function createAuthorsForType(Team $team, $teamUsers): array
    {
        $authors = [];

        foreach ($teamUsers->take(3) as $user) { // Limit to 3 authors per team
            // Check if author already exists for this user
            $existingAuthor = Author::where('team_id', $team->id)
                ->where('email', $user->email)
                ->first();

            if (!$existingAuthor) {
                $author = Author::create([
                    'team_id' => $team->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'bio' => "Author bio for {$user->name} at {$team->name}",
                    'github_handle' => strtolower(str_replace(' ', '', $user->name)),
                    'twitter_handle' => '@' . strtolower(str_replace(' ', '', $user->name)),
                ]);

                $authors[] = $author;
            } else {
                $authors[] = $existingAuthor;
            }
        }

        return $authors;
    }

    /**
     * Create posts for a specific type
     */
    private function createPostsForType(Team $team, string $type, array $config, array $categories, array $authors): void
    {
        $postTitles = $this->getPostTitlesForType($type);

        foreach ($postTitles as $index => $title) {
            $author = $authors[array_rand($authors)];
            $category = $categories[array_rand($categories)];
            $daysAgo = ($index + 1) * 5; // Spread posts over time
            $slug = Str::slug($title . '-' . $type . '-' . $team->slug);

            // Check if post already exists
            $existingPost = Post::where('team_id', $team->id)
                ->where('type', $type)
                ->where('slug', $slug)
                ->first();

            if (!$existingPost) {
                Post::create([
                    'team_id' => $team->id,
                    'type' => $type,
                    'blog_author_id' => $author->id,
                    'blog_category_id' => $category->id,
                    'title' => $title,
                    'slug' => $slug,
                    'content' => $this->getContentForType($type, $title),
                    'published_at' => now()->subDays($daysAgo),
                    'seo_title' => $title,
                    'seo_description' => "SEO description for {$title}",
                ]);
            }
        }
    }

    /**
     * Get category names for each post type
     */
    private function getCategoryNamesForType(string $type): array
    {
        return match ($type) {
            'blog' => ['Technology', 'Education', 'Tutorials', 'Tips & Tricks'],
            'news' => ['Announcements', 'Updates', 'Events', 'School News'],
            'knowledge' => ['Documentation', 'Guides', 'FAQ', 'Best Practices'],
            default => ['General', 'Miscellaneous'],
        };
    }

    /**
     * Get post titles for each type
     */
    private function getPostTitlesForType(string $type): array
    {
        return match ($type) {
            'blog' => [
                'Getting Started with Our Platform',
                'Best Practices for Online Learning',
                'Tips for Effective Study Habits',
                'How to Use Our New Features',
                'Student Success Stories',
            ],
            'news' => [
                'New Course Offerings This Semester',
                'Upcoming School Events',
                'System Maintenance Scheduled',
                'Welcome New Faculty Members',
                'Academic Calendar Updates',
            ],
            'knowledge' => [
                'Platform User Guide',
                'Frequently Asked Questions',
                'Troubleshooting Common Issues',
                'Account Management Guide',
                'Technical Requirements',
            ],
            default => [
                'Sample Post Title',
                'Another Sample Post',
                'Third Sample Post',
            ],
        };
    }

    /**
     * Get content for each post type
     */
    private function getContentForType(string $type, string $title): string
    {
        $baseContent = "This is a sample {$type} post titled '{$title}'. ";

        return match ($type) {
            'blog' => $baseContent . "This blog post contains educational content, tips, and insights for our community. It covers various topics related to learning and development, providing valuable information for students and educators alike.",
            'news' => $baseContent . "This news article provides important updates and announcements for our school community. Stay informed about the latest developments, events, and changes that affect our students, faculty, and staff.",
            'knowledge' => $baseContent . "This knowledge base article provides detailed information and step-by-step instructions. It serves as a reference guide to help users understand and effectively use our platform's features and capabilities.",
            default => $baseContent . "This is general content for the post.",
        };
    }
}
