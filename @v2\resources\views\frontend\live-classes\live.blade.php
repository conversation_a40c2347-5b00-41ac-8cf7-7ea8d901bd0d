@extends('frontend.layouts.app')

@section('title', 'Live Classes Now')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Header Section -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('frontend.live-classes.index') }}" 
                       class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </a>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">
                            <i class="fas fa-circle text-red-500 mr-2 animate-pulse"></i>
                            Live Classes Now
                        </h1>
                        <p class="mt-2 text-gray-600">Join live classes happening right now</p>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <a href="{{ route('frontend.live-classes.upcoming') }}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-clock mr-2"></i>
                        Upcoming
                    </a>
                    <a href="{{ route('frontend.live-classes.index') }}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-list mr-2"></i>
                        All Classes
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        @if($liveClasses->count() > 0)
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                @foreach($liveClasses as $liveVideo)
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow border-l-4 border-red-500">
                        <div class="p-6">
                            <!-- Live Badge -->
                            <div class="flex items-center justify-between mb-4">
                                <span class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                                    <i class="fas fa-circle mr-1 animate-pulse"></i>
                                    LIVE NOW
                                </span>
                                <span class="text-sm text-gray-500">
                                    Started {{ $liveVideo->actual_start_time->diffForHumans() }}
                                </span>
                            </div>

                            <!-- Video Thumbnail -->
                            <div class="relative h-48 bg-gray-900 rounded-lg mb-4">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <i class="fas fa-video text-6xl text-gray-500 opacity-50"></i>
                                </div>
                                <div class="absolute top-4 left-4 bg-red-600 text-white px-2 py-1 rounded text-sm font-medium">
                                    <i class="fas fa-circle mr-1 animate-pulse"></i>
                                    LIVE
                                </div>
                                <!-- Play overlay -->
                                <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity bg-black bg-opacity-50 rounded-lg">
                                    <a href="{{ route('frontend.live-classes.join', $liveVideo) }}" 
                                       class="bg-red-600 hover:bg-red-700 text-white p-4 rounded-full transition-colors">
                                        <i class="fas fa-play text-2xl"></i>
                                    </a>
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="space-y-3">
                                <h3 class="text-xl font-semibold text-gray-900">{{ $liveVideo->title }}</h3>
                                
                                <div class="flex items-center space-x-4 text-sm text-gray-600">
                                    <span>
                                        <i class="fas fa-user mr-1"></i>
                                        {{ $liveVideo->teacher->name }}
                                    </span>
                                    @if($liveVideo->liveable)
                                        <span>
                                            <i class="fas fa-book mr-1"></i>
                                            {{ $liveVideo->liveable->title }}
                                        </span>
                                    @endif
                                </div>

                                @if($liveVideo->description)
                                    <p class="text-gray-600 text-sm line-clamp-2">{{ $liveVideo->description }}</p>
                                @endif

                                <!-- Time Info -->
                                <div class="flex items-center justify-between text-sm text-gray-500">
                                    <span>
                                        <i class="fas fa-clock mr-1"></i>
                                        Started at {{ $liveVideo->actual_start_time->format('g:i A') }}
                                    </span>
                                    <span>
                                        <i class="fas fa-eye mr-1"></i>
                                        <span id="viewer-count-{{ $liveVideo->id }}">-- viewers</span>
                                    </span>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex space-x-3 pt-4">
                                    <a href="{{ route('frontend.live-classes.join', $liveVideo) }}" 
                                       class="flex-1 bg-red-600 hover:bg-red-700 text-white text-center py-3 px-4 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-play mr-2"></i>
                                        Join Live Class
                                    </a>
                                    <a href="{{ route('frontend.live-classes.show', $liveVideo) }}" 
                                       class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-3 px-4 rounded-lg transition-colors">
                                        <i class="fas fa-info"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Auto-refresh notice -->
            <div class="mt-8 text-center">
                <p class="text-sm text-gray-500">
                    <i class="fas fa-sync-alt mr-1"></i>
                    This page refreshes automatically every 30 seconds
                </p>
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="relative">
                    <i class="fas fa-video text-6xl text-gray-400 mb-4"></i>
                    <div class="absolute -top-2 -right-2 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Live Classes Right Now</h3>
                <p class="text-gray-600 mb-6">There are no live classes happening at the moment.</p>
                <div class="flex justify-center space-x-4">
                    <a href="{{ route('frontend.live-classes.upcoming') }}" 
                       class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-clock mr-2"></i>
                        View Upcoming Classes
                    </a>
                    <a href="{{ route('frontend.live-classes.index') }}" 
                       class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-list mr-2"></i>
                        View All Classes
                    </a>
                </div>
            </div>
        @endif
    </div>
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection

@push('scripts')
<script>
// Update viewer counts for all live videos
async function updateViewerCounts() {
    const liveVideoIds = @json($liveClasses->pluck('id'));
    
    for (const id of liveVideoIds) {
        try {
            const response = await fetch(`/api/live-videos/${id}/viewer-count`);
            const data = await response.json();
            
            const element = document.getElementById(`viewer-count-${id}`);
            if (element) {
                element.textContent = `${data.viewer_count} viewers`;
            }
        } catch (error) {
            console.error(`Error fetching viewer count for video ${id}:`, error);
        }
    }
}

// Initial load
updateViewerCounts();

// Update viewer counts every 10 seconds
setInterval(updateViewerCounts, 10000);

// Auto-refresh page every 30 seconds to check for new live classes
setInterval(() => {
    window.location.reload();
}, 30000);

// Add visual feedback for join buttons
document.querySelectorAll('a[href*="join"]').forEach(button => {
    button.addEventListener('click', function() {
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Joining...';
        this.classList.add('opacity-75', 'cursor-not-allowed');
    });
});
</script>
@endpush
