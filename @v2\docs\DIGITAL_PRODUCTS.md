# Digital Products Documentation

## Overview

The product system now supports both **Physical** and **Digital** products with comprehensive features for managing digital content, licensing, and access control.

## Product Types

### Physical Products
- Traditional inventory-based products
- Require shipping and handling
- Limited quantity tracking
- Physical dimensions and weight

### Digital Products
- Downloadable digital content
- No shipping required
- Unlimited quantity (999999)
- File-based delivery

## Digital Product Features

### Supported File Types
- **Documents**: PDF, DOCX, EPUB
- **Spreadsheets**: XLSX
- **Presentations**: PPTX
- **Media**: MP3, MP4
- **Archives**: ZIP
- **Software**: EXE, APK

### Digital Rights Management
- **Download Limits**: Control how many times files can be downloaded
- **Expiry Dates**: Set download link expiration (in days)
- **License Keys**: Generate and manage software licenses
- **License Terms**: Custom terms and conditions

### Content Management
- **Version Tracking**: Track updates to digital content
- **File Size Tracking**: Monitor total download size
- **System Requirements**: Specify technical requirements for software
- **Multiple Formats**: Support various file types per product

## Creating Digital Products

### Basic Setup
1. Set **Product Type** to "Digital"
2. Fill in basic product information (name, description, price)
3. Add product images for marketing

### Digital Content Configuration
1. **Digital Description**: Detailed description specific to digital content
2. **Primary Format**: Select the main file format (PDF, EPUB, etc.)
3. **Available File Types**: Check all formats included
4. **Upload Digital Files**: Upload the actual files customers will receive

### Access Control
1. **Download Limit**: Set maximum downloads per purchase (leave empty for unlimited)
2. **Download Expiry**: Set expiration in days after purchase
3. **License Key**: Enable if product requires license generation
4. **License Terms**: Add custom licensing terms

### Technical Information
1. **File Size**: Total size of all digital files in MB
2. **Version**: Version number for tracking updates
3. **System Requirements**: Technical requirements for software/apps
4. **Last Updated**: When digital content was last modified

## Sample Digital Products

The system includes sample digital products:

1. **Complete Laravel Development Guide** (PDF/EPUB eBook)
2. **Excel Mastery Templates Pack** (XLSX templates with license)
3. **PowerPoint Presentation Toolkit** (PPTX templates)
4. **Digital Marketing Masterclass** (MP4 video course)

## Product Management Features

### Filtering & Search
- Filter by product type (Physical/Digital)
- Filter by digital format
- Filter by license requirement
- Search across all product fields

### Bulk Actions
- Convert multiple products from physical to digital
- Convert multiple products from digital to physical
- Bulk delete with confirmation

### Individual Actions
- Duplicate product as different type
- Edit product details
- Manage digital files

### Analytics
- Product type distribution
- Digital storage usage
- Average file sizes
- License key requirements

## File Upload Configuration

### Accepted MIME Types
```php
'application/pdf',                    // PDF
'application/epub+zip',               // EPUB
'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',       // XLSX
'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
'audio/mpeg',                         // MP3
'video/mp4',                          // MP4
'application/zip',                    // ZIP
'application/x-msdownload',           // EXE
'application/vnd.android.package-archive', // APK
```

### File Limits
- Maximum 10 files per product
- File size tracking in MB
- Automatic file type validation

## Database Schema

### New Fields Added
- `product_type`: enum('physical', 'digital')
- `digital_description`: text
- `digital_file_types`: json
- `download_limit`: integer
- `download_expiry_days`: integer
- `requires_license_key`: boolean
- `license_terms`: text
- `file_size_mb`: decimal(8,2)
- `digital_format`: string
- `system_requirements`: json
- `version`: string
- `last_updated`: date

## API Integration

### Model Methods
```php
// Check product type
$product->isDigital()
$product->isPhysical()

// Get format information
$product->getDigitalFormatIcon()
$product->getFormattedFileSize()

// Validate downloads
$product->isDownloadValid($purchaseDate)

// Get available formats
Product::getDigitalFormats()
```

## Best Practices

### For Digital Products
1. Always set unlimited quantity (999999)
2. Disable shipping requirements
3. Set appropriate download limits
4. Include comprehensive system requirements for software
5. Keep file sizes reasonable for download

### For Physical Products
1. Set realistic inventory quantities
2. Enable shipping requirements
3. Include physical dimensions and weight
4. Set appropriate security stock levels

## Security Considerations

1. **File Access Control**: Digital files should be protected and only accessible to purchasers
2. **License Validation**: Implement license key validation for software products
3. **Download Tracking**: Monitor download attempts and enforce limits
4. **Expiry Enforcement**: Automatically disable expired download links

## Future Enhancements

Potential future features:
- Automatic license key generation
- Download analytics and tracking
- Digital watermarking
- Cloud storage integration
- Streaming for media files
- Progressive download for large files
