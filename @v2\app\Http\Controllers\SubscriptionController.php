<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use TomatoPHP\FilamentSubscriptions\Models\Plan;
use TomatoPHP\FilamentSubscriptions\Models\Subscription;
use TomatoPHP\FilamentPayments\Models\Payment;
use App\Models\User;
use App\Models\Team;

class SubscriptionController extends Controller
{
    public function checkout(Request $request, $planSlug)
    {
        $plan = Plan::where('slug', $planSlug)->where('is_active', true)->firstOrFail();
        $user = Auth::user();

        // Check if user already has an active subscription
        $existingSubscription = $user->subscriptions()
            ->where('starts_at', '<=', now())
            ->where(function ($query) {
                $query->whereNull('ends_at')
                      ->orWhere('ends_at', '>', now());
            })
            ->where(function ($query) {
                $query->whereNull('canceled_at')
                      ->orWhere('canceled_at', '>', now());
            })
            ->first();

        return view('subscription.checkout', compact('plan', 'user', 'existingSubscription'));
    }

    public function process(Request $request)
    {
        $request->validate([
            'plan_slug' => 'required|exists:plans,slug',
            'payment_method' => 'required|in:paypal,bank-transfer,promptpay',
        ]);

        $plan = Plan::where('slug', $request->plan_slug)->firstOrFail();
        $user = Auth::user();

        // Create subscription
        $subscription = Subscription::create([
            'subscriber_id' => $user->id,
            'subscriber_type' => User::class,
            'plan_id' => $plan->id,
            'name' => $plan->name,
            'slug' => $plan->slug . '-' . $user->id . '-' . time(),
            'description' => $plan->description,
            'trial_ends_at' => $this->calculateTrialEnd($plan),
            'starts_at' => $this->calculateStartDate($plan),
            'ends_at' => $this->calculateEndDate($plan),
        ]);

        // Create payment record
        $payment = Payment::create([
            'user_id' => $user->id,
            'amount' => $plan->price,
            'currency' => $plan->currency,
            'status' => 'pending',
            'method' => $request->payment_method,
            'description' => 'Subscription payment for ' . $plan->name,
            'metadata' => json_encode([
                'subscription_id' => $subscription->id,
                'plan_slug' => $plan->slug,
            ]),
        ]);

        // Process payment based on method
        switch ($request->payment_method) {
            case 'paypal':
                return $this->processPaypalPayment($payment, $subscription);
            case 'bank-transfer':
                return $this->processBankTransferPayment($payment, $subscription);
            case 'promptpay':
                return $this->processPromptPayPayment($payment, $subscription);
            default:
                return redirect()->back()->with('error', 'Invalid payment method');
        }
    }

    private function calculateTrialEnd(Plan $plan): ?string
    {
        if ($plan->trial_period > 0) {
            return now()->add($plan->trial_interval, $plan->trial_period)->toDateTimeString();
        }
        return null;
    }

    private function calculateStartDate(Plan $plan): string
    {
        if ($plan->trial_period > 0) {
            return now()->add($plan->trial_interval, $plan->trial_period)->toDateTimeString();
        }
        return now()->toDateTimeString();
    }

    private function calculateEndDate(Plan $plan): string
    {
        $startDate = $this->calculateStartDate($plan);
        return \Carbon\Carbon::parse($startDate)
            ->add($plan->invoice_interval, $plan->invoice_period)
            ->toDateTimeString();
    }

    private function processPaypalPayment(Payment $payment, Subscription $subscription)
    {
        // Integrate with PayPal
        // For now, redirect to a payment form
        return redirect()->route('payment.paypal.form', $payment->id);
    }

    private function processBankTransferPayment(Payment $payment, Subscription $subscription)
    {
        $driver = new \App\Services\PaymentDrivers\BankTransferDriver();
        $result = $driver->pay($payment);

        if ($result['success']) {
            return redirect($result['payment_url']);
        }

        return redirect()->back()->with('error', 'Failed to process bank transfer payment');
    }

    private function processPromptPayPayment(Payment $payment, Subscription $subscription)
    {
        $driver = new \App\Services\PaymentDrivers\PromptPayDriver();
        $result = $driver->pay($payment);

        if ($result['success']) {
            return redirect($result['payment_url']);
        }

        return redirect()->back()->with('error', 'Failed to process PromptPay payment');
    }

    public function success(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $payment = Payment::findOrFail($paymentId);

        // Mark payment as completed
        $payment->update(['status' => 'completed']);

        // Activate subscription
        $subscriptionId = json_decode($payment->metadata, true)['subscription_id'];
        $subscription = Subscription::findOrFail($subscriptionId);
        
        return view('subscription.success', compact('payment', 'subscription'));
    }

    public function cancel(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $payment = Payment::findOrFail($paymentId);

        // Mark payment as cancelled
        $payment->update(['status' => 'cancelled']);

        return view('subscription.cancel', compact('payment'));
    }

    public function bankTransferInstructions($paymentUuid)
    {
        $payment = Payment::where('uuid', $paymentUuid)->firstOrFail();
        $bankDetails = json_decode($payment->gateway_response, true);

        return view('payment.bank-transfer', compact('payment', 'bankDetails'));
    }

    public function promptPayQr($paymentUuid)
    {
        $payment = Payment::where('uuid', $paymentUuid)->firstOrFail();
        $promptPayDetails = json_decode($payment->gateway_response, true);

        return view('payment.promptpay', compact('payment', 'promptPayDetails'));
    }
}
