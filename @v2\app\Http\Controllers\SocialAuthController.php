<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;
use Exception;

class SocialAuthController extends Controller
{
    /**
     * Redirect to social provider
     */
    public function redirectToProvider($provider)
    {
        $validProviders = ['google', 'microsoft', 'apple', 'line'];

        if (!in_array($provider, $validProviders)) {
            return redirect()->route('login')->with('error', 'Invalid social provider');
        }

        // Check if provider is enabled
        $providerConfig = config("services.social_auth.providers.{$provider}");
        if (!$providerConfig || !$providerConfig['enabled']) {
            return redirect()->route('login')->with('error', ucfirst($provider) . ' login is currently disabled');
        }

        try {
            return Socialite::driver($provider)->redirect();
        } catch (Exception $e) {
            \Log::error("Social auth redirect error for {$provider}: " . $e->getMessage());
            return redirect()->route('login')->with('error', 'Unable to connect to ' . ucfirst($provider) . ': ' . $e->getMessage());
        }
    }

    /**
     * Handle social provider callback
     */
    public function handleProviderCallback($provider)
    {
        $validProviders = ['google', 'microsoft', 'apple', 'line'];

        if (!in_array($provider, $validProviders)) {
            return redirect()->route('login')->with('error', 'Invalid social provider');
        }

        // Check if provider is enabled
        $providerConfig = config("services.social_auth.providers.{$provider}");
        if (!$providerConfig || !$providerConfig['enabled']) {
            return redirect()->route('login')->with('error', ucfirst($provider) . ' login is currently disabled');
        }

        try {
            $socialUser = Socialite::driver($provider)->user();

            // Check if this is a connection intent (user is already logged in)
            if (session('social_connect_intent') && Auth::check()) {
                return app(SocialAccountController::class)->callback($provider);
            }

            // Check if user already exists with this social account
            $existingUser = User::where('email', $socialUser->getEmail())->first();

            if ($existingUser) {
                // Update social provider info
                $this->updateSocialInfo($existingUser, $provider, $socialUser);

                // Also add to social_accounts if not already there
                if (!$existingUser->hasSocialAccount($provider)) {
                    $existingUser->addSocialAccount($provider, [
                        'id' => $socialUser->getId(),
                        'email' => $socialUser->getEmail(),
                        'name' => $socialUser->getName(),
                        'avatar' => $socialUser->getAvatar(),
                    ]);
                }

                Auth::login($existingUser);

                // Check if profile needs completion
                if ($existingUser->needsProfileCompletion()) {
                    return redirect()->route('profile.edit')->with('info', 'Please complete your profile to continue.');
                }

                return redirect($existingUser->getDashboardRoute())->with('success', 'Welcome back!');
            } else {
                // Create new user
                $user = $this->createUserFromSocial($provider, $socialUser);
                Auth::login($user);

                // New users always need to complete profile
                return redirect()->route('profile.edit')->with('success', 'Account created successfully! Please complete your profile.');
            }
            
        } catch (Exception $e) {
            \Log::error("Social auth callback error for {$provider}: " . $e->getMessage());
            return redirect()->route('login')->with('error', 'Authentication failed: ' . $e->getMessage());
        }
    }

    /**
     * Create user from social provider data
     */
    private function createUserFromSocial($provider, $socialUser)
    {
        $user = User::create([
            'name' => $socialUser->getName() ?? $socialUser->getNickname() ?? 'User',
            'email' => $socialUser->getEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make(Str::random(24)), // Random password for social users
            'avatar' => $socialUser->getAvatar(),
            'provider' => $provider,
            'provider_id' => $socialUser->getId(),
            'provider_token' => $socialUser->token,
            'provider_refresh_token' => $socialUser->refreshToken ?? null,
        ]);

        // Assign default role (student) for social login users
        // They can change this in their profile later
        $user->assignRole('student');

        // Add the social account to the user
        $user->addSocialAccount($provider, [
            'id' => $socialUser->getId(),
            'email' => $socialUser->getEmail(),
            'name' => $socialUser->getName(),
            'avatar' => $socialUser->getAvatar(),
        ]);

        return $user;
    }

    /**
     * Update existing user's social provider info
     */
    private function updateSocialInfo($user, $provider, $socialUser)
    {
        $user->update([
            'provider' => $provider,
            'provider_id' => $socialUser->getId(),
            'provider_token' => $socialUser->token,
            'provider_refresh_token' => $socialUser->refreshToken ?? null,
            'avatar' => $socialUser->getAvatar() ?? $user->avatar,
        ]);
    }

    /**
     * Handle phone authentication
     */
    public function sendPhoneOtp(Request $request)
    {
        $request->validate([
            'phone' => 'required|string|min:10|max:15',
            'country_code' => 'required|string'
        ]);

        $fullPhone = $request->country_code . $request->phone;
        
        // Generate OTP
        $otp = rand(100000, 999999);
        
        // Store OTP in session (in production, use cache or database)
        session([
            'phone_otp' => $otp,
            'phone_number' => $fullPhone,
            'otp_expires_at' => now()->addMinutes(5)
        ]);

        try {
            // Send OTP via SMS (implement based on your SMS provider)
            $this->sendSMS($fullPhone, $otp);
            
            return response()->json([
                'success' => true,
                'message' => 'OTP sent successfully',
                'phone' => $fullPhone
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send OTP. Please try again.'
            ], 500);
        }
    }

    /**
     * Verify phone OTP
     */
    public function verifyPhoneOtp(Request $request)
    {
        $request->validate([
            'otp' => 'required|string|size:6',
            'phone' => 'required|string'
        ]);

        $sessionOtp = session('phone_otp');
        $sessionPhone = session('phone_number');
        $expiresAt = session('otp_expires_at');

        if (!$sessionOtp || !$sessionPhone || !$expiresAt) {
            return response()->json([
                'success' => false,
                'message' => 'OTP session expired. Please request a new OTP.'
            ], 400);
        }

        if (now()->gt($expiresAt)) {
            return response()->json([
                'success' => false,
                'message' => 'OTP has expired. Please request a new OTP.'
            ], 400);
        }

        if ($request->otp !== (string)$sessionOtp) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid OTP. Please try again.'
            ], 400);
        }

        if ($request->phone !== $sessionPhone) {
            return response()->json([
                'success' => false,
                'message' => 'Phone number mismatch.'
            ], 400);
        }

        // OTP verified successfully
        // Check if user exists with this phone number
        $user = User::where('phone', $sessionPhone)->first();

        if ($user) {
            // Login existing user
            Auth::login($user);

            // Check if profile needs completion
            if ($user->needsProfileCompletion()) {
                $redirectUrl = route('profile.edit');
                $message = 'Welcome back! Please complete your profile.';
            } else {
                $redirectUrl = $user->getDashboardRoute();
                $message = 'Welcome back!';
            }
        } else {
            // Create new user with phone
            $user = User::create([
                'name' => 'User', // Will be updated in profile
                'email' => null, // Phone-only registration
                'phone' => $sessionPhone,
                'phone_verified_at' => now(),
                'password' => Hash::make(Str::random(24)),
            ]);

            // Assign default role (student) for phone registration users
            $user->assignRole('student');

            Auth::login($user);
            $redirectUrl = route('profile.edit');
            $message = 'Account created successfully! Please complete your profile.';
        }

        // Clear OTP session
        session()->forget(['phone_otp', 'phone_number', 'otp_expires_at']);

        return response()->json([
            'success' => true,
            'message' => $message,
            'redirect' => $redirectUrl
        ]);
    }

    /**
     * Send SMS (implement based on your SMS provider)
     */
    private function sendSMS($phone, $otp)
    {
        $provider = config('services.sms.provider', 'twilio');

        switch ($provider) {
            case 'twilio':
                return $this->sendTwilioSMS($phone, $otp);
            case 'aws':
                return $this->sendAWSSMS($phone, $otp);
            case 'firebase':
                return $this->sendFirebaseSMS($phone, $otp);
            default:
                // For development, just log the OTP
                \Log::info("OTP for {$phone}: {$otp}");
                return true;
        }
    }

    /**
     * Send SMS via Twilio
     */
    private function sendTwilioSMS($phone, $otp)
    {
        $sid = config('services.twilio.sid');
        $token = config('services.twilio.token');
        $from = config('services.twilio.phone');

        if (!$sid || !$token || !$from) {
            throw new Exception('Twilio configuration missing');
        }

        $twilio = new \Twilio\Rest\Client($sid, $token);
        
        $message = $twilio->messages->create(
            $phone,
            [
                'from' => $from,
                'body' => "Your EduNest verification code is: {$otp}. This code will expire in 5 minutes."
            ]
        );

        return $message->sid;
    }

    /**
     * Send SMS via AWS SNS
     */
    private function sendAWSSMS($phone, $otp)
    {
        // Implement AWS SNS SMS sending
        // This requires AWS SDK for PHP
        throw new Exception('AWS SMS not implemented yet');
    }

    /**
     * Send SMS via Firebase
     */
    private function sendFirebaseSMS($phone, $otp)
    {
        // Implement Firebase SMS sending
        // This requires Firebase Admin SDK
        throw new Exception('Firebase SMS not implemented yet');
    }
}
