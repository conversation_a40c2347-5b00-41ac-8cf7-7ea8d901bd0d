<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\Request;

class CourseController extends Controller
{
    public function index()
    {
        $courses = Course::where('is_active', true)
            ->with(['subject', 'team'])
            ->paginate(12);

        return view('frontend.courses.index', compact('courses'));
    }

    public function show(Course $course)
    {
        // Check if course is active
        if (!$course->is_active) {
            abort(404);
        }

        // Load relationships
        $course->load(['subject', 'team', 'lessons' => function ($query) {
            $query->where('is_active', true)->orderBy('sort_order');
        }]);

        return view('frontend.courses.show', compact('course'));
    }
}
