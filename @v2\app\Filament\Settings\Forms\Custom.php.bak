<?php
 
namespace App\Filament\Settings\Forms;
 
use Filament\Forms\Components\Tabs\Tab;
use CWSPS154\AppSettings\AppSettingsPlugin;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;

 
class Custom
{
    /**
     * @return Tab
     */
    public static function getTab(): Tab
    {
        return Tab::make('custom')
                    ->label(__('Custom'))
                    ->icon('heroicon-o-computer-desktop')
                    ->schema(self::getFields())
                    ->columns()
                    ->statePath('custom')
                    ->visible(true);
    }
 
    public static function canAccess(): bool
    {
        // Allow access for all authenticated users
        // You can customize this based on your permission requirements
        return true;
    }

    public static function getFields(): array
    {
        return [
            self::getAppSection(),
        ];
    }

    public static function getAppSection(): Section
    {
        return Section::make('System')
            ->label(__('app-settings::app-settings.form.system'))
            ->schema([
                TextInput::make('app_name')
                    ->label(__('app-settings::app-settings.form.field.app.name'))
                    ->maxLength(255)
                    ->required()
                    ->columnSpanFull(),
                Grid::make()->schema([
                    FileUpload::make('app_logo')
                        ->label(fn () => __('app-settings::app-settings.form.field.app.logo'))
                        ->image()
                        ->directory('assets')
                        ->visibility('public')
                        ->moveFiles()
                        ->imageEditor()
                        ->getUploadedFileNameForStorageUsing(fn () => 'site_logo.png'),
                    FileUpload::make('app_dark_logo')
                        ->label(fn () => __('app-settings::app-settings.form.field.app.dark-logo'))
                        ->image()
                        ->directory('assets')
                        ->visibility('public')
                        ->moveFiles()
                        ->imageEditor()
                        ->getUploadedFileNameForStorageUsing(fn () => 'site_dark_logo.png'),
                    FileUpload::make('app_favicon')
                        ->label(fn () => __('app-settings::app-settings.form.field.app.favicon'))
                        ->image()
                        ->directory('assets')
                        ->visibility('public')
                        ->moveFiles()
                        ->getUploadedFileNameForStorageUsing(fn () => 'site_favicon.ico')
                        ->acceptedFileTypes(['image/x-icon', 'image/vnd.microsoft.icon']),
                ])->columns(3),
                TextInput::make('support_email')
                    ->label(__('app-settings::app-settings.form.field.app.support.email'))
                    ->prefixIcon('heroicon-o-envelope'),
                TextInput::make('support_phone_1')
                    ->label(__('app-settings::app-settings.form.field.app.support.phone.1'))
                    ->tel()
                    ->prefixIcon('heroicon-o-phone')
                    ->placeholder('Enter phone number')
                    ->maxLength(20),
                TextInput::make('support_phone_2')
                    ->label(__('app-settings::app-settings.form.field.app.support.phone.2'))
                    ->tel()
                    ->prefixIcon('heroicon-o-phone')
                    ->placeholder('Enter phone number')
                    ->maxLength(20),
            ])
            ->columns(3)->collapsible();
    }

    public static function getAppAdditionalFields(): array
    {
        return AppSettingsPlugin::$appAdditionalFields;
    }

    public static function getSortOrder(): int
    {
        return 1;
    }
}