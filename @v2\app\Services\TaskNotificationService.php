<?php

namespace App\Services;

use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TaskNotificationService
{
    /**
     * Send notifications for a task alert
     */
    public function sendTaskAlert(Task $task): void
    {
        if (!$task->has_alert || !$task->shouldTriggerAlert()) {
            return;
        }

        $user = $task->assignedUser ?? $task->user;
        
        if (!$user) {
            return;
        }

        $notificationMethods = $task->notification_methods;

        foreach ($notificationMethods as $method) {
            try {
                match ($method) {
                    'sound' => $this->sendSoundAlert($task, $user),
                    'email' => $this->sendEmailAlert($task, $user),
                    'line' => $this->sendLineAlert($task, $user),
                    default => null,
                };
            } catch (\Exception $e) {
                Log::error("Failed to send {$method} alert for task {$task->id}: " . $e->getMessage());
            }
        }
    }

    /**
     * Send sound alert (browser notification)
     */
    private function sendSoundAlert(Task $task, User $user): void
    {
        // This would typically be handled by the frontend JavaScript
        // We can store the alert in a session or cache for the frontend to pick up

        $alertData = [
            'type' => 'task_alert',
            'task_id' => $task->id,
            'title' => $task->title,
            'message' => "Task '{$task->title}' is due at {$task->start_datetime->format('H:i')}",
            'sound' => true,
            'timestamp' => now()->toISOString(),
        ];

        // Store in cache for the user to be picked up by frontend
        $cacheKey = "task_alerts_{$user->id}";
        $existingAlerts = cache()->get($cacheKey, []);
        $existingAlerts[] = $alertData;
        cache()->put($cacheKey, $existingAlerts, now()->addMinutes(5));

        Log::info("Sound alert queued for user {$user->id} for task {$task->id}");
    }

    /**
     * Send email alert
     */
    private function sendEmailAlert(Task $task, User $user): void
    {
        $subject = "Task Alert: {$task->title}";
        $message = $this->buildEmailMessage($task);

        Mail::raw($message, function ($mail) use ($user, $subject) {
            $mail->to($user->email)
                 ->subject($subject);
        });

        Log::info("Email alert sent to {$user->email} for task {$task->id}");
    }

    /**
     * Send LINE notification
     */
    private function sendLineAlert(Task $task, User $user): void
    {
        if (!$task->line_user_id) {
            Log::warning("No LINE user ID set for task {$task->id}");
            return;
        }

        $lineToken = config('services.line.channel_access_token');
        
        if (!$lineToken) {
            Log::warning("LINE channel access token not configured");
            return;
        }

        $message = $this->buildLineMessage($task);

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $lineToken,
            'Content-Type' => 'application/json',
        ])->post('https://api.line.me/v2/bot/message/push', [
            'to' => $task->line_user_id,
            'messages' => [
                [
                    'type' => 'text',
                    'text' => $message,
                ]
            ]
        ]);

        if ($response->successful()) {
            Log::info("LINE alert sent to {$task->line_user_id} for task {$task->id}");
        } else {
            Log::error("Failed to send LINE alert for task {$task->id}: " . $response->body());
        }
    }

    /**
     * Build email message content
     */
    private function buildEmailMessage(Task $task): string
    {
        $message = "Task Alert\n\n";
        $message .= "Title: {$task->title}\n";
        $message .= "Priority: " . ucfirst($task->priority) . "\n";
        $message .= "Start Time: {$task->start_datetime->format('Y-m-d H:i')}\n";
        
        if ($task->end_datetime) {
            $message .= "End Time: {$task->end_datetime->format('Y-m-d H:i')}\n";
        }
        
        if ($task->location) {
            $message .= "Location: {$task->location}\n";
        }
        
        if ($task->description) {
            $message .= "\nDescription:\n{$task->description}\n";
        }
        
        if ($task->notes) {
            $message .= "\nNotes:\n{$task->notes}\n";
        }

        $message .= "\n---\n";
        $message .= "This is an automated alert from your task management system.";

        return $message;
    }

    /**
     * Build LINE message content
     */
    private function buildLineMessage(Task $task): string
    {
        $message = "🔔 Task Alert\n\n";
        $message .= "📋 {$task->title}\n";
        $message .= "⏰ {$task->start_datetime->format('M j, Y H:i')}\n";
        
        if ($task->priority === 'high' || $task->priority === 'urgent') {
            $message .= "🔥 Priority: " . ucfirst($task->priority) . "\n";
        }
        
        if ($task->location) {
            $message .= "📍 {$task->location}\n";
        }

        return $message;
    }

    /**
     * Check for pending alerts and send notifications
     */
    public function processPendingAlerts(): void
    {
        $pendingTasks = Task::where('has_alert', true)
            ->where('status', '!=', 'completed')
            ->where('status', '!=', 'cancelled')
            ->get();

        foreach ($pendingTasks as $task) {
            if ($task->shouldTriggerAlert()) {
                $this->sendTaskAlert($task);
            }
        }
    }

    /**
     * Get pending browser alerts for a user
     */
    public function getPendingBrowserAlerts(User $user): array
    {
        $alerts = [];

        // For file cache, we'll use a different approach
        // Store alerts in a single cache key per user
        $cacheKey = "task_alerts_{$user->id}";
        $userAlerts = cache()->get($cacheKey, []);

        if (!empty($userAlerts)) {
            $alerts = $userAlerts;
            cache()->forget($cacheKey); // Remove after retrieving
        }

        return $alerts;
    }
}
