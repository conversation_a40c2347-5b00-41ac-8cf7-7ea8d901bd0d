<?php

namespace App\Filament\Resources\CourseResource\Pages;

use App\Filament\Resources\CourseResource;
use App\Filament\Resources\LiveVideoResource;
use App\Models\LiveVideo;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms;
use Filament\Notifications\Notification;

class EditCourse extends EditRecord
{
    protected static string $resource = CourseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('start_live_lesson')
                ->label('Start Live Lesson')
                ->icon('heroicon-o-video-camera')
                ->color('success')
                ->form([
                    Forms\Components\TextInput::make('title')
                        ->label('Live Lesson Title')
                        ->required()
                        ->default(fn () => $this->record->title . ' - Live Session'),

                    Forms\Components\Textarea::make('description')
                        ->label('Description')
                        ->rows(3),

                    Forms\Components\DateTimePicker::make('scheduled_start_time')
                        ->label('Scheduled Start Time')
                        ->required()
                        ->default(now()->addMinutes(5)),

                    Forms\Components\DateTimePicker::make('scheduled_end_time')
                        ->label('Scheduled End Time')
                        ->required()
                        ->default(now()->addHours(1)),

                    Forms\Components\Toggle::make('is_recording_enabled')
                        ->label('Enable Recording')
                        ->default(true),

                    Forms\Components\Toggle::make('is_public')
                        ->label('Public Access')
                        ->default(false),
                ])
                ->action(function (array $data) {
                    $liveVideo = LiveVideo::create([
                        'team_id' => $this->record->team_id,
                        'user_id' => auth()->id(),
                        'liveable_type' => get_class($this->record),
                        'liveable_id' => $this->record->id,
                        'title' => $data['title'],
                        'description' => $data['description'],
                        'scheduled_start_time' => $data['scheduled_start_time'],
                        'scheduled_end_time' => $data['scheduled_end_time'],
                        'is_recording_enabled' => $data['is_recording_enabled'],
                        'is_public' => $data['is_public'],
                        'status' => 'scheduled',
                        'is_active' => true,
                    ]);

                    Notification::make()
                        ->title('Live lesson created successfully!')
                        ->success()
                        ->send();

                    // Redirect to the live video resource
                    return redirect()->to(LiveVideoResource::getUrl('edit', ['record' => $liveVideo]));
                }),

            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
