<!DOCTYPE html>
<!-- saved from url=(0140)https://bd41pk3hey2tse8q.canva-hosted-embed.com/codelet/AAEAEGJkNDFwazNoZXkydHNlOHEAAAAAAZdOc-QzcUCbhuLMRJfErqU3Cnv2sIlp6WTKsC8tyVwq64lpa4U/ -->
<html lang="th"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EduNest - แดชบอร์ดผู้เรียน</title>
    <script src="./saved_resource"></script>
    <link href="./css2" rel="stylesheet">
    <style>
        body {
            font-family: 'Prompt', sans-serif;
            background-color: #f8f9ff;
        }
        
        .card {
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .progress-bar {
            height: 10px;
            border-radius: 5px;
            background-color: #e9ecef;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 5px;
            transition: width 1s ease;
        }
        
        .calendar-day {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .calendar-day:hover {
            background-color: #f0f4ff;
        }
        
        .calendar-day.has-event {
            position: relative;
        }
        
        .calendar-day.has-event::after {
            content: '';
            position: absolute;
            bottom: 2px;
            width: 4px;
            height: 4px;
            background-color: #ff6b6b;
            border-radius: 50%;
        }
        
        .calendar-day.active {
            background-color: #4f46e5;
            color: white;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 18px;
            height: 18px;
            background-color: #ff6b6b;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
        }
        
        .daily-practice-item {
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .daily-practice-item:hover {
            transform: scale(1.02);
        }
        
        .locked {
            position: relative;
            opacity: 0.7;
        }
        
        .locked::after {
            content: '🔒';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
        }
        
        .subject-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 100;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background-color: white;
            border-radius: 16px;
            padding: 24px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        /* Circular Progress Bar Styles */
        .circular-progress {
            position: relative;
            width: 100px;
            height: 100px;
            margin: 0 auto 15px;
        }
        
        .circular-progress svg {
            transform: rotate(-90deg);
        }
        
        .circular-progress circle {
            fill: none;
            stroke-width: 8;
            stroke-linecap: round;
        }
        
        .circular-progress .bg {
            stroke: rgba(255, 255, 255, 0.3);
        }
        
        .circular-progress .progress {
            stroke-dasharray: 283;
            transition: stroke-dashoffset 1s ease;
        }
        
        .circular-progress .percentage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 20px;
            font-weight: bold;
            color: white;
        }
        
        .subject-card {
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
        
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .subject-card h4 {
            margin-top: 10px;
            margin-bottom: 5px;
            font-weight: 600;
            color: white;
        }
        
        .subject-card p {
            margin: 0;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .action-button {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
        }
        
        .action-button svg {
            width: 16px;
            height: 16px;
            margin-right: 4px;
        }
        
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .circular-progress {
                width: 80px;
                height: 80px;
            }
            
            .action-buttons {
                flex-direction: column;
                width: 100%;
            }
            
            .action-button {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
<style>*, ::before, ::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/* ! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com */*,::after,::before{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}::after,::before{--tw-content:''}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.container{width:100%}@media (min-width: 640px){.container{max-width:640px}}@media (min-width: 768px){.container{max-width:768px}}@media (min-width: 1024px){.container{max-width:1024px}}@media (min-width: 1280px){.container{max-width:1280px}}@media (min-width: 1536px){.container{max-width:1536px}}.relative{position:relative}.mx-auto{margin-left:auto;margin-right:auto}.mb-2{margin-bottom:0.5rem}.mb-4{margin-bottom:1rem}.mb-6{margin-bottom:1.5rem}.mb-8{margin-bottom:2rem}.mr-2{margin-right:0.5rem}.mr-3{margin-right:0.75rem}.mr-4{margin-right:1rem}.mt-2{margin-top:0.5rem}.mt-4{margin-top:1rem}.flex{display:flex}.grid{display:grid}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-8{height:2rem}.min-h-screen{min-height:100vh}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-8{width:2rem}.w-full{width:100%}.min-w-full{min-width:100%}.cursor-pointer{cursor:pointer}.grid-cols-1{grid-template-columns:repeat(1, minmax(0, 1fr))}.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.grid-cols-7{grid-template-columns:repeat(7, minmax(0, 1fr))}.items-start{align-items:flex-start}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-1{gap:0.25rem}.gap-4{gap:1rem}.gap-6{gap:1.5rem}.space-x-2 > :not([hidden]) ~ :not([hidden]){--tw-space-x-reverse:0;margin-right:calc(0.5rem * var(--tw-space-x-reverse));margin-left:calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))}.space-y-2 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.5rem * var(--tw-space-y-reverse))}.space-y-3 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(0.75rem * var(--tw-space-y-reverse))}.space-y-6 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))}.overflow-x-auto{overflow-x:auto}.rounded-full{border-radius:9999px}.rounded-lg{border-radius:0.5rem}.border-b{border-bottom-width:1px}.border-t{border-top-width:1px}.border-gray-100{--tw-border-opacity:1;border-color:rgb(243 244 246 / var(--tw-border-opacity, 1))}.bg-blue-100{--tw-bg-opacity:1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.bg-blue-50{--tw-bg-opacity:1;background-color:rgb(239 246 255 / var(--tw-bg-opacity, 1))}.bg-blue-600{--tw-bg-opacity:1;background-color:rgb(37 99 235 / var(--tw-bg-opacity, 1))}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235 / var(--tw-bg-opacity, 1))}.bg-gray-50{--tw-bg-opacity:1;background-color:rgb(249 250 251 / var(--tw-bg-opacity, 1))}.bg-green-100{--tw-bg-opacity:1;background-color:rgb(220 252 231 / var(--tw-bg-opacity, 1))}.bg-green-200{--tw-bg-opacity:1;background-color:rgb(187 247 208 / var(--tw-bg-opacity, 1))}.bg-green-50{--tw-bg-opacity:1;background-color:rgb(240 253 244 / var(--tw-bg-opacity, 1))}.bg-green-600{--tw-bg-opacity:1;background-color:rgb(22 163 74 / var(--tw-bg-opacity, 1))}.bg-indigo-100{--tw-bg-opacity:1;background-color:rgb(224 231 255 / var(--tw-bg-opacity, 1))}.bg-indigo-50{--tw-bg-opacity:1;background-color:rgb(238 242 255 / var(--tw-bg-opacity, 1))}.bg-indigo-500{--tw-bg-opacity:1;background-color:rgb(99 102 241 / var(--tw-bg-opacity, 1))}.bg-indigo-600{--tw-bg-opacity:1;background-color:rgb(79 70 229 / var(--tw-bg-opacity, 1))}.bg-purple-100{--tw-bg-opacity:1;background-color:rgb(243 232 255 / var(--tw-bg-opacity, 1))}.bg-purple-50{--tw-bg-opacity:1;background-color:rgb(250 245 255 / var(--tw-bg-opacity, 1))}.bg-purple-500{--tw-bg-opacity:1;background-color:rgb(168 85 247 / var(--tw-bg-opacity, 1))}.bg-purple-600{--tw-bg-opacity:1;background-color:rgb(147 51 234 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-yellow-100{--tw-bg-opacity:1;background-color:rgb(254 249 195 / var(--tw-bg-opacity, 1))}.bg-yellow-50{--tw-bg-opacity:1;background-color:rgb(254 252 232 / var(--tw-bg-opacity, 1))}.bg-gradient-to-br{background-image:linear-gradient(to bottom right, var(--tw-gradient-stops))}.from-blue-500{--tw-gradient-from:#3b82f6 var(--tw-gradient-from-position);--tw-gradient-to:rgb(59 130 246 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-green-500{--tw-gradient-from:#22c55e var(--tw-gradient-from-position);--tw-gradient-to:rgb(34 197 94 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-orange-500{--tw-gradient-from:#f97316 var(--tw-gradient-from-position);--tw-gradient-to:rgb(249 115 22 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-pink-500{--tw-gradient-from:#ec4899 var(--tw-gradient-from-position);--tw-gradient-to:rgb(236 72 153 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-purple-500{--tw-gradient-from:#a855f7 var(--tw-gradient-from-position);--tw-gradient-to:rgb(168 85 247 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.from-yellow-500{--tw-gradient-from:#eab308 var(--tw-gradient-from-position);--tw-gradient-to:rgb(234 179 8 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to)}.to-blue-600{--tw-gradient-to:#2563eb var(--tw-gradient-to-position)}.to-green-600{--tw-gradient-to:#16a34a var(--tw-gradient-to-position)}.to-orange-600{--tw-gradient-to:#ea580c var(--tw-gradient-to-position)}.to-pink-600{--tw-gradient-to:#db2777 var(--tw-gradient-to-position)}.to-purple-600{--tw-gradient-to:#9333ea var(--tw-gradient-to-position)}.to-yellow-600{--tw-gradient-to:#ca8a04 var(--tw-gradient-to-position)}.p-1{padding:0.25rem}.p-2{padding:0.5rem}.p-3{padding:0.75rem}.p-6{padding:1.5rem}.px-3{padding-left:0.75rem;padding-right:0.75rem}.px-4{padding-left:1rem;padding-right:1rem}.py-1{padding-top:0.25rem;padding-bottom:0.25rem}.py-2{padding-top:0.5rem;padding-bottom:0.5rem}.py-3{padding-top:0.75rem;padding-bottom:0.75rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-6{padding-top:1.5rem;padding-bottom:1.5rem}.pt-4{padding-top:1rem}.text-left{text-align:left}.text-center{text-align:center}.text-2xl{font-size:1.5rem;line-height:2rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:0.875rem;line-height:1.25rem}.text-xl{font-size:1.25rem;line-height:1.75rem}.font-bold{font-weight:700}.font-medium{font-weight:500}.text-blue-600{--tw-text-opacity:1;color:rgb(37 99 235 / var(--tw-text-opacity, 1))}.text-blue-800{--tw-text-opacity:1;color:rgb(30 64 175 / var(--tw-text-opacity, 1))}.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1))}.text-gray-600{--tw-text-opacity:1;color:rgb(75 85 99 / var(--tw-text-opacity, 1))}.text-gray-700{--tw-text-opacity:1;color:rgb(55 65 81 / var(--tw-text-opacity, 1))}.text-gray-800{--tw-text-opacity:1;color:rgb(31 41 55 / var(--tw-text-opacity, 1))}.text-green-600{--tw-text-opacity:1;color:rgb(22 163 74 / var(--tw-text-opacity, 1))}.text-green-800{--tw-text-opacity:1;color:rgb(22 101 52 / var(--tw-text-opacity, 1))}.text-indigo-600{--tw-text-opacity:1;color:rgb(79 70 229 / var(--tw-text-opacity, 1))}.text-indigo-800{--tw-text-opacity:1;color:rgb(55 48 163 / var(--tw-text-opacity, 1))}.text-purple-600{--tw-text-opacity:1;color:rgb(147 51 234 / var(--tw-text-opacity, 1))}.text-purple-800{--tw-text-opacity:1;color:rgb(107 33 168 / var(--tw-text-opacity, 1))}.text-red-500{--tw-text-opacity:1;color:rgb(239 68 68 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.text-yellow-600{--tw-text-opacity:1;color:rgb(202 138 4 / var(--tw-text-opacity, 1))}.text-yellow-800{--tw-text-opacity:1;color:rgb(133 77 14 / var(--tw-text-opacity, 1))}.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)}.transition{transition-property:color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms}.hover\:bg-blue-100:hover{--tw-bg-opacity:1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.hover\:bg-blue-700:hover{--tw-bg-opacity:1;background-color:rgb(29 78 216 / var(--tw-bg-opacity, 1))}.hover\:bg-gray-100:hover{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1))}.hover\:bg-green-100:hover{--tw-bg-opacity:1;background-color:rgb(220 252 231 / var(--tw-bg-opacity, 1))}.hover\:bg-green-700:hover{--tw-bg-opacity:1;background-color:rgb(21 128 61 / var(--tw-bg-opacity, 1))}.hover\:bg-indigo-700:hover{--tw-bg-opacity:1;background-color:rgb(67 56 202 / var(--tw-bg-opacity, 1))}.hover\:bg-purple-100:hover{--tw-bg-opacity:1;background-color:rgb(243 232 255 / var(--tw-bg-opacity, 1))}.hover\:bg-purple-700:hover{--tw-bg-opacity:1;background-color:rgb(126 34 206 / var(--tw-bg-opacity, 1))}.hover\:bg-yellow-100:hover{--tw-bg-opacity:1;background-color:rgb(254 249 195 / var(--tw-bg-opacity, 1))}@media (min-width: 768px){.md\:col-span-2{grid-column:span 2 / span 2}.md\:grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}}@media (min-width: 1024px){.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr))}}</style></head>
<body class="min-h-screen">
 
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-6">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-800">สวัสดี, น้องมายด์! 👋</h2>
            <p class="text-gray-600">มาดูความก้าวหน้าของเราวันนี้กันเถอะ</p>
        </div>

        <!-- Dashboard Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 dashboard-grid">
            <!-- Left Column -->
            <div class="md:col-span-2 space-y-6">
                <!-- Progress by Subject - Moved to top -->
                <div class="card bg-white p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-6">📈 ความก้าวหน้าแยกตามวิชา</h3>
                    
                    <div class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Math Subject Card -->
                        <div class="subject-card bg-gradient-to-br from-blue-500 to-blue-600" onclick="window.location.href=&#39;#math-details&#39;">
                            <div class="circular-progress">
                                <svg width="100" height="100" viewBox="0 0 100 100">
                                    <circle class="bg" cx="50" cy="50" r="45"></circle>
                                    <circle class="progress math-progress" cx="50" cy="50" r="45" stroke-dashoffset="0" style="stroke-dasharray: 282.743, 282.743; stroke-dashoffset: 70.6858;"></circle>
                                </svg>
                                <div class="percentage">75%</div>
                            </div>
                            <h4>คณิตศาสตร์</h4>
                            <p>75% แบบฝึกหัด</p>
                            <p>92 จาก 100 คะแนนเฉลี่ย</p>
                        </div>
                        
                        <!-- Science Subject Card -->
                        <div class="subject-card bg-gradient-to-br from-green-500 to-green-600" onclick="window.location.href=&#39;#science-details&#39;">
                            <div class="circular-progress">
                                <svg width="100" height="100" viewBox="0 0 100 100">
                                    <circle class="bg" cx="50" cy="50" r="45"></circle>
                                    <circle class="progress science-progress" cx="50" cy="50" r="45" stroke-dashoffset="0" style="stroke-dasharray: 282.743, 282.743; stroke-dashoffset: 113.097;"></circle>
                                </svg>
                                <div class="percentage">60%</div>
                            </div>
                            <h4>วิทยาศาสตร์</h4>
                            <p>60% แบบฝึกหัด</p>
                            <p>85 จาก 100 คะแนนเฉลี่ย</p>
                        </div>
                        
                        <!-- Thai Language Subject Card -->
                        <div class="subject-card bg-gradient-to-br from-purple-500 to-purple-600" onclick="window.location.href=&#39;#thai-details&#39;">
                            <div class="circular-progress">
                                <svg width="100" height="100" viewBox="0 0 100 100">
                                    <circle class="bg" cx="50" cy="50" r="45"></circle>
                                    <circle class="progress thai-progress" cx="50" cy="50" r="45" stroke-dashoffset="0" style="stroke-dasharray: 282.743, 282.743; stroke-dashoffset: 28.2743;"></circle>
                                </svg>
                                <div class="percentage">90%</div>
                            </div>
                            <h4>ภาษาไทย</h4>
                            <p>90% แบบฝึกหัด</p>
                            <p>99 จาก 100 คะแนนเฉลี่ย</p>
                        </div>
                        
                        <!-- English Subject Card -->
                        <div class="subject-card bg-gradient-to-br from-yellow-500 to-yellow-600" onclick="window.location.href=&#39;#english-details&#39;">
                            <div class="circular-progress">
                                <svg width="100" height="100" viewBox="0 0 100 100">
                                    <circle class="bg" cx="50" cy="50" r="45"></circle>
                                    <circle class="progress english-progress" cx="50" cy="50" r="45" stroke-dashoffset="0" style="stroke-dasharray: 282.743, 282.743; stroke-dashoffset: 155.509;"></circle>
                                </svg>
                                <div class="percentage">45%</div>
                            </div>
                            <h4>ภาษาอังกฤษ</h4>
                            <p>45% แบบฝึกหัด</p>
                            <p>78 จาก 100 คะแนนเฉลี่ย</p>
                        </div>
                        
                        <!-- Social Studies Subject Card -->
                        <div class="subject-card bg-gradient-to-br from-pink-500 to-pink-600" onclick="window.location.href=&#39;#social-details&#39;">
                            <div class="circular-progress">
                                <svg width="100" height="100" viewBox="0 0 100 100">
                                    <circle class="bg" cx="50" cy="50" r="45"></circle>
                                    <circle class="progress social-progress" cx="50" cy="50" r="45" stroke-dashoffset="0" style="stroke-dasharray: 282.743, 282.743; stroke-dashoffset: 98.9602;"></circle>
                                </svg>
                                <div class="percentage">65%</div>
                            </div>
                            <h4>สังคมศึกษา</h4>
                            <p>65% แบบฝึกหัด</p>
                            <p>88 จาก 100 คะแนนเฉลี่ย</p>
                        </div>
                        
                        <!-- Art Subject Card -->
                        <div class="subject-card bg-gradient-to-br from-orange-500 to-orange-600" onclick="window.location.href=&#39;#art-details&#39;">
                            <div class="circular-progress">
                                <svg width="100" height="100" viewBox="0 0 100 100">
                                    <circle class="bg" cx="50" cy="50" r="45"></circle>
                                    <circle class="progress art-progress" cx="50" cy="50" r="45" stroke-dashoffset="0" style="stroke-dasharray: 282.743, 282.743; stroke-dashoffset: 56.5487;"></circle>
                                </svg>
                                <div class="percentage">80%</div>
                            </div>
                            <h4>ศิลปะ</h4>
                            <p>80% แบบฝึกหัด</p>
                            <p>95 จาก 100 คะแนนเฉลี่ย</p>
                        </div>
                    </div>
                </div>

                <!-- Calendar Section -->
                <div class="card bg-white p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-bold text-gray-800">📅 ปฏิทินกิจกรรม</h3>
                        <div class="flex space-x-2">
                            <button id="prev-month" class="p-1 rounded-full hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                </svg>
                            </button>
                            <span id="current-month" class="text-gray-700 font-medium">มิถุนายน 2568</span>
                            <button id="next-month" class="p-1 rounded-full hover:bg-gray-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Calendar Days -->
                    <div class="grid grid-cols-7 gap-1 text-center mb-2">
                        <div class="text-sm font-medium text-gray-500">อา</div>
                        <div class="text-sm font-medium text-gray-500">จ</div>
                        <div class="text-sm font-medium text-gray-500">อ</div>
                        <div class="text-sm font-medium text-gray-500">พ</div>
                        <div class="text-sm font-medium text-gray-500">พฤ</div>
                        <div class="text-sm font-medium text-gray-500">ศ</div>
                        <div class="text-sm font-medium text-gray-500">ส</div>
                    </div>
                    
                    <div class="grid grid-cols-7 gap-1" id="calendar-days"><div class="calendar-day">1</div><div class="calendar-day">2</div><div class="calendar-day">3</div><div class="calendar-day">4</div><div class="calendar-day">5</div><div class="calendar-day">6</div><div class="calendar-day active">7</div><div class="calendar-day">8</div><div class="calendar-day">9</div><div class="calendar-day">10</div><div class="calendar-day">11</div><div class="calendar-day">12</div><div class="calendar-day">13</div><div class="calendar-day">14</div><div class="calendar-day">15</div><div class="calendar-day">16</div><div class="calendar-day">17</div><div class="calendar-day">18</div><div class="calendar-day">19</div><div class="calendar-day">20</div><div class="calendar-day">21</div><div class="calendar-day">22</div><div class="calendar-day">23</div><div class="calendar-day">24</div><div class="calendar-day">25</div><div class="calendar-day">26</div><div class="calendar-day">27</div><div class="calendar-day">28</div><div class="calendar-day">29</div><div class="calendar-day">30</div></div>
                    
                    <!-- Event Details -->
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <h4 class="font-medium text-gray-800 mb-2">กิจกรรมวันนี้</h4>
                        <div id="event-list">
                            <div class="flex items-start p-3 rounded-lg bg-blue-50 mb-2">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div class="w-full">
                                    <h5 class="font-medium text-blue-800">Live Class: คณิตศาสตร์ - เรื่องเศษส่วน</h5>
                                    <p class="text-sm text-blue-600">17:00 - 18:00 น.</p>
                                    <div class="action-buttons">
                                        <button class="action-button bg-blue-600 text-white hover:bg-blue-700 transition">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"></path>
                                            </svg>
                                            เข้าร่วมคลาส
                                        </button>
                                        <button class="action-button bg-green-600 text-white hover:bg-green-700 transition">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"></path>
                                            </svg>
                                            สรุปบทเรียน (.pdf)
                                        </button>
                                        <button class="action-button bg-purple-600 text-white hover:bg-purple-700 transition">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"></path>
                                            </svg>
                                            แบบทดสอบหลังเรียน
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-start p-3 rounded-lg bg-green-50">
                                <div class="bg-green-100 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h5 class="font-medium text-green-800">กล่องกิจกรรม: วิทยาศาสตร์ - การทดลองเรื่องแสง</h5>
                                    <p class="text-sm text-green-600">จัดส่งวันนี้</p>
                                    <button class="mt-2 px-3 py-1 bg-green-600 text-white text-sm rounded-full hover:bg-green-700 transition">ติดตามพัสดุ</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Activity Summary Table -->
                <div class="card bg-white p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">📊 สรุปตารางการทำแบบฝึกหัด</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead>
                                <tr class="bg-gray-50">
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-600">วันที่</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-600">วิชา</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-600">เวลาสะสม</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-600">ผลคะแนน</th>
                                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-600">สถานะการทบทวน</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-3 text-sm text-gray-700">15 พ.ค. 2023</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">คณิตศาสตร์</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">45 นาที</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">92/100</td>
                                    <td class="px-4 py-3 text-sm text-green-600">✓ ทบทวนแล้ว</td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-3 text-sm text-gray-700">14 พ.ค. 2023</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">วิทยาศาสตร์</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">30 นาที</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">85/100</td>
                                    <td class="px-4 py-3 text-sm text-green-600">✓ ทบทวนแล้ว</td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-3 text-sm text-gray-700">13 พ.ค. 2023</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">ภาษาไทย</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">60 นาที</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">99/100</td>
                                    <td class="px-4 py-3 text-sm text-green-600">✓ ทบทวนแล้ว</td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-3 text-sm text-gray-700">12 พ.ค. 2023</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">ภาษาอังกฤษ</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">40 นาที</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">78/100</td>
                                    <td class="px-4 py-3 text-sm text-red-500">✗ ยังไม่ทบทวน</td>
                                </tr>
                                <tr class="border-b border-gray-100">
                                    <td class="px-4 py-3 text-sm text-gray-700">11 พ.ค. 2023</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">คณิตศาสตร์</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">35 นาที</td>
                                    <td class="px-4 py-3 text-sm text-gray-700">88/100</td>
                                    <td class="px-4 py-3 text-sm text-green-600">✓ ทบทวนแล้ว</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Right Column -->
            <div class="space-y-6">
                <!-- Daily Practice Track -->
                <div class="card bg-white p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">🎯 แทบฝึกประจำวัน</h3>
                    
                    <div class="flex justify-between items-center mb-4">
                        <div>
                            <span class="text-sm text-gray-500">ความก้าวหน้า</span>
                            <div class="text-xl font-bold text-indigo-600">32/50 ข้อ</div>
                        </div>
                        <div class="bg-indigo-100 text-indigo-600 px-3 py-1 rounded-full text-sm font-medium">วิชา: คณิตศาสตร์</div>
                    </div>
                    
                    <div class="progress-bar mb-6">
                        <div class="progress-fill bg-indigo-500" style="width: 64%"></div>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="daily-practice-item bg-green-100 p-3 rounded-lg flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="bg-green-200 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-medium text-green-800">บทที่ 1: พื้นฐานเศษส่วน</div>
                                    <div class="text-sm text-green-600">ผ่านแล้ว</div>
                                </div>
                            </div>
                            <div class="text-green-600 font-medium">100%</div>
                        </div>
                        
                        <div class="daily-practice-item bg-indigo-50 p-3 rounded-lg flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="bg-indigo-100 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-medium text-indigo-800">บทที่ 2: การบวกเศษส่วน</div>
                                    <div class="text-sm text-indigo-600">กำลังเรียน (32/50)</div>
                                </div>
                            </div>
                            <button class="px-3 py-1 bg-indigo-600 text-white text-sm rounded-full hover:bg-indigo-700 transition">ทำต่อ</button>
                        </div>
                        
                        <div class="daily-practice-item bg-gray-50 p-3 rounded-lg flex justify-between items-center locked">
                            <div class="flex items-center">
                                <div class="bg-gray-200 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-700">บทที่ 3: การลบเศษส่วน</div>
                                    <div class="text-sm text-gray-500">ล็อกอยู่</div>
                                </div>
                            </div>
                            <div class="text-gray-500">0%</div>
                        </div>
                        
                        <div class="daily-practice-item bg-gray-50 p-3 rounded-lg flex justify-between items-center locked">
                            <div class="flex items-center">
                                <div class="bg-gray-200 p-2 rounded-lg mr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-700">บททดสอบ: เศษส่วน</div>
                                    <div class="text-sm text-gray-500">ล็อกอยู่</div>
                                </div>
                            </div>
                            <div class="text-gray-500">0%</div>
                        </div>
                    </div>
                </div>
                
                <!-- Recommended Resources -->
                <div class="card bg-white p-6">
                    <h3 class="text-lg font-bold text-gray-800 mb-4">📚 สื่อจำเป็นสำหรับคุณ</h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-start p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition cursor-pointer">
                            <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-blue-800">วิดีโอ: เทคนิคการบวกเศษส่วน</h5>
                                <p class="text-sm text-blue-600">ระยะเวลา: 12 นาที</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start p-3 rounded-lg bg-green-50 hover:bg-green-100 transition cursor-pointer">
                            <div class="bg-green-100 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-green-800">ใบงาน: แบบฝึกหัดเศษส่วนเพิ่มเติม</h5>
                                <p class="text-sm text-green-600">PDF, 5 หน้า</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start p-3 rounded-lg bg-purple-50 hover:bg-purple-100 transition cursor-pointer">
                            <div class="bg-purple-100 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-purple-800">เกม: เศษส่วนมหาสนุก</h5>
                                <p class="text-sm text-purple-600">เล่นออนไลน์</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start p-3 rounded-lg bg-yellow-50 hover:bg-yellow-100 transition cursor-pointer">
                            <div class="bg-yellow-100 p-2 rounded-lg mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div>
                                <h5 class="font-medium text-yellow-800">เคล็ดลับ: การจำตัวส่วนร่วมมาก</h5>
                                <p class="text-sm text-yellow-600">อ่าน 5 นาที</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Event Modal -->
    <div id="event-modal" class="modal">
        <div class="modal-content">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800" id="modal-title">รายละเอียดกิจกรรม</h3>
                <button id="close-modal" class="p-1 rounded-full hover:bg-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div id="modal-content">
                <!-- Modal content will be inserted here -->
            </div>
        </div>
    </div>

    <script>
        // Calendar functionality
        document.addEventListener('DOMContentLoaded', function() {
            const calendarDays = document.getElementById('calendar-days');
            const currentMonthEl = document.getElementById('current-month');
            const prevMonthBtn = document.getElementById('prev-month');
            const nextMonthBtn = document.getElementById('next-month');
            const modal = document.getElementById('event-modal');
            const closeModal = document.getElementById('close-modal');
            const modalTitle = document.getElementById('modal-title');
            const modalContent = document.getElementById('modal-content');
            
            let currentDate = new Date();
            let currentMonth = currentDate.getMonth();
            let currentYear = currentDate.getFullYear();
            
            // Events data
            const events = {
                '2023-05-15': [
                    { 
                        type: 'live-class', 
                        title: 'Live Class: คณิตศาสตร์ - เรื่องเศษส่วน', 
                        time: '17:00 - 18:00 น.',
                        details: 'เรียนรู้เกี่ยวกับการบวกและลบเศษส่วนที่มีตัวส่วนไม่เท่ากัน',
                        buttons: [
                            { text: 'เข้าร่วมคลาส', link: '#', color: 'blue', icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" /></svg>' },
                            { text: 'สรุปบทเรียน (.pdf)', link: '#', color: 'green', icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" /></svg>' },
                            { text: 'แบบทดสอบหลังเรียน', link: '#', color: 'purple', icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" /></svg>' }
                        ]
                    }
                ],
                '2023-05-16': [
                    { 
                        type: 'activity-box', 
                        title: 'กล่องกิจกรรม: วิทยาศาสตร์ - การทดลองเรื่องแสง', 
                        details: 'กล่องกิจกรรมจะถูกจัดส่งในวันนี้ คาดว่าจะได้รับภายใน 1-2 วัน',
                        buttons: [
                            { text: 'ติดตามพัสดุ', link: '#', color: 'green', icon: '' }
                        ]
                    }
                ],
                '2023-05-18': [
                    { 
                        type: 'live-class', 
                        title: 'Live Class: วิทยาศาสตร์ - แสงและการมองเห็น', 
                        time: '16:00 - 17:00 น.',
                        details: 'เรียนรู้เกี่ยวกับแสงและการมองเห็น พร้อมทำการทดลองจากกล่องกิจกรรม',
                        buttons: [
                            { text: 'เข้าร่วมคลาส', link: '#', color: 'blue', icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z" /></svg>' },
                            { text: 'สรุปบทเรียน (.pdf)', link: '#', color: 'green', icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m.75 12 3 3m0 0 3-3m-3 3v-6m-1.5-9H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" /></svg>' },
                            { text: 'แบบทดสอบหลังเรียน', link: '#', color: 'purple', icon: '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-1"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z" /></svg>' }
                        ]
                    }
                ],
                '2023-05-20': [
                    { 
                        type: 'test', 
                        title: 'แบบทดสอบ: คณิตศาสตร์ - เศษส่วน', 
                        time: 'ตลอดทั้งวัน',
                        details: 'แบบทดสอบเพื่อวัดความเข้าใจเรื่องเศษส่วน ใช้เวลาทำ 30 นาที',
                        buttons: [
                            { text: 'เริ่มทำแบบทดสอบ', link: '#', color: 'red', icon: '' }
                        ]
                    }
                ],
                '2023-05-22': [
                    { 
                        type: 'activity-box', 
                        title: 'กล่องกิจกรรม: ภาษาอังกฤษ - ชุดคำศัพท์และเกม', 
                        details: 'กล่องกิจกรรมจะถูกจัดส่งในวันนี้ คาดว่าจะได้รับภายใน 1-2 วัน',
                        buttons: [
                            { text: 'ติดตามพัสดุ', link: '#', color: 'green', icon: '' }
                        ]
                    }
                ]
            };
            
            // Thai month names
            const thaiMonths = [
                'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
                'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
            ];
            
            function renderCalendar() {
                calendarDays.innerHTML = '';
                
                const firstDay = new Date(currentYear, currentMonth, 1).getDay();
                const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
                
                // Update month display
                currentMonthEl.textContent = `${thaiMonths[currentMonth]} ${currentYear + 543}`;
                
                // Add empty cells for days before the first day of the month
                for (let i = 0; i < firstDay; i++) {
                    const emptyDay = document.createElement('div');
                    emptyDay.classList.add('calendar-day');
                    calendarDays.appendChild(emptyDay);
                }
                
                // Add days of the month
                for (let day = 1; day <= daysInMonth; day++) {
                    const dayEl = document.createElement('div');
                    dayEl.classList.add('calendar-day');
                    dayEl.textContent = day;
                    
                    // Check if this day has events
                    const dateStr = `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                    if (events[dateStr]) {
                        dayEl.classList.add('has-event');
                        
                        // Add event indicator color based on event type
                        if (events[dateStr].some(e => e.type === 'live-class')) {
                            dayEl.style.color = '#4f46e5';
                        } else if (events[dateStr].some(e => e.type === 'activity-box')) {
                            dayEl.style.color = '#10b981';
                        } else if (events[dateStr].some(e => e.type === 'test')) {
                            dayEl.style.color = '#ef4444';
                        }
                        
                        // Add click event to show modal
                        dayEl.addEventListener('click', () => {
                            showEventModal(dateStr, day);
                        });
                    }
                    
                    // Highlight today
                    if (currentYear === new Date().getFullYear() && 
                        currentMonth === new Date().getMonth() && 
                        day === new Date().getDate()) {
                        dayEl.classList.add('active');
                    }
                    
                    calendarDays.appendChild(dayEl);
                }
            }
            
            function showEventModal(dateStr, day) {
                const dayEvents = events[dateStr];
                if (!dayEvents) return;
                
                modalTitle.textContent = `กิจกรรมวันที่ ${day} ${thaiMonths[currentMonth]} ${currentYear + 543}`;
                
                let content = '';
                dayEvents.forEach(event => {
                    let bgColor, iconBg, iconColor, icon;
                    
                    switch(event.type) {
                        case 'live-class':
                            bgColor = 'bg-blue-50';
                            iconBg = 'bg-blue-100';
                            iconColor = 'text-blue-600';
                            icon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>';
                            break;
                        case 'activity-box':
                            bgColor = 'bg-green-50';
                            iconBg = 'bg-green-100';
                            iconColor = 'text-green-600';
                            icon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" /></svg>';
                            break;
                        case 'test':
                            bgColor = 'bg-red-50';
                            iconBg = 'bg-red-100';
                            iconColor = 'text-red-600';
                            icon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>';
                            break;
                    }
                    
                    // Create buttons HTML
                    let buttonsHtml = '';
                    if (event.buttons && event.buttons.length > 0) {
                        buttonsHtml = '<div class="flex flex-wrap gap-2 mt-2">';
                        event.buttons.forEach(button => {
                            let buttonColor;
                            switch(button.color) {
                                case 'blue':
                                    buttonColor = 'bg-blue-600 hover:bg-blue-700';
                                    break;
                                case 'green':
                                    buttonColor = 'bg-green-600 hover:bg-green-700';
                                    break;
                                case 'purple':
                                    buttonColor = 'bg-purple-600 hover:bg-purple-700';
                                    break;
                                case 'red':
                                    buttonColor = 'bg-red-600 hover:bg-red-700';
                                    break;
                                default:
                                    buttonColor = 'bg-indigo-600 hover:bg-indigo-700';
                            }
                            
                            buttonsHtml += `
                                <a href="${button.link}" class="px-3 py-1 ${buttonColor} text-white text-sm rounded-full transition inline-flex items-center">
                                    ${button.icon ? button.icon : ''}
                                    ${button.text}
                                </a>
                            `;
                        });
                        buttonsHtml += '</div>';
                    }
                    
                    content += `
                        <div class="flex items-start p-3 rounded-lg ${bgColor} mb-3">
                            <div class="${iconBg} p-2 rounded-lg mr-3">
                                <span class="${iconColor}">${icon}</span>
                            </div>
                            <div class="w-full">
                                <h5 class="font-medium text-gray-800">${event.title}</h5>
                                ${event.time ? `<p class="text-sm text-gray-600 mb-1">${event.time}</p>` : ''}
                                <p class="text-sm text-gray-600 mb-2">${event.details}</p>
                                ${buttonsHtml}
                            </div>
                        </div>
                    `;
                });
                
                modalContent.innerHTML = content;
                modal.style.display = 'flex';
            }
            
            // Initialize calendar
            renderCalendar();
            
            // Month navigation
            prevMonthBtn.addEventListener('click', () => {
                currentMonth--;
                if (currentMonth < 0) {
                    currentMonth = 11;
                    currentYear--;
                }
                renderCalendar();
            });
            
            nextMonthBtn.addEventListener('click', () => {
                currentMonth++;
                if (currentMonth > 11) {
                    currentMonth = 0;
                    currentYear++;
                }
                renderCalendar();
            });
            
            // Close modal
            closeModal.addEventListener('click', () => {
                modal.style.display = 'none';
            });
            
            window.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            });
            
            // Initialize circular progress bars
            function setCircularProgress(className, percentage) {
                const circumference = 2 * Math.PI * 45; // 45 is the radius
                const offset = circumference - (percentage / 100) * circumference;
                const progressElement = document.querySelector(`.${className}`);
                
                if (progressElement) {
                    progressElement.style.strokeDasharray = `${circumference} ${circumference}`;
                    progressElement.style.strokeDashoffset = offset;
                }
            }
            
            // Set progress for each subject
            setCircularProgress('math-progress', 75);
            setCircularProgress('science-progress', 60);
            setCircularProgress('thai-progress', 90);
            setCircularProgress('english-progress', 45);
            setCircularProgress('social-progress', 65);
            setCircularProgress('art-progress', 80);
        });
    </script>
 
</body></html>