<?php namespace MyFatoorah\Library\API; use MyFatoorah\Library\MyFatoorah; class  MyFatoorahShipping extends MyFatoorah{public function getShippingCountries(){$url="$this->apiURL/v2/GetCountries";$json=$this->callAPI($url,null,null,'Get Countries');return $json->Data;}public function getShippingCities($method,$countryCode,$searchValue=''){$url=$this->apiURL.'/v2/GetCities'.'?shippingMethod='.$method.'&countryCode='.$countryCode.'&searchValue='.urlencode(substr($searchValue,0,30));$json=$this->callAPI($url,null,null,"Get Cities: $countryCode");return array_map('ucwords',$json->Data->CityNames);}public function calculateShippingCharge($curlData){if(!empty($curlData['Items'])){foreach($curlData['Items']as &$item){$item['ProductName']=strip_tags($item['ProductName']);$item['Description']=strip_tags($item['Description']);}}$url="$this->apiURL/v2/CalculateShippingCharge";$json=$this->callAPI($url,$curlData,null,'Calculate Shipping Charge');return $json->Data;}}