<!-- Teacher-specific Information Display -->

@if($profile->employee_id || $profile->qualification || isset($profile->years_of_experience))
<div class="form-section">
    <h2 class="section-title">Professional Information</h2>
    <div class="form-grid">
        @if($profile->employee_id)
        <div class="form-group">
            <label class="form-label">Employee ID</label>
            <div class="profile-value">{{ $profile->employee_id }}</div>
        </div>
        @endif

        @if($profile->qualification)
        <div class="form-group">
            <label class="form-label">Qualification</label>
            <div class="profile-value">{{ $profile->qualification }}</div>
        </div>
        @endif

        @if(isset($profile->years_of_experience))
        <div class="form-group">
            <label class="form-label">Years of Experience</label>
            <div class="profile-value">{{ $profile->years_of_experience }} years</div>
        </div>
        @endif
    </div>
</div>
@endif

@if($profile->subjects && count($profile->subjects) > 0)
<div class="form-section">
    <h2 class="section-title">Teaching Subjects</h2>
    <div class="form-grid">
        <div class="form-group" style="grid-column: 1 / -1;">
            <label class="form-label">Subjects You Teach</label>
            <div class="profile-value">
                @foreach($profile->subjects as $subject)
                    <span class="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm mr-2 mb-1">
                        {{ $subject }}
                    </span>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endif
