<?php

namespace App\Console\Commands;

use App\Models\AppSettings;
use App\Models\Team;
use Illuminate\Console\Command;

class TestTeamSettings extends Command
{
    protected $signature = 'test:team-settings';
    protected $description = 'Test team-specific settings functionality';

    public function handle()
    {
        $this->info('=== Testing Team-Specific Settings ===');

        // Get teams
        $teams = Team::all();
        
        if ($teams->isEmpty()) {
            $this->error('No teams found. Please create teams first.');
            return 1;
        }

        $this->info("Found {$teams->count()} teams:");
        foreach ($teams as $team) {
            $this->line("  - {$team->name} (ID: {$team->id})");
        }

        // Test setting team-specific values
        $this->info("\n=== Setting Team-Specific App Names ===");
        
        foreach ($teams as $team) {
            $appName = "App for {$team->name}";
            
            AppSettings::setTeamSetting('app', 'app_name', $appName, $team->id);
            $this->info("✅ Set app_name for {$team->name}: {$appName}");
        }

        // Test retrieving team-specific values
        $this->info("\n=== Retrieving Team-Specific Settings ===");
        
        foreach ($teams as $team) {
            $appName = get_team_settings($team->id, 'app.app_name');
            $this->info("📖 App name for {$team->name}: " . ($appName ?? 'Not set'));
        }

        // Test getting all settings for a team
        $this->info("\n=== Getting All Settings for First Team ===");
        $firstTeam = $teams->first();
        $allSettings = get_team_settings($firstTeam->id);
        
        $this->info("All settings for {$firstTeam->name}:");
        $this->line(json_encode($allSettings, JSON_PRETTY_PRINT));

        // Test global settings (team_id = null)
        $this->info("\n=== Testing Global Settings ===");
        AppSettings::setTeamSetting('app', 'global_setting', 'This is a global setting', null);
        $globalSetting = get_team_settings(null, 'app.global_setting');
        $this->info("📖 Global setting: " . ($globalSetting ?? 'Not set'));

        // Show database state
        $this->info("\n=== Database State ===");
        $allAppSettings = AppSettings::where('tab', 'app')->get();
        
        $this->table(
            ['ID', 'Tab', 'Key', 'Value', 'Team ID', 'Team Name'],
            $allAppSettings->map(function ($setting) {
                return [
                    $setting->id,
                    $setting->tab,
                    $setting->key,
                    $setting->value,
                    $setting->team_id ?? 'NULL (Global)',
                    $setting->team ? $setting->team->name : 'Global'
                ];
            })
        );

        $this->info("\n✅ Team settings test completed!");
        
        return 0;
    }
}
