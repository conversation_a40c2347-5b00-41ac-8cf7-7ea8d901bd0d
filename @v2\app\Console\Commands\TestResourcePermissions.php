<?php

namespace App\Console\Commands;

use App\Models\Team;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Filament\Facades\Filament;
use Filament\Resources\Resource;
use Illuminate\Support\Str;

class TestResourcePermissions extends Command
{
    protected $signature = 'test:permissions 
                            {--team= : Test specific team slug}
                            {--role= : Test specific role}
                            {--resource= : Test specific resource}
                            {--verbose : Show detailed output}';

    protected $description = 'Test all Filament resource permissions across different roles and teams';

    protected array $results = [];
    protected array $errors = [];

    public function handle()
    {
        $this->info('🧪 Starting Resource Permission Testing...');
        $this->newLine();

        // Get test parameters
        $teamSlug = $this->option('team');
        $roleFilter = $this->option('role');
        $resourceFilter = $this->option('resource');
        $verbose = $this->option('verbose');

        // Get teams to test
        $teams = $teamSlug 
            ? Team::where('slug', $teamSlug)->get()
            : Team::limit(3)->get(); // Limit to first 3 teams for testing

        if ($teams->isEmpty()) {
            $this->error('No teams found to test.');
            return 1;
        }

        // Get all Filament resources
        $resources = $this->getFilamentResources($resourceFilter);

        if (empty($resources)) {
            $this->error('No resources found to test.');
            return 1;
        }

        // Test each team
        foreach ($teams as $team) {
            $this->testTeam($team, $resources, $roleFilter, $verbose);
        }

        // Display results
        $this->displayResults();

        return 0;
    }

    protected function testTeam(Team $team, array $resources, ?string $roleFilter, bool $verbose): void
    {
        $this->info("🏫 Testing Team: {$team->name} ({$team->slug})");
        
        // Get roles to test
        $roles = $roleFilter 
            ? [$roleFilter]
            : ['super_admin', 'team_admin', 'teacher', 'student', 'parent'];

        foreach ($roles as $roleName) {
            $this->testRole($team, $roleName, $resources, $verbose);
        }

        $this->newLine();
    }

    protected function testRole(Team $team, string $roleName, array $resources, bool $verbose): void
    {
        // Get a user with this role
        $user = $this->getUserWithRole($team, $roleName);
        
        if (!$user) {
            $this->warn("  ⚠️  No user found with role '{$roleName}' in team '{$team->name}'");
            return;
        }

        if ($verbose) {
            $this->line("  👤 Testing Role: {$roleName} (User: {$user->name})");
        }

        // Set current tenant and authenticate user
        if ($roleName !== 'super_admin') {
            Filament::setTenant($team);
        }
        Auth::login($user);

        foreach ($resources as $resourceClass) {
            $this->testResource($team, $roleName, $resourceClass, $verbose);
        }

        Auth::logout();
    }

    protected function testResource(Team $team, string $roleName, string $resourceClass, bool $verbose): void
    {
        try {
            $resourceName = class_basename($resourceClass);
            
            // Test basic permissions
            $permissions = [
                'canViewAny' => $resourceClass::canViewAny(),
                'canCreate' => $resourceClass::canCreate(),
            ];

            // Try to get a sample record for record-specific permissions
            $model = $resourceClass::getModel();
            $record = null;
            
            if ($roleName !== 'super_admin') {
                $record = $model::where('team_id', $team->id)->first();
            } else {
                $record = $model::first();
            }

            if ($record) {
                $permissions['canView'] = $resourceClass::canView($record);
                $permissions['canEdit'] = $resourceClass::canEdit($record);
                $permissions['canDelete'] = $resourceClass::canDelete($record);
            }

            // Store results
            $this->results[$team->slug][$roleName][$resourceName] = $permissions;

            if ($verbose) {
                $this->displayResourceResult($resourceName, $permissions);
            }

        } catch (\Exception $e) {
            $error = "Error testing {$resourceClass} for {$roleName}: " . $e->getMessage();
            $this->errors[] = $error;
            
            if ($verbose) {
                $this->error("    ❌ {$error}");
            }
        }
    }

    protected function displayResourceResult(string $resourceName, array $permissions): void
    {
        $status = collect($permissions)->contains(true) ? '✅' : '❌';
        $permissionSummary = collect($permissions)
            ->map(fn($allowed, $permission) => $allowed ? "✓{$permission}" : "✗{$permission}")
            ->join(', ');
            
        $this->line("    {$status} {$resourceName}: {$permissionSummary}");
    }

    protected function getUserWithRole(Team $team, string $roleName): ?User
    {
        if ($roleName === 'super_admin') {
            return User::whereNull('team_id')
                ->whereHas('roles', fn($q) => $q->where('name', 'super_admin'))
                ->first();
        }

        return User::where('team_id', $team->id)
            ->whereHas('roles', fn($q) => $q->where('name', $roleName))
            ->first();
    }

    protected function getFilamentResources(?string $filter): array
    {
        $resourcePaths = [
            app_path('Filament/Resources'),
        ];

        $resources = [];

        foreach ($resourcePaths as $path) {
            if (!is_dir($path)) continue;

            $files = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($path)
            );

            foreach ($files as $file) {
                if ($file->isFile() && $file->getExtension() === 'php') {
                    $relativePath = str_replace($path . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $className = 'App\\Filament\\Resources\\' . str_replace(['/', '.php'], ['\\', ''], $relativePath);

                    if (class_exists($className) && is_subclass_of($className, Resource::class)) {
                        if (!$filter || Str::contains(strtolower($className), strtolower($filter))) {
                            $resources[] = $className;
                        }
                    }
                }
            }
        }

        return $resources;
    }

    protected function displayResults(): void
    {
        $this->newLine();
        $this->info('📊 Test Results Summary:');
        $this->newLine();

        foreach ($this->results as $teamSlug => $teamResults) {
            $this->line("🏫 Team: {$teamSlug}");
            
            foreach ($teamResults as $role => $roleResults) {
                $this->line("  👤 Role: {$role}");
                
                foreach ($roleResults as $resource => $permissions) {
                    $allowedCount = collect($permissions)->filter()->count();
                    $totalCount = count($permissions);
                    $percentage = $totalCount > 0 ? round(($allowedCount / $totalCount) * 100) : 0;
                    
                    $status = $percentage > 0 ? '✅' : '❌';
                    $this->line("    {$status} {$resource}: {$allowedCount}/{$totalCount} permissions ({$percentage}%)");
                }
            }
            $this->newLine();
        }

        if (!empty($this->errors)) {
            $this->error('❌ Errors encountered:');
            foreach ($this->errors as $error) {
                $this->line("  • {$error}");
            }
        }

        $this->info('✅ Testing completed!');
    }
}
