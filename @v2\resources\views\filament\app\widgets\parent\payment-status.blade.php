<x-filament-widgets::widget>
    <x-filament::section>
        <x-slot name="heading">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <x-heroicon-o-currency-dollar class="w-5 h-5 text-primary-600 mr-2" />
                    💳 สถานะการชำระเงิน
                </div>
                <div class="text-sm text-gray-600 dark:text-gray-400">
                    ยอดค้างชำระ: <span class="font-bold text-red-600 dark:text-red-400">{{ $this->getTotalPending() }}</span>
                </div>
            </div>
        </x-slot>

        <div class="space-y-4">
            @foreach($this->getPayments() as $payment)
                <div class="p-4 rounded-lg border-l-4 transition-all duration-200 hover:shadow-md
                    @if($payment['status'] === 'pending' && $payment['priority'] === 'high') border-red-500 bg-red-50 dark:bg-red-900/20
                    @elseif($payment['status'] === 'pending') border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20
                    @else border-green-500 bg-green-50 dark:bg-green-900/20
                    @endif">
                    
                    <div class="flex justify-between items-start">
                        <div class="flex items-start space-x-3">
                            <div class="p-2 rounded-lg
                                @if($payment['status'] === 'pending' && $payment['priority'] === 'high') bg-red-100 dark:bg-red-900/50
                                @elseif($payment['status'] === 'pending') bg-yellow-100 dark:bg-yellow-900/50
                                @else bg-green-100 dark:bg-green-900/50
                                @endif">
                                @if($payment['status'] === 'pending')
                                    <x-heroicon-o-exclamation-triangle class="w-5 h-5 
                                        @if($payment['priority'] === 'high') text-red-600 dark:text-red-400
                                        @else text-yellow-600 dark:text-yellow-400
                                        @endif" />
                                @else
                                    <x-heroicon-o-check-circle class="w-5 h-5 text-green-600 dark:text-green-400" />
                                @endif
                            </div>
                            
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 dark:text-white">{{ $payment['description'] }}</h4>
                                <p class="text-lg font-bold mt-1
                                    @if($payment['status'] === 'pending') text-red-600 dark:text-red-400
                                    @else text-green-600 dark:text-green-400
                                    @endif">
                                    {{ $payment['amount'] }}
                                </p>
                                <p class="text-sm text-gray-500 dark:text-gray-500 mt-1">
                                    กำหนดชำระ: {{ $payment['due_date'] }}
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex flex-col items-end space-y-2">
                            <span class="px-3 py-1 rounded-full text-xs font-medium
                                @if($payment['status'] === 'pending' && $payment['priority'] === 'high') bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200
                                @elseif($payment['status'] === 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200
                                @else bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200
                                @endif">
                                @if($payment['status'] === 'pending' && $payment['priority'] === 'high') เร่งด่วน
                                @elseif($payment['status'] === 'pending') ค้างชำระ
                                @else ชำระแล้ว
                                @endif
                            </span>
                            
                            @if($payment['status'] === 'pending')
                                <button class="px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center
                                    @if($payment['priority'] === 'high') bg-red-600 hover:bg-red-700 text-white
                                    @else bg-yellow-600 hover:bg-yellow-700 text-white
                                    @endif">
                                    <x-heroicon-o-credit-card class="w-4 h-4 mr-1" />
                                    ชำระเงิน
                                </button>
                            @else
                                <button class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300 text-sm rounded-lg transition-colors flex items-center">
                                    <x-heroicon-o-document-text class="w-4 h-4 mr-1" />
                                    ดูใบเสร็จ
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Quick Payment Summary -->
        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="flex justify-between items-center">
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white">สรุปการชำระเงิน</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">ยอดรวมที่ต้องชำระในเดือนนี้</p>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ $this->getTotalPending() }}</div>
                    <button class="mt-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white text-sm rounded-lg transition-colors">
                        ชำระทั้งหมด
                    </button>
                </div>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
