<?php

namespace App\Console\Commands;

use App\Services\TaskNotificationService;
use Illuminate\Console\Command;

class ProcessTaskAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tasks:process-alerts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process pending task alerts and send notifications';

    /**
     * Execute the console command.
     */
    public function handle(TaskNotificationService $notificationService)
    {
        $this->info('Processing pending task alerts...');

        try {
            $notificationService->processPendingAlerts();
            $this->info('Task alerts processed successfully.');
        } catch (\Exception $e) {
            $this->error('Error processing task alerts: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
