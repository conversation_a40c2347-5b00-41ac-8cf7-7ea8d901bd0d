<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assignment_submissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->onDelete('cascade');
            $table->foreignId('assignment_id')->constrained('assignments')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade'); // student
            $table->decimal('score', 8, 2)->nullable();
            $table->timestamp('submitted_at')->nullable();
            $table->string('status')->default('draft'); // draft, submitted, graded, returned
            $table->text('content')->nullable(); // submission content/text
            $table->string('file_path')->nullable(); // path to uploaded file
            $table->text('feedback')->nullable(); // teacher feedback
            $table->timestamp('graded_at')->nullable();
            $table->foreignId('graded_by')->nullable()->constrained('users')->onDelete('set null'); // teacher who graded
            $table->timestamps();

            // Indexes for better performance
            $table->index(['team_id', 'assignment_id']);
            $table->index(['team_id', 'user_id']);
            $table->index(['assignment_id', 'user_id']);
            $table->index(['status']);
            $table->index(['submitted_at']);
            $table->index(['graded_at']);

            // Unique constraint to prevent duplicate submissions
            $table->unique(['assignment_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assignment_submissions');
    }
};
