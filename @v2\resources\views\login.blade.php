@extends('layouts.frontend')

@section('title', 'เข้าสู่ระบบ - EduNest')

@push('styles')
    @vite('resources/css/login.css')
@endpush

@push('scripts')
    @vite('resources/js/login.js')
@endpush

@section('content')





<div class="login-container">
    <div class="login-card">
        <!-- Header -->
        <div class="login-header">
            <h1 class="login-title gradient-text">เข้าสู่ระบบ</h1>
            <p class="login-subtitle">ยินดีต้อนรับกลับสู่ EduNest</p>
        </div>

        <!-- Social Login Options -->
        @php
            $enabledProviders = \App\Helpers\SocialAuthHelper::getEnabledProviders();
        @endphp

        @if(count($enabledProviders) > 0)
        <div class="social-login-section">
            @foreach($enabledProviders as $provider => $config)
                <button id="{{ $provider }}Login" class="social-button {{ \App\Helpers\SocialAuthHelper::getProviderCssClass($provider) }}">
                    {!! \App\Helpers\SocialAuthHelper::getProviderIcon($provider) !!}
                    {{ \App\Helpers\SocialAuthHelper::getProviderButtonText($provider) }}
                </button>
            @endforeach
        </div>
        @endif

        <!-- Divider -->
        @if(count($enabledProviders) > 0)
        <div class="divider">
            <span>หรือเข้าสู่ระบบด้วยอีเมล</span>
        </div>
        @endif

        <!-- Login Form -->
        <form id="loginForm" class="login-form" method="POST" action="{{ route('login.authenticate') }}">
            @csrf
            <div class="form-group">
                <label for="email" class="form-label">อีเมล</label>
                <input type="email" id="email" name="email" class="form-input"
                       value="{{ old('email') }}" placeholder="กรอกอีเมลของคุณ" required>
                @error('email')
                    <div class="error-message">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="form-group">
                <label for="password" class="form-label">รหัสผ่าน</label>
                <input type="password" id="password" name="password" class="form-input"
                       placeholder="กรอกรหัสผ่าน" required>
                @error('password')
                    <div class="error-message">
                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        {{ $message }}
                    </div>
                @enderror
            </div>

            <div class="form-options">
                <label class="remember-me">
                    <input type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                    <span>จดจำการเข้าสู่ระบบ</span>
                </label>
                <a href="/forgot-password" class="forgot-password">ลืมรหัสผ่าน?</a>
            </div>

            <button type="submit" id="submitBtn" class="submit-button">
                เข้าสู่ระบบ
            </button>
        </form>

        <!-- Footer -->
        <div class="login-footer">
            <p>ยังไม่มีบัญชี? <a href="{{ route('register') }}">สมัครสมาชิก</a></p>
        </div>
    </div>
</div>

<!-- Phone Authentication Modal -->
<div id="phoneModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-2xl w-full max-w-md mx-4 p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800">เข้าสู่ระบบด้วยโทรศัพท์</h3>
            <button id="closePhoneModal" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Phone Number Step -->
        <div id="phoneStep">
            <div class="mb-4">
                <label class="block text-gray-700 font-medium mb-2">หมายเลขโทรศัพท์</label>
                <div class="flex gap-2">
                    <select id="countryCode" class="w-24 px-3 py-2 border-2 border-gray-300 rounded-lg">
                        <option value="+66">🇹🇭 +66</option>
                        <option value="+1">🇺🇸 +1</option>
                        <option value="+44">🇬🇧 +44</option>
                        <option value="+81">🇯🇵 +81</option>
                        <option value="+82">🇰🇷 +82</option>
                        <option value="+86">🇨🇳 +86</option>
                    </select>
                    <input type="tel" id="phoneNumber" class="flex-1 px-3 py-2 border-2 border-gray-300 rounded-lg" placeholder="8xxxxxxxx" maxlength="10">
                </div>
            </div>
            <button id="sendOtp" class="w-full py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transition-all">ส่ง OTP</button>
        </div>

        <!-- OTP Verification Step -->
        <div id="otpStep" class="hidden">
            <div class="mb-4">
                <label class="block text-gray-700 font-medium mb-2">กรอกรหัส OTP</label>
                <p class="text-sm text-gray-600 mb-4">เราได้ส่งรหัส 6 หลักไปยังหมายเลขโทรศัพท์ของคุณ</p>
                <div class="flex gap-2 justify-center">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                    <input type="text" class="otp-input w-12 h-12 text-center border-2 border-gray-300 rounded-lg text-lg font-bold" maxlength="1">
                </div>
                <p class="text-xs text-gray-500 mt-2 text-center">ใช้รหัส 123456 สำหรับทดสอบ</p>
            </div>
            <button id="verifyOtp" class="w-full py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg font-medium hover:shadow-lg transition-all">ยืนยัน OTP</button>
            <button id="resendOtp" class="w-full mt-2 text-sm text-gray-600 hover:text-gray-800">ส่งรหัสใหม่</button>
        </div>
    </div>
</div>

@endsection
