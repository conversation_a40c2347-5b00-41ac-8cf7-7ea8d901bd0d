# Role-Based Authentication System Guide

This guide explains the complete role-based authentication system implemented in EduNest.

## Overview

The system provides:
- **Role-based registration** with 4 user types
- **Social authentication** with configurable providers
- **Phone authentication** with OTP verification
- **Profile completion system** with role-specific fields
- **Automatic role-based redirects** after login/registration

## User Roles

### 1. Student (`student`)
- **Purpose**: For learners and students
- **Required Profile Fields**: 
  - Basic info (name, phone, email)
  - Date of birth, gender
  - Address information
  - Emergency contact details
- **Optional Fields**: Student ID, grade level, class section

### 2. Parent (`parent`)
- **Purpose**: For parents and guardians
- **Required Profile Fields**:
  - Basic info (name, phone, email)
  - Occupation
  - Address information
- **Optional Fields**: Workplace, children's student IDs

### 3. Teacher (`teacher`)
- **Purpose**: For educators and instructors
- **Required Profile Fields**:
  - Basic info (name, phone, email)
  - Qualification
  - Years of experience
  - Teaching subjects (at least one)
- **Optional Fields**: Employee ID, date of birth

### 4. School (`school`)
- **Purpose**: For school administrators
- **Required Profile Fields**:
  - Basic info (name, phone, email)
  - School name and code
  - School address
  - Principal name
- **Optional Fields**: Contact info, statistics, established date

## Registration Flow

### Traditional Registration
1. User visits `/register`
2. Selects role from dropdown
3. Fills basic registration form
4. System creates account and assigns selected role
5. User redirected to profile completion page

### Social Authentication Registration
1. User clicks social login button
2. Completes OAuth flow with provider
3. System creates account with default `student` role
4. User redirected to profile completion page
5. User can change role during profile completion

### Phone Authentication Registration
1. User clicks "Continue with Phone"
2. Enters phone number and receives OTP
3. Verifies OTP (test code: `123456`)
4. System creates account with default `student` role
5. User redirected to profile completion page

## Profile Completion System

### Automatic Redirection
- New users are automatically redirected to profile completion
- Existing users with incomplete profiles are redirected on login
- Profile completion can be skipped but recommended

### Role-Specific Forms
Each role has different required and optional fields:

**Student Profile**:
- Emergency contact information (required)
- Academic information (optional)

**Parent Profile**:
- Professional information (required)
- Children information (optional)

**Teacher Profile**:
- Professional qualifications (required)
- Teaching subjects (required, dynamic input)

**School Profile**:
- School information (required)
- Contact and statistics (optional)

### Progress Tracking
- Real-time completion percentage calculation
- Visual progress bar
- Role-specific completion criteria

## Authentication Features

### Social Login Configuration
```env
# Enable/disable individual providers
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=true
APPLE_LOGIN_ENABLED=true
LINE_LOGIN_ENABLED=true
PHONE_LOGIN_ENABLED=true

# Provider credentials
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
# ... etc for other providers
```

### Phone Authentication
- Country code selection
- OTP verification with 5-minute expiration
- Test OTP: `123456` (for development)
- SMS integration ready (Twilio/AWS/Firebase)

## Dashboard System

### Role-Based Dashboards
After successful login and profile completion, users are redirected to role-specific dashboards:

- **Student**: `/student-dashboard` - Course and assignment management
- **Parent**: `/parent-dashboard` - Children monitoring and communication
- **Teacher**: `/teacher-dashboard` - Class and grade management
- **School**: `/school-dashboard` - School administration
- **Super Admin**: `/admin` - System administration

### Dashboard Features
- Profile completion alerts
- Role-specific quick actions
- Profile management links
- Logout functionality

## Testing the System

### Test Users
Create test users with the command:
```bash
php artisan create:test-user {role} {email}
```

Examples:
```bash
php artisan create:test-<NAME_EMAIL>
php artisan create:test-<NAME_EMAIL>
php artisan create:test-<NAME_EMAIL>
php artisan create:test-<NAME_EMAIL>
```

All test users have password: `password`

### Test Scenarios

1. **Traditional Registration**:
   - Visit `/register`
   - Select role and fill form
   - Complete profile after registration

2. **Social Login**:
   - Click social provider button
   - Complete OAuth flow
   - Change role if needed during profile completion

3. **Phone Authentication**:
   - Click "Continue with Phone"
   - Use test OTP: `123456`
   - Complete profile with role selection

4. **Profile Management**:
   - Edit profile at `/profile/edit`
   - View profile at `/profile`
   - Test role-specific field validation

## Permissions System

### Role Permissions
Each role has specific permissions defined in `RoleSeeder`:

**Student Permissions**:
- View own grades, attendance, reports
- Submit assignments
- Send/view messages
- Manage own profile

**Parent Permissions**:
- View children's grades, attendance, reports
- Send/view messages
- Manage own profile

**Teacher Permissions**:
- View students and parents
- Create/grade assignments
- Manage grades and attendance
- Create reports
- Send/view messages

**School Permissions**:
- Manage users (students, teachers, parents)
- View all data and reports
- System administration

**Super Admin Permissions**:
- All permissions (full system access)

## Security Features

### Profile Completion Middleware
- `EnsureProfileCompleted` middleware redirects incomplete profiles
- Skips profile routes to prevent infinite redirects
- Allows logout and API routes

### Validation
- Role-specific form validation
- File upload validation (profile images)
- Email and phone number validation
- Password strength requirements

### Data Protection
- Social provider tokens are hidden
- Profile images stored securely
- Role-based access control

## File Structure

```
app/
├── Http/Controllers/
│   ├── Auth/RegisterController.php
│   ├── ProfileController.php
│   └── SocialAuthController.php
├── Http/Middleware/
│   └── EnsureProfileCompleted.php
├── Models/
│   ├── User.php
│   └── UserProfile.php
└── Helpers/
    └── SocialAuthHelper.php

resources/views/
├── profile/
│   ├── edit.blade.php
│   ├── show.blade.php
│   └── partials/
│       ├── student-fields.blade.php
│       ├── parent-fields.blade.php
│       ├── teacher-fields.blade.php
│       ├── school-fields.blade.php
│       ├── student-show.blade.php
│       ├── parent-show.blade.php
│       ├── teacher-show.blade.php
│       └── school-show.blade.php
├── login.blade.php
├── register.blade.php
└── dashboard.blade.php

database/
├── migrations/
│   ├── *_create_user_profiles_table.php
│   └── *_add_social_auth_fields_to_users_table.php
└── seeders/
    └── RoleSeeder.php
```

## Next Steps

1. **Customize Role Permissions**: Modify `RoleSeeder` for specific needs
2. **Add More Fields**: Extend `UserProfile` model and forms
3. **Implement SMS**: Configure Twilio/AWS for phone authentication
4. **Add Email Verification**: Implement email verification flow
5. **Create Admin Panel**: Build admin interface for user management
6. **Add File Management**: Implement document upload for profiles
7. **Enhance Security**: Add 2FA and advanced security features

## Support

For questions or issues:
1. Check the configuration files
2. Review the test users and scenarios
3. Examine the role permissions
4. Test the profile completion flow
5. Verify social authentication setup
