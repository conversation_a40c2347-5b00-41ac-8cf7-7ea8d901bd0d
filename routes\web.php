<?php

use App\Livewire\Form;
use App\Http\Controllers\HomepageController;
use App\Http\Controllers\LanguageController;

// Language switching route
\Illuminate\Support\Facades\Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Homepage route
\Illuminate\Support\Facades\Route::get('/', [HomepageController::class, 'index'])->name('homepage');

// Downloads page route
\Illuminate\Support\Facades\Route::get('/downloads', [HomepageController::class, 'downloads'])->name('downloads');

// Sitemap routes
\Illuminate\Support\Facades\Route::get('/sitemap', [\App\Http\Controllers\SitemapController::class, 'webpage'])->name('sitemap.webpage');
\Illuminate\Support\Facades\Route::get('/sitemap.xml', [\App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');

// Pricing routes
\Illuminate\Support\Facades\Route::get('/pricing', [\App\Http\Controllers\PricingController::class, 'index'])->name('pricing');
\Illuminate\Support\Facades\Route::get('/pricing/select/{plan}', [\App\Http\Controllers\PricingController::class, 'selectPlan'])->name('pricing.select');

// Subscription routes
\Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
    \Illuminate\Support\Facades\Route::get('/subscription/checkout/{plan}', [\App\Http\Controllers\SubscriptionController::class, 'checkout'])->name('subscription.checkout');
    \Illuminate\Support\Facades\Route::post('/subscription/process', [\App\Http\Controllers\SubscriptionController::class, 'process'])->name('subscription.process');
    \Illuminate\Support\Facades\Route::get('/subscription/success', [\App\Http\Controllers\SubscriptionController::class, 'success'])->name('subscription.success');
    \Illuminate\Support\Facades\Route::get('/subscription/cancel', [\App\Http\Controllers\SubscriptionController::class, 'cancel'])->name('subscription.cancel');
});

// Payment instruction routes
\Illuminate\Support\Facades\Route::get('/payment/bank-transfer/{uuid}', [\App\Http\Controllers\SubscriptionController::class, 'bankTransferInstructions'])->name('payment.bank-transfer.instructions');
\Illuminate\Support\Facades\Route::get('/payment/promptpay/{uuid}', [\App\Http\Controllers\SubscriptionController::class, 'promptPayQr'])->name('payment.promptpay.qr');

// Slip upload routes
\Illuminate\Support\Facades\Route::post('/payment/slip/upload/{uuid}', [\App\Http\Controllers\SlipUploadController::class, 'upload'])->name('payment.slip.upload');
\Illuminate\Support\Facades\Route::get('/payment/bank-transfer/success/{uuid}', [\App\Http\Controllers\SlipUploadController::class, 'success'])->name('payment.bank-transfer.success');

// Frontend Content Routes
\Illuminate\Support\Facades\Route::prefix('courses')->name('frontend.courses.')->group(function () {
    \Illuminate\Support\Facades\Route::get('/', [\App\Http\Controllers\Frontend\CourseController::class, 'index'])->name('index');
    \Illuminate\Support\Facades\Route::get('/{course}', [\App\Http\Controllers\Frontend\CourseController::class, 'show'])->name('show');
});

\Illuminate\Support\Facades\Route::prefix('books')->name('frontend.books.')->group(function () {
    \Illuminate\Support\Facades\Route::get('/', [\App\Http\Controllers\Frontend\BookController::class, 'index'])->name('index');
    \Illuminate\Support\Facades\Route::get('/{book}', [\App\Http\Controllers\Frontend\BookController::class, 'show'])->name('show');
});

\Illuminate\Support\Facades\Route::prefix('posts')->name('frontend.posts.')->group(function () {
    \Illuminate\Support\Facades\Route::get('/', [\App\Http\Controllers\Frontend\PostController::class, 'index'])->name('index');
    \Illuminate\Support\Facades\Route::get('/{post}', [\App\Http\Controllers\Frontend\PostController::class, 'show'])->name('show');
});

\Illuminate\Support\Facades\Route::prefix('subjects')->name('frontend.subjects.')->group(function () {
    \Illuminate\Support\Facades\Route::get('/', [\App\Http\Controllers\Frontend\SubjectController::class, 'index'])->name('index');
    \Illuminate\Support\Facades\Route::get('/{subject}', [\App\Http\Controllers\Frontend\SubjectController::class, 'show'])->name('show');
});

// Student dashboard page route
\Illuminate\Support\Facades\Route::get('/student-dashboard', [HomepageController::class, 'studentDashboard'])->name('student-dashboard');

// Teacher dashboard page route
\Illuminate\Support\Facades\Route::get('/teacher-dashboard', [HomepageController::class, 'teacherDashboard'])->name('teacher-dashboard');

// Teacher event list page route
\Illuminate\Support\Facades\Route::get('/teacher-eventlist', [HomepageController::class, 'teacherEventList'])->name('teacher-eventlist');

// Teacher event show page route
\Illuminate\Support\Facades\Route::get('/teacher-eventshow', [HomepageController::class, 'teacherEventShow'])->name('teacher-eventshow');

// Teacher timetable page route
\Illuminate\Support\Facades\Route::get('/teacher-timetable', [HomepageController::class, 'teacherTimetable'])->name('teacher-timetable');

// Register page route
\Illuminate\Support\Facades\Route::get('/register', [HomepageController::class, 'register'])->name('register');

// Registration form submission
use App\Http\Controllers\Auth\RegisterController;
\Illuminate\Support\Facades\Route::post('/register', [RegisterController::class, 'store'])->name('register.store');
\Illuminate\Support\Facades\Route::post('/api/register', [RegisterController::class, 'apiStore'])->name('register.api');

// Login page route
\Illuminate\Support\Facades\Route::get('/login', [HomepageController::class, 'login'])->name('login');

// Login form submission
use App\Http\Controllers\Auth\LoginController;
\Illuminate\Support\Facades\Route::post('/login', [LoginController::class, 'authenticate'])->name('login.authenticate');
\Illuminate\Support\Facades\Route::post('/api/login', [LoginController::class, 'apiAuthenticate'])->name('login.api');

// Social Authentication Routes
use App\Http\Controllers\SocialAuthController;

// Social login redirects
\Illuminate\Support\Facades\Route::get('/auth/{provider}', [SocialAuthController::class, 'redirectToProvider'])->name('social.redirect');

// Social login callbacks
\Illuminate\Support\Facades\Route::get('/auth/{provider}/callback', [SocialAuthController::class, 'handleProviderCallback'])->name('social.callback');

// Live Streaming Routes
use App\Http\Controllers\LiveStreamController;
use App\Http\Controllers\Frontend\LiveClassController;
use App\Http\Controllers\Frontend\PublicExamController;
use App\Http\Controllers\Frontend\AssignmentController;
use App\Http\Controllers\PaymentController;

\Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
    // Stream management (for teachers/admins)
    \Illuminate\Support\Facades\Route::get('/live-videos/{liveVideo}/stream', [LiveStreamController::class, 'stream'])->name('live-videos.stream');

    // Watch stream (for viewers)
    \Illuminate\Support\Facades\Route::get('/live-videos/{liveVideo}/watch', [LiveStreamController::class, 'watch'])->name('live-videos.watch');

    // Frontend Live Classes
    \Illuminate\Support\Facades\Route::get('/live-classes', [LiveClassController::class, 'index'])->name('frontend.live-classes.index');
    \Illuminate\Support\Facades\Route::get('/live-classes/upcoming', [LiveClassController::class, 'upcoming'])->name('frontend.live-classes.upcoming');
    \Illuminate\Support\Facades\Route::get('/live-classes/live', [LiveClassController::class, 'live'])->name('frontend.live-classes.live');
    \Illuminate\Support\Facades\Route::get('/live-classes/{liveVideo}', [LiveClassController::class, 'show'])->name('frontend.live-classes.show');
    \Illuminate\Support\Facades\Route::get('/live-classes/{liveVideo}/join', [LiveClassController::class, 'join'])->name('frontend.live-classes.join');

    // Payment Routes
    \Illuminate\Support\Facades\Route::get('/payment/checkout', [PaymentController::class, 'checkout'])->name('payment.checkout');
    \Illuminate\Support\Facades\Route::post('/payment/process', [PaymentController::class, 'process'])->name('payment.process');
    \Illuminate\Support\Facades\Route::get('/payment/success', [PaymentController::class, 'success'])->name('payment.success');
    \Illuminate\Support\Facades\Route::get('/payment/cancel', [PaymentController::class, 'cancel'])->name('payment.cancelled');

    // Profile Edit Route (for frontend access)
    \Illuminate\Support\Facades\Route::get('/profile/edit', function () {
        return redirect('/backend/edit-profile');
    })->name('profile.edit');
});

// Public Exam Routes (some require auth, some don't)
\Illuminate\Support\Facades\Route::prefix('exams')->name('frontend.exams.')->group(function () {
    // Public routes (no auth required)
    \Illuminate\Support\Facades\Route::get('/', [PublicExamController::class, 'index'])->name('index');
    \Illuminate\Support\Facades\Route::get('/{exam}', [PublicExamController::class, 'show'])->name('show');

    // Auth required routes
    \Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
        \Illuminate\Support\Facades\Route::post('/{exam}/start', [PublicExamController::class, 'start'])->name('start');
        \Illuminate\Support\Facades\Route::get('/{exam}/take/{attempt}', [PublicExamController::class, 'take'])->name('take');
        \Illuminate\Support\Facades\Route::post('/{exam}/take/{attempt}/submit', [PublicExamController::class, 'submit'])->name('submit');
        \Illuminate\Support\Facades\Route::get('/{exam}/result/{attempt}', [PublicExamController::class, 'result'])->name('result');
    });
});

// Assignment Routes (auth required)
\Illuminate\Support\Facades\Route::middleware(['auth'])->prefix('assignments')->name('frontend.assignments.')->group(function () {
    \Illuminate\Support\Facades\Route::get('/', [AssignmentController::class, 'index'])->name('index');
    \Illuminate\Support\Facades\Route::get('/{assignment}', [AssignmentController::class, 'show'])->name('show');
    \Illuminate\Support\Facades\Route::post('/{assignment}/start', [AssignmentController::class, 'start'])->name('start');
    \Illuminate\Support\Facades\Route::get('/{assignment}/work', [AssignmentController::class, 'work'])->name('work');
    \Illuminate\Support\Facades\Route::post('/{assignment}/submit', [AssignmentController::class, 'submit'])->name('submit');
});

// Backend Exam Attempt Details Route (for Filament)
\Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
    \Illuminate\Support\Facades\Route::get('/backend/{tenant}/exams/{exam}/attempts/{user}',
        \App\Filament\Resources\Exam\ExamResource\Pages\AttemptDetails::class)
        ->name('filament.backend.resources.exams.attempt-details');
});

// Testing Routes (only available when APP_DEBUG=true)
\Illuminate\Support\Facades\Route::prefix('test')->name('test.')->middleware(['web'])->group(function () {
    // Main testing dashboard
    \Illuminate\Support\Facades\Route::get('/', [\App\Http\Controllers\TestController::class, 'dashboard'])->name('dashboard');

    // Permission testing (requires auth)
    \Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
        \Illuminate\Support\Facades\Route::get('/permissions', [\App\Http\Controllers\TestController::class, 'index'])->name('permissions');
        \Illuminate\Support\Facades\Route::post('/switch-user', [\App\Http\Controllers\TestController::class, 'switchUser'])->name('switch-user');
        \Illuminate\Support\Facades\Route::get('/switch-back', [\App\Http\Controllers\TestController::class, 'switchBack'])->name('switch-back');
        \Illuminate\Support\Facades\Route::post('/resource', [\App\Http\Controllers\TestController::class, 'testResource'])->name('resource');
        \Illuminate\Support\Facades\Route::get('/all-resources', [\App\Http\Controllers\TestController::class, 'testAllResources'])->name('all-resources');
        \Illuminate\Support\Facades\Route::post('/resource-urls', [\App\Http\Controllers\TestController::class, 'getResourceUrls'])->name('resource-urls');
    });

    // Demo pages (no auth required for easier testing)
    \Illuminate\Support\Facades\Route::get('/svg-demo', [\App\Http\Controllers\TestController::class, 'svgDemo'])->name('svg-demo');
    \Illuminate\Support\Facades\Route::get('/safeicon-demo', [\App\Http\Controllers\TestController::class, 'safeIconDemo'])->name('safeicon-demo');
});

// Phone authentication API routes
\Illuminate\Support\Facades\Route::post('/api/auth/phone/send-otp', [SocialAuthController::class, 'sendPhoneOtp'])->name('phone.send-otp');
\Illuminate\Support\Facades\Route::post('/api/auth/phone/verify-otp', [SocialAuthController::class, 'verifyPhoneOtp'])->name('phone.verify-otp');

// Profile Routes
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SocialAccountController;

\Illuminate\Support\Facades\Route::middleware(['auth'])->group(function () {
    \Illuminate\Support\Facades\Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    \Illuminate\Support\Facades\Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    \Illuminate\Support\Facades\Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    \Illuminate\Support\Facades\Route::post('/profile/skip', [ProfileController::class, 'skip'])->name('profile.skip');

    // Social Account Management Routes
    \Illuminate\Support\Facades\Route::get('/profile/connect/{provider}', [SocialAccountController::class, 'connect'])->name('social.connect');
    \Illuminate\Support\Facades\Route::get('/profile/connect/{provider}/callback', [SocialAccountController::class, 'callback'])->name('social.connect.callback');
    \Illuminate\Support\Facades\Route::post('/profile/disconnect/{provider}', [SocialAccountController::class, 'disconnect'])->name('social.disconnect');
});

// Logout Route
\Illuminate\Support\Facades\Route::post('/logout', function () {
    Auth::logout();
    request()->session()->invalidate();
    request()->session()->regenerateToken();
    return redirect('/');
})->name('logout');

// Debug route (remove in production)
\Illuminate\Support\Facades\Route::get('/debug-profile', function () {
    if (!Auth::check()) {
        return 'Not logged in';
    }

    $user = Auth::user();
    $profile = $user->getOrCreateProfile();
    $role = $user->getPrimaryRole();

    return [
        'user_id' => $user->id,
        'user_email' => $user->email,
        'user_name' => $user->name,
        'role' => $role,
        'profile_exists' => !!$profile,
        'profile_id' => $profile?->id,
        'needs_completion' => $user->needsProfileCompletion(),
        'dashboard_route' => $user->getDashboardRoute(),
        'has_profile_image' => !!($profile && $profile->profile_image),
        'has_avatar' => !!$user->avatar,
        'is_super_admin' => $user->hasRole('super_admin'),
        'is_school' => $user->hasRole('school'),
    ];
})->middleware('auth');

// Legacy routes moved to /test/* - these can be removed in production

// Dashboard Routes (for testing)
\Illuminate\Support\Facades\Route::middleware(['auth', 'profile.complete'])->group(function () {
    \Illuminate\Support\Facades\Route::get('/dashboard', function () {
        return view('dashboard', ['user' => auth()->user()]);
    })->name('dashboard');

    \Illuminate\Support\Facades\Route::get('/student-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'student']);
    })->name('student-dashboard');

    \Illuminate\Support\Facades\Route::get('/parent-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'parent']);
    })->name('parent-dashboard');

    \Illuminate\Support\Facades\Route::get('/teacher-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'teacher']);
    })->name('teacher-dashboard');

    \Illuminate\Support\Facades\Route::get('/school-dashboard', function () {
        return view('dashboard', ['user' => auth()->user(), 'role' => 'school']);
    })->name('school-dashboard');
});

\Illuminate\Support\Facades\Route::get('form', Form::class);

// Legacy debug routes - moved to TestController

// Debug route to check available teams
\Illuminate\Support\Facades\Route::get('/debug-teams', function () {
    return response()->json([
        'all_teams' => \App\Models\Team::all(['id', 'name', 'slug']),
        'current_user' => auth()->user()?->only(['id', 'name', 'email', 'team_id']),
        'current_user_team' => auth()->user()?->team?->only(['id', 'name', 'slug']),
        'current_user_roles' => auth()->user()?->roles?->pluck('name'),
    ]);
});

// All debug routes moved to /test/* for better organization
// Access the testing dashboard at: /test
