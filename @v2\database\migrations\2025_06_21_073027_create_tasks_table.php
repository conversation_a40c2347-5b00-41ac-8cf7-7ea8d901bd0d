<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('team_id')->constrained('teams')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete(); // Task creator/owner
            $table->foreignId('assigned_to')->nullable()->constrained('users')->nullOnDelete(); // Assigned user
            $table->string('title');
            $table->text('description')->nullable();
            $table->dateTime('start_datetime');
            $table->dateTime('end_datetime')->nullable();
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->string('color', 7)->default('#3B82F6'); // Hex color for timetable display

            // Alert settings
            $table->boolean('has_alert')->default(false);
            $table->dateTime('alert_datetime')->nullable();
            $table->integer('alert_minutes_before')->nullable(); // Minutes before start_datetime

            // Recurring settings
            $table->boolean('is_recurring')->default(false);
            $table->json('recurring_pattern')->nullable(); // Flexible recurring pattern storage
            $table->date('recurring_end_date')->nullable();

            // Notification settings
            $table->boolean('alert_sound')->default(true);
            $table->boolean('alert_email')->default(false);
            $table->boolean('alert_line')->default(false);
            $table->string('line_user_id')->nullable(); // LINE user ID for notifications

            // Timetable positioning
            $table->integer('position_x')->nullable(); // X position in timetable
            $table->integer('position_y')->nullable(); // Y position in timetable
            $table->integer('width')->nullable(); // Width in timetable
            $table->integer('height')->nullable(); // Height in timetable

            // Additional metadata
            $table->json('metadata')->nullable(); // Additional flexible data
            $table->boolean('is_all_day')->default(false);
            $table->string('location')->nullable();
            $table->text('notes')->nullable();

            $table->timestamps();

            // Indexes for performance
            $table->index(['team_id', 'start_datetime']);
            $table->index(['user_id', 'start_datetime']);
            $table->index(['assigned_to', 'start_datetime']);
            $table->index(['start_datetime', 'end_datetime']);
            $table->index(['status', 'priority']);
            $table->index(['has_alert', 'alert_datetime']);
            $table->index(['is_recurring', 'recurring_end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};
