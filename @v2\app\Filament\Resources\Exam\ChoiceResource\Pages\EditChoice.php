<?php

namespace App\Filament\Resources\Exam\ChoiceResource\Pages;

use App\Filament\Resources\Exam\ChoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditChoice extends EditRecord
{
    protected static string $resource = ChoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
