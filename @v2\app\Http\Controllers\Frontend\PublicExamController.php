<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Exam\Exam;
use App\Models\Exam\Attempt;
use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PublicExamController extends Controller
{
    /**
     * Display a listing of public exams
     */
    public function index(Request $request)
    {
        $query = Exam::with(['subject', 'teacher'])
            ->public()
            ->active()
            ->available();

        // Filter by subject if provided
        if ($request->filled('subject')) {
            $query->where('subject_id', $request->subject);
        }

        // Filter by grade level if provided
        if ($request->filled('grade_level')) {
            $query->where('grade_level', $request->grade_level);
        }

        // Filter by type if provided
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Search by title
        if ($request->filled('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        $exams = $query->orderBy('created_at', 'desc')->paginate(12);

        // Get filter options
        $subjects = Subject::whereHas('exams', function ($q) {
            $q->public()->active();
        })->get();

        $gradeLevels = Exam::public()->active()
            ->whereNotNull('grade_level')
            ->distinct()
            ->pluck('grade_level')
            ->filter()
            ->sort()
            ->values();

        return view('frontend.exams.index', compact('exams', 'subjects', 'gradeLevels'));
    }

    /**
     * Display the specified exam
     */
    public function show(Exam $exam)
    {
        // Check if exam is public and available
        if (!$exam->isPublic() || !$exam->isAvailable()) {
            abort(404);
        }

        $exam->load(['subject', 'teacher', 'questions.choices']);

        // Get user's attempts if logged in
        $userAttempts = null;
        if (Auth::check()) {
            $userAttempts = $exam->attempts()
                ->where('user_id', Auth::id())
                ->orderBy('created_at', 'desc')
                ->get();
        }

        return view('frontend.exams.show', compact('exam', 'userAttempts'));
    }

    /**
     * Start taking an exam
     */
    public function start(Exam $exam)
    {
        // Must be logged in to take exam
        if (!Auth::check()) {
            return redirect()->route('login')->with('message', 'Please login to take this exam.');
        }

        // Check if exam is public and available
        if (!$exam->isPublic() || !$exam->isAvailable()) {
            abort(404);
        }

        $user = Auth::user();

        // Check if user can take another attempt
        $existingAttempts = $exam->attempts()->where('user_id', $user->id)->count();
        
        if (!$exam->allow_multiple_attempts && $existingAttempts > 0) {
            return redirect()->route('frontend.exams.show', $exam)
                ->with('error', 'You have already taken this exam and multiple attempts are not allowed.');
        }

        // Create new attempt
        $attempt = Attempt::create([
            'team_id' => $exam->team_id,
            'exam_id' => $exam->id,
            'user_id' => $user->id,
            'attempt_number' => $existingAttempts + 1,
            'status' => 'in_progress',
            'started_at' => now(),
            'exam_snapshot' => $exam->toArray(), // Store exam state at time of attempt
        ]);

        return redirect()->route('frontend.exams.take', [$exam, $attempt]);
    }

    /**
     * Take the exam
     */
    public function take(Exam $exam, Attempt $attempt)
    {
        // Must be logged in and own this attempt
        if (!Auth::check() || $attempt->user_id !== Auth::id()) {
            abort(403);
        }

        // Check if exam is public and available
        if (!$exam->isPublic() || !$exam->isAvailable()) {
            abort(404);
        }

        // Check if attempt is still in progress
        if (!$attempt->isInProgress()) {
            return redirect()->route('frontend.exams.result', [$exam, $attempt]);
        }

        // Check time limit
        if ($exam->time_limit && $attempt->started_at->addMinutes($exam->time_limit)->isPast()) {
            // Auto-submit expired attempt
            $attempt->update([
                'status' => 'expired',
                'submitted_at' => now(),
            ]);
            
            return redirect()->route('frontend.exams.result', [$exam, $attempt])
                ->with('warning', 'Time limit exceeded. Your exam has been automatically submitted.');
        }

        $exam->load(['questions.choices']);
        
        // Get existing answers
        $existingAnswers = $attempt->answers()->with('selectedChoice')->get()->keyBy('question_id');

        return view('frontend.exams.take', compact('exam', 'attempt', 'existingAnswers'));
    }

    /**
     * Submit exam answers
     */
    public function submit(Request $request, Exam $exam, Attempt $attempt)
    {
        // Must be logged in and own this attempt
        if (!Auth::check() || $attempt->user_id !== Auth::id()) {
            abort(403);
        }

        // Check if attempt is still in progress
        if (!$attempt->isInProgress()) {
            return redirect()->route('frontend.exams.result', [$exam, $attempt]);
        }

        $answers = $request->input('answers', []);

        DB::transaction(function () use ($exam, $attempt, $answers) {
            foreach ($answers as $questionId => $answerData) {
                $question = $exam->questions()->find($questionId);
                if (!$question) continue;

                $attempt->answers()->updateOrCreate(
                    [
                        'question_id' => $questionId,
                    ],
                    [
                        'team_id' => $exam->team_id,
                        'selected_choice_id' => $answerData['choice_id'] ?? null,
                        'answer_text' => $answerData['text'] ?? null,
                        'answered_at' => now(),
                        'max_score' => $question->points,
                    ]
                );
            }

            // Update attempt status
            $attempt->update([
                'status' => 'submitted',
                'submitted_at' => now(),
            ]);

            // Calculate auto scores for multiple choice questions
            $attempt->updateScores();
        });

        return redirect()->route('frontend.exams.result', [$exam, $attempt])
            ->with('success', 'Your exam has been submitted successfully!');
    }

    /**
     * Show exam result
     */
    public function result(Exam $exam, Attempt $attempt)
    {
        // Must be logged in and own this attempt
        if (!Auth::check() || $attempt->user_id !== Auth::id()) {
            abort(403);
        }

        // Check if exam is public
        if (!$exam->isPublic()) {
            abort(404);
        }

        $attempt->load(['answers.question.choices', 'answers.selectedChoice']);

        return view('frontend.exams.result', compact('exam', 'attempt'));
    }
}
