<?php namespace MyFatoorah\Library\API; use MyFatoorah\Library\MyFatoorah; class  MyFatoorahSupplier extends MyFatoorah{public function getSupplierDashboard($supplierCode){$url=$this->apiURL.'/v2/GetSupplierDashboard?SupplierCode='.$supplierCode;return $this->callAPI($url,null,null,"Get Supplier Documents");}public function isSupplierApproved($supplierCode){$supplier=$this->getSupplierDashboard($supplierCode);return($supplier->IsApproved&&$supplier->IsActive);}}