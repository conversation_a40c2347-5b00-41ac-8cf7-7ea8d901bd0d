<?php

namespace App\Providers;

use App\Models\Team;
use App\Models\User;
use App\Observers\TeamObserver;
use App\Observers\UserObserver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Model::unguard();

        // Register observers
        User::observe(UserObserver::class);
        Team::observe(TeamObserver::class);

        if (app()->environment('production')) {
            URL::forceScheme('https');
        }


        // Blade::component('filament-media-manager', \App\Livewire\FilamentMediaManager::class);
    }
}
