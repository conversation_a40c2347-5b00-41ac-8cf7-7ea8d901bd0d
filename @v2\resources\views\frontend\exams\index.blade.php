@extends('layouts.frontend')

@section('title', 'Public Exams & Exercises')

@section('content')
<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Public Exams & Exercises</h1>
            <p class="text-lg text-gray-600">Test your knowledge with our collection of public exams and exercises</p>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
            <form method="GET" action="{{ route('frontend.exams.index') }}" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="{{ request('search') }}"
                           placeholder="Search exams..."
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <!-- Subject Filter -->
                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                    <select id="subject" 
                            name="subject" 
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Subjects</option>
                        @foreach($subjects as $subject)
                            <option value="{{ $subject->id }}" {{ request('subject') == $subject->id ? 'selected' : '' }}>
                                {{ $subject->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Grade Level Filter -->
                <div>
                    <label for="grade_level" class="block text-sm font-medium text-gray-700 mb-2">Grade Level</label>
                    <select id="grade_level" 
                            name="grade_level" 
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Grades</option>
                        @foreach($gradeLevels as $grade)
                            <option value="{{ $grade }}" {{ request('grade_level') == $grade ? 'selected' : '' }}>
                                {{ $grade }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Type Filter -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                    <select id="type" 
                            name="type" 
                            class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">All Types</option>
                        <option value="exercise" {{ request('type') == 'exercise' ? 'selected' : '' }}>Exercise</option>
                        <option value="exam" {{ request('type') == 'exam' ? 'selected' : '' }}>Exam</option>
                    </select>
                </div>

                <!-- Filter Buttons -->
                <div class="md:col-span-4 flex justify-end space-x-3">
                    <button type="submit" 
                            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Apply Filters
                    </button>
                    <a href="{{ route('frontend.exams.index') }}" 
                       class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Clear
                    </a>
                </div>
            </form>
        </div>

        <!-- Exams Grid -->
        @if($exams->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                @foreach($exams as $exam)
                    <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                        <div class="p-6">
                            <!-- Header -->
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $exam->title }}</h3>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        @if($exam->subject)
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{{ $exam->subject->name }}</span>
                                        @endif
                                        @if($exam->grade_level)
                                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full">{{ $exam->grade_level }}</span>
                                        @endif
                                        <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full capitalize">{{ $exam->type }}</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Description -->
                            @if($exam->description)
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ $exam->description }}</p>
                            @endif

                            <!-- Stats -->
                            <div class="grid grid-cols-2 gap-4 mb-4 text-sm">
                                <div>
                                    <span class="text-gray-500">Questions:</span>
                                    <span class="font-medium">{{ $exam->total_questions }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Time:</span>
                                    <span class="font-medium">{{ $exam->time_limit ? $exam->time_limit . ' min' : 'No limit' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Score:</span>
                                    <span class="font-medium">{{ $exam->total_score }} pts</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Teacher:</span>
                                    <span class="font-medium">{{ $exam->teacher->name }}</span>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="flex justify-end">
                                <a href="{{ route('frontend.exams.show', $exam) }}" 
                                   class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm font-medium">
                                    View Details
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="flex justify-center">
                {{ $exams->appends(request()->query())->links() }}
            </div>
        @else
            <!-- No Results -->
            <div class="text-center py-12">
                <div class="max-w-md mx-auto">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No exams found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or check back later for new exams.</p>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
