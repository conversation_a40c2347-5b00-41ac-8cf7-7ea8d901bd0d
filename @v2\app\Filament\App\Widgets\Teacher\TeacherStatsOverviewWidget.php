<?php

namespace App\Filament\App\Widgets\Teacher;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class TeacherStatsOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $user = Auth::user();
        
        if (!$user || !$user->hasRole('teacher')) {
            return [];
        }

        // Placeholder data - replace with actual data from database
        return [
            Stat::make('จำนวนชั่วโมงสอนสัปดาห์นี้', '18 ชั่วโมง')
                ->description('ชั่วโมงการสอนในสัปดาห์')
                ->descriptionIcon('heroicon-m-clock')
                ->color('primary'),

            Stat::make('จำนวนนักเรียนทั้งหมด', '120 คน')
                ->description('นักเรียนในความดูแล')
                ->descriptionIcon('heroicon-m-users')
                ->color('success'),

            Stat::make('การบ้านที่ต้องตรวจ', '24 ชิ้น')
                ->description('รอการตรวจสอบ')
                ->descriptionIcon('heroicon-m-clipboard-document-check')
                ->color('warning'),

            Stat::make('แผนการสอนที่ต้องเตรียม', '3 แผน')
                ->description('สำหรับสัปดาห์หน้า')
                ->descriptionIcon('heroicon-m-document-text')
                ->color('info'),
        ];
    }

    protected function getColumns(): int
    {
        return 4;
    }

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('teacher');
    }
}
