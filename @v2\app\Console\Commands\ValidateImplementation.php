<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Controllers\LanguageController;
use App\Filament\Resources\Exam\ExamResource;
use App\Models\Exam\Exam;
use App\Models\Exam\Question;
use App\Models\Exam\Choice;

class ValidateImplementation extends Command
{
    protected $signature = 'validate:implementation';
    protected $description = 'Validate the Google Forms-like question builder and language switcher implementation';

    public function handle()
    {
        $this->info('🔍 Validating Implementation...');
        $this->newLine();

        $this->validateRoutes();
        $this->validateControllers();
        $this->validateViews();
        $this->validateModels();
        $this->validateResources();
        $this->validateMultiTenancy();

        $this->newLine();
        $this->info('✅ Validation completed!');
    }

    private function validateRoutes()
    {
        $this->info('📍 Checking Routes...');

        // Check language route
        $languageRoute = Route::getRoutes()->getByName('language.switch');
        if ($languageRoute) {
            $this->line('  ✅ Language switch route exists');
        } else {
            $this->error('  ❌ Language switch route missing');
        }

        // Check exam routes
        $examRoutes = collect(Route::getRoutes())->filter(function ($route) {
            return str_contains($route->getName() ?? '', 'filament.backend.resources.exam.exams');
        });

        if ($examRoutes->count() >= 3) {
            $this->line('  ✅ Exam resource routes exist (index, create, edit)');
        } else {
            $this->error('  ❌ Exam resource routes incomplete');
        }
    }

    private function validateControllers()
    {
        $this->info('🎮 Checking Controllers...');

        if (class_exists(LanguageController::class)) {
            $this->line('  ✅ LanguageController exists');
            
            if (method_exists(LanguageController::class, 'switch')) {
                $this->line('  ✅ LanguageController::switch method exists');
            } else {
                $this->error('  ❌ LanguageController::switch method missing');
            }
        } else {
            $this->error('  ❌ LanguageController missing');
        }
    }

    private function validateViews()
    {
        $this->info('👁️ Checking Views...');

        $views = [
            'components.language-switcher',
            'homepage',
            'downloads',
            'student-dashboard',
            'teacher-dashboard',
            'register',
            'login'
        ];

        foreach ($views as $view) {
            if (View::exists($view)) {
                $this->line("  ✅ View '{$view}' exists");
            } else {
                $this->error("  ❌ View '{$view}' missing");
            }
        }
    }

    private function validateModels()
    {
        $this->info('📊 Checking Models...');

        $models = [
            Exam::class => 'Exam',
            Question::class => 'Question',
            Choice::class => 'Choice'
        ];

        foreach ($models as $class => $name) {
            if (class_exists($class)) {
                $this->line("  ✅ {$name} model exists");
                
                // Check fillable fields
                $model = new $class;
                if (!empty($model->getFillable())) {
                    $this->line("  ✅ {$name} model has fillable fields");
                } else {
                    $this->warn("  ⚠️ {$name} model has no fillable fields");
                }
            } else {
                $this->error("  ❌ {$name} model missing");
            }
        }
    }

    private function validateResources()
    {
        $this->info('🏗️ Checking Filament Resources...');

        if (class_exists(ExamResource::class)) {
            $this->line('  ✅ ExamResource exists');
            
            // Check if form method exists
            if (method_exists(ExamResource::class, 'form')) {
                $this->line('  ✅ ExamResource::form method exists');
            } else {
                $this->error('  ❌ ExamResource::form method missing');
            }

            // Check if table method exists
            if (method_exists(ExamResource::class, 'table')) {
                $this->line('  ✅ ExamResource::table method exists');
            } else {
                $this->error('  ❌ ExamResource::table method missing');
            }

            // Check tenant scoping
            if (method_exists(ExamResource::class, 'getEloquentQuery')) {
                $this->line('  ✅ ExamResource has tenant scoping');
            } else {
                $this->warn('  ⚠️ ExamResource may not have proper tenant scoping');
            }
        } else {
            $this->error('  ❌ ExamResource missing');
        }
    }

    private function validateMultiTenancy()
    {
        $this->info('🏢 Checking Multi-Tenancy...');

        // Check if models have team_id in fillable
        $exam = new Exam();
        if (in_array('team_id', $exam->getFillable())) {
            $this->line('  ✅ Exam model supports multi-tenancy');
        } else {
            $this->error('  ❌ Exam model missing team_id in fillable');
        }

        $question = new Question();
        if (in_array('team_id', $question->getFillable())) {
            $this->line('  ✅ Question model supports multi-tenancy');
        } else {
            $this->error('  ❌ Question model missing team_id in fillable');
        }

        // Check global scopes
        try {
            $questionQuery = Question::query();
            $this->line('  ✅ Question model can be queried');
        } catch (\Exception $e) {
            $this->error('  ❌ Question model query failed: ' . $e->getMessage());
        }

        try {
            $choiceQuery = Choice::query();
            $this->line('  ✅ Choice model can be queried');
        } catch (\Exception $e) {
            $this->error('  ❌ Choice model query failed: ' . $e->getMessage());
        }
    }
}
