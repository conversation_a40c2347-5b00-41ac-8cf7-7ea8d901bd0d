<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Subject;

class SubjectController extends Controller
{
    public function index()
    {
        // Subjects are global, no tenant scoping needed
        $subjects = Subject::where('is_active', true)
            ->withCount(['courses', 'books'])
            ->orderBy('name')
            ->paginate(12);

        return view('frontend.subjects.index', compact('subjects'));
    }

    public function show($subjectId)
    {
        // Find subject (subjects are global, no scoping needed)
        $subject = Subject::findOrFail($subjectId);

        // Check if subject is active
        if (!$subject->is_active) {
            abort(404);
        }

        // Load related courses and books without tenant scoping
        $subject->loadMissing([
            'courses' => function ($query) {
                $query->withoutGlobalScopes()->where('is_active', true)->limit(6);
            },
            'books' => function ($query) {
                $query->withoutGlobalScopes()->where('is_active', true)->limit(6);
            }
        ]);

        return view('frontend.subjects.show', compact('subject'));
    }
}
