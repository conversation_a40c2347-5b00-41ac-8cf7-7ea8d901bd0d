<?php

namespace App\Services;

use App\Models\LiveVideo;
use App\Models\MediaManager\Media;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Exception;

class LiveVideoRecordingService
{
    /**
     * Process and save a live video recording
     */
    public function processRecording(LiveVideo $liveVideo, UploadedFile $recordingFile): ?Media
    {
        try {
            // Validate the recording file
            $this->validateRecordingFile($recordingFile);

            // Generate filename
            $filename = $this->generateRecordingFilename($liveVideo, $recordingFile);

            // Store the file
            $path = $this->storeRecordingFile($recordingFile, $filename);

            // Create media record
            $media = $this->createMediaRecord($liveVideo, $recordingFile, $path);

            // Update live video record
            $this->updateLiveVideoRecord($liveVideo, $media, $path);

            // Log successful processing
            Log::info('Live video recording processed successfully', [
                'live_video_id' => $liveVideo->id,
                'media_id' => $media->id,
                'file_path' => $path
            ]);

            return $media;

        } catch (Exception $e) {
            Log::error('Failed to process live video recording', [
                'live_video_id' => $liveVideo->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Automatically process recording when live video ends
     */
    public function autoProcessRecording(LiveVideo $liveVideo): void
    {
        // This would be called by a background job or webhook
        // when the streaming service indicates recording is complete
        
        try {
            // Check if recording is enabled
            if (!$liveVideo->is_recording_enabled) {
                return;
            }

            // Check if recording already exists
            if ($liveVideo->recorded_media_id) {
                return;
            }

            // In a real implementation, this would:
            // 1. Download the recording from the streaming service
            // 2. Process and optimize the video
            // 3. Generate thumbnails
            // 4. Store in the media library

            Log::info('Auto-processing recording for live video', [
                'live_video_id' => $liveVideo->id
            ]);

            // For now, just mark that auto-processing was attempted
            $liveVideo->update([
                'metadata' => array_merge($liveVideo->metadata ?? [], [
                    'auto_recording_attempted' => true,
                    'auto_recording_attempted_at' => now()->toISOString()
                ])
            ]);

        } catch (Exception $e) {
            Log::error('Failed to auto-process live video recording', [
                'live_video_id' => $liveVideo->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Generate thumbnail from video recording
     */
    public function generateThumbnail(LiveVideo $liveVideo, string $videoPath): ?string
    {
        try {
            // This would use FFmpeg or similar to generate a thumbnail
            // For now, return null as this requires additional setup
            
            Log::info('Thumbnail generation requested', [
                'live_video_id' => $liveVideo->id,
                'video_path' => $videoPath
            ]);

            return null;

        } catch (Exception $e) {
            Log::error('Failed to generate thumbnail', [
                'live_video_id' => $liveVideo->id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Clean up old recordings based on retention policy
     */
    public function cleanupOldRecordings(int $retentionDays = 90): int
    {
        try {
            $cutoffDate = now()->subDays($retentionDays);
            
            $oldLiveVideos = LiveVideo::where('status', 'ended')
                ->where('actual_end_time', '<', $cutoffDate)
                ->whereNotNull('recorded_media_id')
                ->get();

            $deletedCount = 0;

            foreach ($oldLiveVideos as $liveVideo) {
                if ($this->deleteRecording($liveVideo)) {
                    $deletedCount++;
                }
            }

            Log::info('Cleaned up old recordings', [
                'retention_days' => $retentionDays,
                'deleted_count' => $deletedCount
            ]);

            return $deletedCount;

        } catch (Exception $e) {
            Log::error('Failed to cleanup old recordings', [
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Delete a recording and its associated media
     */
    public function deleteRecording(LiveVideo $liveVideo): bool
    {
        try {
            if (!$liveVideo->recorded_media_id) {
                return false;
            }

            $media = $liveVideo->recordedMedia;
            
            if ($media) {
                // Delete the physical file
                if ($media->disk && $media->file_name) {
                    Storage::disk($media->disk)->delete($media->file_name);
                }

                // Delete the media record
                $media->delete();
            }

            // Update live video record
            $liveVideo->update([
                'recorded_media_id' => null,
                'recording_path' => null
            ]);

            Log::info('Recording deleted successfully', [
                'live_video_id' => $liveVideo->id,
                'media_id' => $media?->id
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to delete recording', [
                'live_video_id' => $liveVideo->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Validate the uploaded recording file
     */
    private function validateRecordingFile(UploadedFile $file): void
    {
        $allowedMimes = ['video/mp4', 'video/webm', 'video/avi', 'video/mov'];
        $maxSize = 1024 * 1024 * 1024; // 1GB

        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new Exception('Invalid file type. Only video files are allowed.');
        }

        if ($file->getSize() > $maxSize) {
            throw new Exception('File size exceeds maximum allowed size of 1GB.');
        }
    }

    /**
     * Generate a unique filename for the recording
     */
    private function generateRecordingFilename(LiveVideo $liveVideo, UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        
        return "live-lesson-{$liveVideo->id}-{$timestamp}.{$extension}";
    }

    /**
     * Store the recording file
     */
    private function storeRecordingFile(UploadedFile $file, string $filename): string
    {
        return $file->storeAs('live-recordings', $filename, 'public');
    }

    /**
     * Create a media record for the recording
     */
    private function createMediaRecord(LiveVideo $liveVideo, UploadedFile $file, string $path): Media
    {
        return $liveVideo->addMedia($file)
            ->withCustomProperties([
                'title' => $liveVideo->title . ' - Recording',
                'description' => 'Recorded live lesson from ' . $liveVideo->actual_start_time?->format('M j, Y g:i A'),
                'team_id' => $liveVideo->team_id,
                'created_by_user' => auth()->user()?->name ?? 'System',
                'live_video_id' => $liveVideo->id,
                'duration_minutes' => $liveVideo->duration,
                'recording_date' => $liveVideo->actual_start_time?->toDateString(),
            ])
            ->toMediaCollection('recordings');
    }

    /**
     * Update the live video record with recording information
     */
    private function updateLiveVideoRecord(LiveVideo $liveVideo, Media $media, string $path): void
    {
        $liveVideo->update([
            'recording_path' => $path,
            'recorded_media_id' => $media->id,
            'metadata' => array_merge($liveVideo->metadata ?? [], [
                'recording_processed_at' => now()->toISOString(),
                'recording_file_size' => $media->size,
                'recording_mime_type' => $media->mime_type,
            ])
        ]);
    }
}
