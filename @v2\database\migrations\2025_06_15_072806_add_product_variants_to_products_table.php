<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shop_products', function (Blueprint $table) {
            // Add product variants for flexible option combinations
            $table->json('product_variants')->nullable()->after('wholesale_pricing');

            // Add variant option labels for team customization
            $table->string('option1_label')->nullable()->after('product_variants');
            $table->string('option2_label')->nullable()->after('option1_label');
            
            $table->json('option1_variants')->nullable()->after('option2_label');
            $table->json('option2_variants')->nullable()->after('option1_variants');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shop_products', function (Blueprint $table) {
            $table->dropColumn([
                'product_variants',
                'option1_label',
                'option2_label'
            ]);
        });
    }
};
