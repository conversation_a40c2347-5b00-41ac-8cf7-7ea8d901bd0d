<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Streaming - {{ $liveVideo->title }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
        }
        .video-container video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{{ $liveVideo->title }}</h1>
                        <p class="text-sm text-gray-600">{{ $liveVideo->description }}</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span id="viewer-count" class="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-eye mr-1"></i> 0 viewers
                        </span>
                        <span id="live-status" class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                            <i class="fas fa-circle mr-1 animate-pulse"></i> LIVE
                        </span>
                    </div>
                </div>
            </div>
        </header>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Main Video Area -->
                <div class="lg:col-span-3">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <!-- Video Container -->
                        <div class="video-container bg-black">
                            <video id="localVideo" autoplay muted playsinline class="hidden"></video>
                            <video id="remoteVideo" autoplay playsinline class="hidden"></video>
                            
                            <!-- Placeholder when not streaming -->
                            <div id="video-placeholder" class="absolute inset-0 flex items-center justify-center bg-gray-900">
                                <div class="text-center text-white">
                                    <i class="fas fa-video text-6xl mb-4 opacity-50"></i>
                                    <h3 class="text-xl font-semibold mb-2">Stream Not Started</h3>
                                    <p class="text-gray-300">Click "Start Streaming" to begin your live lesson</p>
                                </div>
                            </div>
                        </div>

                        <!-- Stream Controls -->
                        <div class="p-4 bg-gray-50 border-t">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button id="startStreamBtn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-play mr-2"></i> Start Streaming
                                    </button>
                                    <button id="stopStreamBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg font-medium transition-colors hidden">
                                        <i class="fas fa-stop mr-2"></i> Stop Streaming
                                    </button>
                                    <button id="toggleMicBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-microphone"></i>
                                    </button>
                                    <button id="toggleCameraBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-video"></i>
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    @if($liveVideo->is_recording_enabled)
                                    <button id="toggleRecordBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-record-vinyl mr-2"></i> Record
                                    </button>
                                    @endif
                                    <button id="shareScreenBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-desktop mr-2"></i> Share Screen
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Stream Information -->
                    <div class="mt-6 bg-white rounded-lg shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Stream Information</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Teacher:</span>
                                <span class="ml-2">{{ $liveVideo->teacher->name }}</span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Scheduled Time:</span>
                                <span class="ml-2">{{ $liveVideo->scheduled_start_time->format('M j, Y g:i A') }}</span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Duration:</span>
                                <span class="ml-2">{{ $liveVideo->duration ?? 'Not set' }} minutes</span>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Recording:</span>
                                <span class="ml-2">{{ $liveVideo->is_recording_enabled ? 'Enabled' : 'Disabled' }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Sidebar -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div class="p-4 bg-gray-50 border-b">
                            <h3 class="font-semibold text-gray-900">Live Chat</h3>
                        </div>
                        
                        <!-- Chat Messages -->
                        <div id="chatMessages" class="chat-container p-4 space-y-3">
                            <div class="text-center text-gray-500 text-sm">
                                Welcome to the live chat! Messages will appear here.
                            </div>
                        </div>

                        <!-- Chat Input -->
                        <div class="p-4 border-t bg-gray-50">
                            <div class="flex space-x-2">
                                <input type="text" id="chatInput" placeholder="Type a message..." 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                <button id="sendChatBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Participants -->
                    <div class="mt-6 bg-white rounded-lg shadow-sm overflow-hidden">
                        <div class="p-4 bg-gray-50 border-b">
                            <h3 class="font-semibold text-gray-900">Participants</h3>
                        </div>
                        <div id="participantsList" class="p-4">
                            <div class="text-center text-gray-500 text-sm">
                                No participants yet
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Font Awesome -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    
    <script>
        // Live streaming functionality will be implemented here
        class LiveStreamManager {
            constructor(liveVideoId) {
                this.liveVideoId = liveVideoId;
                this.localStream = null;
                this.isStreaming = false;
                this.isRecording = false;
                this.mediaRecorder = null;
                this.recordedChunks = [];
                
                this.initializeElements();
                this.setupEventListeners();
            }

            initializeElements() {
                this.startStreamBtn = document.getElementById('startStreamBtn');
                this.stopStreamBtn = document.getElementById('stopStreamBtn');
                this.toggleMicBtn = document.getElementById('toggleMicBtn');
                this.toggleCameraBtn = document.getElementById('toggleCameraBtn');
                this.toggleRecordBtn = document.getElementById('toggleRecordBtn');
                this.shareScreenBtn = document.getElementById('shareScreenBtn');
                this.localVideo = document.getElementById('localVideo');
                this.videoPlaceholder = document.getElementById('video-placeholder');
                this.viewerCount = document.getElementById('viewer-count');
                this.liveStatus = document.getElementById('live-status');
            }

            setupEventListeners() {
                this.startStreamBtn?.addEventListener('click', () => this.startStream());
                this.stopStreamBtn?.addEventListener('click', () => this.stopStream());
                this.toggleMicBtn?.addEventListener('click', () => this.toggleMicrophone());
                this.toggleCameraBtn?.addEventListener('click', () => this.toggleCamera());
                this.toggleRecordBtn?.addEventListener('click', () => this.toggleRecording());
                this.shareScreenBtn?.addEventListener('click', () => this.shareScreen());
            }

            async startStream() {
                try {
                    this.localStream = await navigator.mediaDevices.getUserMedia({
                        video: true,
                        audio: true
                    });

                    this.localVideo.srcObject = this.localStream;
                    this.localVideo.classList.remove('hidden');
                    this.videoPlaceholder.classList.add('hidden');

                    this.startStreamBtn.classList.add('hidden');
                    this.stopStreamBtn.classList.remove('hidden');
                    this.isStreaming = true;

                    // Update live video status in database
                    await this.updateStreamStatus('live');

                    console.log('Stream started successfully');
                } catch (error) {
                    console.error('Error starting stream:', error);
                    alert('Failed to start stream. Please check your camera and microphone permissions.');
                }
            }

            async stopStream() {
                if (this.localStream) {
                    this.localStream.getTracks().forEach(track => track.stop());
                    this.localStream = null;
                }

                this.localVideo.classList.add('hidden');
                this.videoPlaceholder.classList.remove('hidden');

                this.startStreamBtn.classList.remove('hidden');
                this.stopStreamBtn.classList.add('hidden');
                this.isStreaming = false;

                if (this.isRecording) {
                    this.stopRecording();
                }

                // Update live video status in database
                await this.updateStreamStatus('ended');

                console.log('Stream stopped');
            }

            toggleMicrophone() {
                if (this.localStream) {
                    const audioTrack = this.localStream.getAudioTracks()[0];
                    if (audioTrack) {
                        audioTrack.enabled = !audioTrack.enabled;
                        this.toggleMicBtn.classList.toggle('bg-red-600', !audioTrack.enabled);
                        this.toggleMicBtn.classList.toggle('bg-blue-600', audioTrack.enabled);
                    }
                }
            }

            toggleCamera() {
                if (this.localStream) {
                    const videoTrack = this.localStream.getVideoTracks()[0];
                    if (videoTrack) {
                        videoTrack.enabled = !videoTrack.enabled;
                        this.toggleCameraBtn.classList.toggle('bg-red-600', !videoTrack.enabled);
                        this.toggleCameraBtn.classList.toggle('bg-blue-600', videoTrack.enabled);
                    }
                }
            }

            async shareScreen() {
                try {
                    const screenStream = await navigator.mediaDevices.getDisplayMedia({
                        video: true,
                        audio: true
                    });

                    // Replace video track with screen share
                    if (this.localStream) {
                        const videoTrack = screenStream.getVideoTracks()[0];
                        const sender = this.localStream.getVideoTracks()[0];
                        
                        this.localVideo.srcObject = screenStream;
                        
                        videoTrack.onended = () => {
                            // Switch back to camera when screen share ends
                            this.startStream();
                        };
                    }
                } catch (error) {
                    console.error('Error sharing screen:', error);
                }
            }

            toggleRecording() {
                if (this.isRecording) {
                    this.stopRecording();
                } else {
                    this.startRecording();
                }
            }

            startRecording() {
                if (this.localStream) {
                    this.recordedChunks = [];
                    this.mediaRecorder = new MediaRecorder(this.localStream);
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            this.recordedChunks.push(event.data);
                        }
                    };

                    this.mediaRecorder.onstop = () => {
                        this.saveRecording();
                    };

                    this.mediaRecorder.start();
                    this.isRecording = true;
                    this.toggleRecordBtn.textContent = 'Stop Recording';
                    this.toggleRecordBtn.classList.remove('bg-green-600');
                    this.toggleRecordBtn.classList.add('bg-red-600');
                }
            }

            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.isRecording = false;
                    this.toggleRecordBtn.textContent = 'Record';
                    this.toggleRecordBtn.classList.remove('bg-red-600');
                    this.toggleRecordBtn.classList.add('bg-green-600');
                }
            }

            saveRecording() {
                const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);
                
                // Create download link
                const a = document.createElement('a');
                a.href = url;
                a.download = `live-lesson-${this.liveVideoId}-${Date.now()}.webm`;
                a.click();
                
                // TODO: Upload to server and attach to LiveVideo record
                this.uploadRecording(blob);
            }

            async uploadRecording(blob) {
                const formData = new FormData();
                formData.append('recording', blob, `live-lesson-${this.liveVideoId}.webm`);
                formData.append('live_video_id', this.liveVideoId);

                try {
                    const response = await fetch('/api/live-videos/upload-recording', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    });

                    if (response.ok) {
                        console.log('Recording uploaded successfully');
                    }
                } catch (error) {
                    console.error('Error uploading recording:', error);
                }
            }

            async updateStreamStatus(status) {
                try {
                    await fetch(`/api/live-videos/${this.liveVideoId}/status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({ status })
                    });
                } catch (error) {
                    console.error('Error updating stream status:', error);
                }
            }
        }

        // Initialize the live stream manager
        document.addEventListener('DOMContentLoaded', function() {
            const liveVideoId = {{ $liveVideo->id }};
            const streamManager = new LiveStreamManager(liveVideoId);
        });
    </script>
</body>
</html>
