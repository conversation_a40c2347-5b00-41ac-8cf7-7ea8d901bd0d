<?php

namespace App\Filament\Resources\Blog\PostResource\Pages;

use App\Filament\Resources\Blog\PostResource;
use App\Models\Blog\Post;
use App\Models\Blog\Author;
use App\Models\Blog\Category;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

class EditPost extends EditRecord
{
    protected static string $resource = PostResource::class;

    public function getTitle(): string | Htmlable
    {
        /** @var Post */
        $record = $this->getRecord();

        return $record->title;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),

            Actions\Action::make('bulkCreateAuthors')
                ->label('Quick Create Authors')
                ->icon('heroicon-o-users')
                ->color('info')
                ->form([
                    \Filament\Forms\Components\Textarea::make('author_data')
                        ->label('Author Data')
                        ->placeholder('Enter author data, one per line in format: Name <<EMAIL>>' . "\n" . '<PERSON> <<EMAIL>>' . "\n" . '<PERSON> <<EMAIL>>')
                        ->rows(5)
                        ->required()
                        ->helperText('Enter one author per line in format: Name <<EMAIL>>'),
                ])
                ->action(function (array $data) {
                    $lines = array_filter(array_map('trim', explode("\n", $data['author_data'])));
                    $created = 0;

                    foreach ($lines as $line) {
                        if (!empty($line) && preg_match('/^(.+?)\s*<(.+?)>$/', $line, $matches)) {
                            $name = trim($matches[1]);
                            $email = trim($matches[2]);

                            if (!Author::where('email', $email)->exists()) {
                                Author::create([
                                    'name' => $name,
                                    'email' => $email,
                                    'bio' => null,
                                    'github_handle' => null,
                                    'twitter_handle' => null,
                                    'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                                ]);
                                $created++;
                            }
                        }
                    }

                    Notification::make()
                        ->title("Created {$created} authors successfully!")
                        ->success()
                        ->send();
                }),

            Actions\Action::make('bulkCreateCategories')
                ->label('Quick Create Categories')
                ->icon('heroicon-o-folder')
                ->color('success')
                ->form([
                    \Filament\Forms\Components\Textarea::make('category_names')
                        ->label('Category Names')
                        ->placeholder('Enter category names, one per line:' . "\n" . 'Technology' . "\n" . 'Business' . "\n" . 'Lifestyle')
                        ->rows(5)
                        ->required()
                        ->helperText('Enter one category name per line. Categories will be created automatically.'),

                    \Filament\Forms\Components\Toggle::make('make_visible')
                        ->label('Make all categories visible')
                        ->default(true),
                ])
                ->action(function (array $data) {
                    $names = array_filter(array_map('trim', explode("\n", $data['category_names'])));
                    $created = 0;

                    foreach ($names as $name) {
                        if (!empty($name)) {
                            Category::create([
                                'name' => $name,
                                'slug' => Str::slug($name),
                                'description' => null,
                                'is_visible' => $data['make_visible'] ?? true,
                                'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                            ]);
                            $created++;
                        }
                    }

                    Notification::make()
                        ->title("Created {$created} categories successfully!")
                        ->success()
                        ->send();
                }),
        ];
    }
}
