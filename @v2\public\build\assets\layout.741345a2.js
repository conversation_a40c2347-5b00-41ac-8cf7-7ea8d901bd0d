document.addEventListener("DOMContentLoaded",function(){u(),f(),m(),h(),w(),p(),y();function u(){const e=document.querySelector(".header");if(!e)return;let t=window.scrollY,n=!1;function o(){const i=window.scrollY;i>50?e.classList.add("scrolled"):e.classList.remove("scrolled"),i>t&&i>100?e.style.transform="translateY(-100%)":e.style.transform="translateY(0)",t=i,n=!1}function s(){n||(requestAnimationFrame(o),n=!0)}window.addEventListener("scroll",s,{passive:!0})}function f(){const e=document.getElementById("menu-toggle"),t=document.getElementById("mobile-menu"),n=document.getElementById("mobile-menu-overlay"),o=document.getElementById("close-menu"),s=document.querySelectorAll(".mobile-nav-link");if(!e||!t)return;e.addEventListener("click",function(){i()}),o&&o.addEventListener("click",function(){a()}),n&&n.addEventListener("click",function(){a()}),s.forEach(c=>{c.addEventListener("click",function(){a()})}),document.addEventListener("keydown",function(c){c.key==="Escape"&&t.classList.contains("active")&&a()});function i(){t.classList.contains("active")?a():d()}function d(){t.classList.add("active"),n&&n.classList.add("active"),e.classList.add("active"),document.body.style.overflow="hidden";const c=t.querySelector('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])');c&&c.focus()}function a(){t.classList.remove("active"),n&&n.classList.remove("active"),e.classList.remove("active"),document.body.style.overflow=""}window.addEventListener("resize",function(){window.innerWidth>768&&t.classList.contains("active")&&a()})}function m(){document.querySelector(".scroll-to-top")||v();const t=document.querySelector(".scroll-to-top");if(!t)return;let n=!1;function o(){window.scrollY>300?t.classList.add("visible"):t.classList.remove("visible"),n=!1}function s(){n||(requestAnimationFrame(o),n=!0)}window.addEventListener("scroll",s,{passive:!0}),t.addEventListener("click",function(){window.scrollTo({top:0,behavior:"smooth"})})}function v(){const e=document.createElement("button");e.className="scroll-to-top",e.innerHTML='<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path></svg>',e.setAttribute("aria-label","Scroll to top"),document.body.appendChild(e)}function h(){document.querySelectorAll('a[href^="/"], a[href^="'+window.location.origin+'"]').forEach(t=>{t.hasAttribute("data-layout-processed")||(t.setAttribute("data-layout-processed","true"),t.addEventListener("click",function(n){const o=this.getAttribute("href");if(!o.startsWith("#")&&!(this.hasAttribute("download")||this.hasAttribute("target")||this.getAttribute("rel")==="external")){if(o.includes("#")){const s=o.indexOf("#"),i=o.substring(0,s),d=window.location.origin+window.location.pathname;if(i===d||i===window.location.pathname||i===window.location.origin&&window.location.pathname==="/"||i===window.location.origin+"/"&&window.location.pathname==="/")return}if(o===window.location.pathname||o===window.location.origin+window.location.pathname){n.preventDefault();return}r()}},{once:!1,passive:!1}))})}function w(){const e=window.location.pathname;document.querySelectorAll(".nav-link, .mobile-nav-link").forEach(n=>{const o=n.getAttribute("href");n.classList.remove("active"),(o===e||o!=="/"&&e.startsWith(o))&&n.classList.add("active")})}function p(){document.querySelector(".loading-overlay")||b(),window.addEventListener("load",function(){l()})}function y(){document.querySelectorAll('a[href^="#"]').forEach(t=>{t.hasAttribute("data-anchor-processed")||(t.setAttribute("data-anchor-processed","true"),t.addEventListener("click",function(n){const o=this.getAttribute("href");if(o==="#"||o==="#!")return;n.preventDefault(),n.stopPropagation();const s=o.substring(1);window.LayoutUtils.smoothScrollTo(s)||(window.location.hash=o)},{once:!1,passive:!1}))})}function b(){const e=document.createElement("div");e.className="loading-overlay",e.innerHTML='<div class="loading-spinner"></div>',document.body.appendChild(e)}function r(){const e=document.querySelector(".loading-overlay");e&&e.classList.add("active")}function l(){const e=document.querySelector(".loading-overlay");e&&e.classList.remove("active")}window.LayoutUtils={showLoading:r,hideLoading:l,closeMobileMenu:function(){const e=document.getElementById("mobile-menu"),t=document.getElementById("mobile-menu-overlay"),n=document.getElementById("menu-toggle");e&&e.classList.remove("active"),t&&t.classList.remove("active"),n&&n.classList.remove("active"),document.body.style.overflow=""},smoothScrollTo:function(e){l();const t=document.getElementById(e);return t?(t.scrollIntoView({behavior:"smooth",block:"start"}),this.closeMobileMenu(),!0):!1},showNotification:function(e,t="info",n=5e3){const o=document.createElement("div");o.className=`notification ${t}`,o.style.cssText=`
                position: fixed;
                top: 1rem;
                right: 1rem;
                z-index: 60;
                padding: 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
                animation: slideInRight 0.3s ease-out;
                max-width: 400px;
            `;const s={success:"#10b981",error:"#ef4444",warning:"#f59e0b",info:"#3b82f6"};o.style.backgroundColor=s[t]||s.info,o.style.color="white",o.innerHTML=`
                <div class="flex items-center">
                    <span>${e}</span>
                    <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            `,document.body.appendChild(o),setTimeout(()=>{o.parentElement&&(o.style.transform="translateX(100%)",setTimeout(()=>{o.remove()},300))},n)},animateOnScroll:function(){const e=document.querySelectorAll(".fade-in, .slide-in-left, .slide-in-right"),t=new IntersectionObserver(n=>{n.forEach(o=>{o.isIntersecting&&(o.target.style.animationPlayState="running",t.unobserve(o.target))})},{threshold:.1,rootMargin:"0px 0px -50px 0px"});e.forEach(n=>{n.style.animationPlayState="paused",t.observe(n)})}},"IntersectionObserver"in window&&window.LayoutUtils.animateOnScroll(),document.querySelectorAll("form").forEach(e=>{e.addEventListener("submit",function(t){const n=e.querySelector('button[type="submit"], input[type="submit"]');n&&!n.disabled&&(r(),setTimeout(()=>{l()},1e4))})}),document.addEventListener("keydown",function(e){if(e.altKey&&e.key==="m"){e.preventDefault();const t=document.querySelector("main");t&&(t.focus(),t.scrollIntoView({behavior:"smooth"}))}if((e.ctrlKey||e.metaKey)&&e.key==="k"){e.preventDefault();const t=document.querySelector('input[type="search"], input[placeholder*="\u0E04\u0E49\u0E19\u0E2B\u0E32"]');t&&t.focus()}}),"performance"in window&&window.addEventListener("load",function(){setTimeout(()=>{const e=performance.getEntriesByType("navigation")[0];e&&e.loadEventEnd>3e3&&console.warn("Page load time is slow:",e.loadEventEnd+"ms")},0)})});
