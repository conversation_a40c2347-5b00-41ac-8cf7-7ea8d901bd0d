<?php

namespace App\Observers;

use App\Models\Team;
use App\Models\MediaManager\Folder;
use Illuminate\Support\Facades\Storage;

class TeamObserver
{
    /**
     * Handle the Team "created" event.
     */
    public function created(Team $team): void
    {
        // Create physical directory for the team
        $teamPath = "teams/{$team->id}";
        Storage::disk('public')->makeDirectory($teamPath);
        
        // Create main team folder in database
        $mainFolder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->create([
            'name' => $team->name,
            'collection' => "team_{$team->id}_main",
            'description' => "Main folder for {$team->name}",
            'icon' => 'heroicon-o-building-office',
            'color' => '#3B82F6',
            'is_personal' => false,
            'is_public' => false,
            'team_id' => $team->id,
            'created_by' => auth()->id(),
            'owner_id' => auth()->id(),
            'is_hidden' => false,
            'is_favorite' => false,
            'has_user_access' => false,
        ]);

        // Create subdirectories for different content types
        $subfolders = [
            [
                'name' => 'Products',
                'collection' => "team_{$team->id}_products",
                'description' => 'Product images and files',
                'icon' => 'heroicon-o-shopping-bag',
                'color' => '#10B981',
            ],
            [
                'name' => 'Avatars',
                'collection' => "team_{$team->id}_avatars",
                'description' => 'User profile images',
                'icon' => 'heroicon-o-user-circle',
                'color' => '#8B5CF6',
            ],
            [
                'name' => 'Documents',
                'collection' => "team_{$team->id}_documents",
                'description' => 'General documents and files',
                'icon' => 'heroicon-o-document',
                'color' => '#F59E0B',
            ],
            [
                'name' => 'Media',
                'collection' => "team_{$team->id}_media",
                'description' => 'General media files',
                'icon' => 'heroicon-o-photo',
                'color' => '#EF4444',
            ],
        ];

        foreach ($subfolders as $subfolder) {
            // Create physical subdirectory
            Storage::disk('public')->makeDirectory("{$teamPath}/{$subfolder['name']}");
            
            // Create database folder record
            Folder::withoutGlobalScopes(['team_folder', 'user_folder'])->create([
                'name' => $subfolder['name'],
                'collection' => $subfolder['collection'],
                'description' => $subfolder['description'],
                'icon' => $subfolder['icon'],
                'color' => $subfolder['color'],
                'is_personal' => false,
                'is_public' => false,
                'team_id' => $team->id,
                'parent_id' => $mainFolder->id,
                'created_by' => auth()->id(),
                'owner_id' => auth()->id(),
                'is_hidden' => false,
                'is_favorite' => false,
                'has_user_access' => false,
            ]);
        }
    }

    /**
     * Handle the Team "updated" event.
     */
    public function updated(Team $team): void
    {
        // If team name changed, update the main folder name
        if ($team->isDirty('name')) {
            $mainFolder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                ->where('team_id', $team->id)
                ->where('parent_id', null)
                ->where('is_personal', false)
                ->first();

            if ($mainFolder) {
                $mainFolder->update([
                    'name' => $team->name,
                    'description' => "Main folder for {$team->name}",
                ]);
            }
        }
    }

    /**
     * Handle the Team "deleting" event.
     */
    public function deleting(Team $team): void
    {
        // Mark all team folders as deleted
        $teamFolders = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
            ->where('team_id', $team->id)
            ->get();

        foreach ($teamFolders as $folder) {
            $folder->update([
                'description' => ($folder->description ?: '') . " [Team deleted: {$team->name}]",
                'is_hidden' => true,
            ]);
        }
    }

    /**
     * Handle the Team "deleted" event.
     */
    public function deleted(Team $team): void
    {
        // Rename physical directory to indicate deletion
        $oldPath = "teams/{$team->id}";
        $newPath = "teams/{$team->id}-deleted";
        
        if (Storage::disk('public')->exists($oldPath)) {
            Storage::disk('public')->move($oldPath, $newPath);
        }
    }
}
