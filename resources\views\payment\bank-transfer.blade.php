@extends('layouts.app')

@section('title', 'Bank Transfer Instructions')

@section('content')
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="bg-green-600 px-6 py-4">
                <h1 class="text-2xl font-bold text-white">Bank Transfer Instructions</h1>
                <p class="text-green-100 mt-1">Please complete your payment using the details below</p>
            </div>

            <div class="p-6">
                <!-- Payment Details -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">Payment Amount</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p class="text-2xl font-bold">${{ number_format($payment->amount, 2) }} {{ $payment->currency }}</p>
                                <p class="text-sm">Payment Reference: <span class="font-mono">{{ $bankDetails['reference'] }}</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bank Details -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-gray-900">Bank Account Details</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700">Bank Name</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900">{{ $bankDetails['bank_name'] }}</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700">Account Name</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900">{{ $bankDetails['account_name'] }}</p>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700">Account Number</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900 font-mono">{{ $bankDetails['account_number'] }}</p>
                            <button onclick="copyToClipboard('{{ $bankDetails['account_number'] }}')" class="mt-2 text-sm text-blue-600 hover:text-blue-500">
                                Copy Account Number
                            </button>
                        </div>

                        <div class="bg-gray-50 p-4 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700">SWIFT Code</label>
                            <p class="mt-1 text-lg font-semibold text-gray-900 font-mono">{{ $bankDetails['swift_code'] }}</p>
                        </div>
                    </div>

                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Important Instructions</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Please include the payment reference <strong>{{ $bankDetails['reference'] }}</strong> in your transfer description</li>
                                        <li>Transfer the exact amount: <strong>${{ number_format($payment->amount, 2) }}</strong></li>
                                        <li>Your subscription will be activated within 1-2 business days after payment verification</li>
                                        <li>Keep your transfer receipt for your records</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slip Upload Section -->
                <div class="mt-8">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4">Upload Payment Slip</h2>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">Upload Your Transfer Slip</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>After completing the bank transfer, upload your slip for automatic verification and instant activation.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('payment.slip.upload', $payment->uuid) }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                        @csrf
                        <div>
                            <label for="slip_image" class="block text-sm font-medium text-gray-700">Payment Slip Image</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-gray-400 transition-colors">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="slip_image" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                            <span>Upload a file</span>
                                            <input id="slip_image" name="slip_image" type="file" accept="image/*" class="sr-only" required>
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG up to 5MB</p>
                                </div>
                            </div>
                            @error('slip_image')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                Upload & Verify Slip
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Action Buttons -->
                <div class="mt-8 flex flex-col sm:flex-row gap-4">
                    <button onclick="window.print()" class="flex-1 bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        Print Instructions
                    </button>
                    <a href="{{ route('dashboard') }}" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-center">
                        Go to Dashboard
                    </a>
                </div>

                <!-- Contact Information -->
                <div class="mt-8 text-center text-sm text-gray-600">
                    <p>Need help? Contact our support team at <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-500"><EMAIL></a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('text-green-600');
        
        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('text-green-600');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
@endsection
