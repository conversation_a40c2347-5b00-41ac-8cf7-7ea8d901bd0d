# Filament Media Manager Setup Guide

## Installation Complete ✅

The `tomatophp/filament-media-manager` package has been successfully installed and configured in your Laravel/Filament application.

## What was installed:

1. **Package Installation**: `tomatophp/filament-media-manager` v1.1.5
2. **Dependencies**: 
   - `calebporzio/sushi` v2.4.5
   - `tomatophp/console-helpers` v1.1.0
   - `tomatophp/filament-icons` v1.1.5

## Configuration Changes Made:

### 1. AdminPanelProvider Updated
- Added `FilamentMediaManagerPlugin` to the plugins array
- Added "Content" navigation group for the media manager

### 2. Database Migrations
The following migrations were already present and executed:
- `2024_10_03_171807_create_folders_table`
- `2024_10_03_171808_create_media_has_models_table`
- `2024_10_03_171809_create_folder_has_models_table`
- `2024_10_03_171810_update_folders_table`

### 3. Configuration Files
- Published `config/filament-media-manager.php`
- Published language files to `lang/vendor/filament-media-manager/`

## How to Use the Media Manager

### 1. In Filament Forms

You can use the `MediaManagerInput` component in your Filament forms:

```php
use TomatoPHP\FilamentMediaManager\Form\MediaManagerInput;

public function form(Form $form)
{
    return $form->schema([
        MediaManagerInput::make('images')
            ->disk('public')
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('description')
                    ->required()
                    ->maxLength(255),
            ]),
    ]);
}
```

### 2. Access the Media Manager GUI

The media manager is now available in your admin panel under the "Content" navigation group as "Media Manager".

### 3. Features Available

- ✅ Create folders and subfolders
- ✅ Set password protection for folders
- ✅ Upload files with custom fields
- ✅ Auto-create folders for Model/Collection/Record
- ✅ RTL/Multi-language support
- ✅ GUI for managing media files

### 4. Optional Configuration

#### Enable API Access (Optional)
To enable API access, edit `config/filament-media-manager.php`:

```php
'api' => [
    'active' => true,
    // ... other settings
],
```

#### Enable Sub Folders (Optional)
To allow sub-folders, update your AdminPanelProvider:

```php
->plugins([
    // ... other plugins
    FilamentMediaManagerPlugin::make()
        ->allowSubFolders()
])
```

#### Enable User Access Control (Optional)
To enable user-specific folder access:

```php
->plugins([
    // ... other plugins
    FilamentMediaManagerPlugin::make()
        ->allowUserAccess()
])
```

Then add the trait to your User model:

```php
use TomatoPHP\FilamentMediaManager\Traits\InteractsWithMediaFolders;

class User extends Authenticatable
{
    use InteractsWithMediaFolders;
}
```

## Next Steps

1. **Test the Installation**: Visit your admin panel at `/backend` and look for "Media Manager" under the "Content" navigation group.

2. **Create Your First Folder**: Use the media manager to create folders and upload files.

3. **Integrate with Your Models**: Use the `MediaManagerInput` component in your existing Filament resources.

4. **Customize as Needed**: Modify the configuration files and language files as per your requirements.

## Troubleshooting

If you encounter any issues:

1. Clear caches: `php artisan config:clear && php artisan route:clear`
2. Check that all migrations have run: `php artisan migrate:status`
3. Ensure the storage link exists: `php artisan storage:link`

## Documentation

For more advanced usage and customization options, refer to:
- [Official Documentation](https://docs.tomatophp.com/filament/filament-media-manager)
- [GitHub Repository](https://github.com/tomatophp/filament-media-manager)
