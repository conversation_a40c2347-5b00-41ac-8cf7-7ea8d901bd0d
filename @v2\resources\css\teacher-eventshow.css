/* Teacher Event Show Specific Styles */

/* Header Card */
.header-card {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
}

.header-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Info Labels and Values */
.info-label {
    color: #666;
    font-weight: 500;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.info-value {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

/* Copy Link Button */
.copy-link-btn {
    background: linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%);
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.copy-link-btn:hover {
    background: linear-gradient(135deg, #6d28d9 0%, #7c3aed 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(124, 58, 237, 0.3);
}

.copy-link-btn:active {
    transform: translateY(0);
}

/* Table Container */
.table-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    background-color: white;
}

/* Table Styles */
.student-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.student-table th {
    background-color: #f3f4f6;
    font-weight: 600;
    text-align: left;
    color: #4b5563;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    border-bottom: 1px solid #e5e7eb;
}

.student-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    font-size: 0.875rem;
}

.student-table tbody tr {
    transition: background-color 0.2s ease;
}

.student-table tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

.student-table tbody tr:nth-child(odd) {
    background-color: white;
}

.student-table tbody tr:hover {
    background-color: #f0f9ff;
    transform: scale(1.01);
}

/* Button Styles */
.btn-test {
    background-color: #8b5cf6;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.btn-test:hover {
    background-color: #7c3aed;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.btn-exercise {
    background-color: #10b981;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.btn-exercise:hover {
    background-color: #059669;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-disabled {
    background-color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.7;
}

.btn-disabled:hover {
    background-color: #9ca3af;
    transform: none;
    box-shadow: none;
}

/* Progress Container */
.progress-container {
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #8b5cf6 0%, #a855f7 100%);
    border-radius: 4px;
    transition: width 1s ease-in-out;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Score Display */
.score-display {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.score-excellent {
    background-color: #dcfce7;
    color: #166534;
}

.score-good {
    background-color: #dbeafe;
    color: #1e40af;
}

.score-average {
    background-color: #fef3c7;
    color: #92400e;
}

.score-poor {
    background-color: #fee2e2;
    color: #dc2626;
}

.score-missing {
    background-color: #f3f4f6;
    color: #6b7280;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
    opacity: 0;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.2s ease;
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-scale {
    transition: transform 0.2s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Loading States */
.loading {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: slideInRight 0.3s ease-out;
}

.notification.success {
    background-color: #10b981;
    color: white;
}

.notification.error {
    background-color: #ef4444;
    color: white;
}

.notification.info {
    background-color: #3b82f6;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-card {
        padding: 1rem;
    }
    
    .student-table th,
    .student-table td {
        padding: 0.5rem;
        font-size: 0.75rem;
    }
    
    .btn-test,
    .btn-exercise,
    .btn-disabled {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .progress-container {
        height: 6px;
    }
    
    .info-label {
        font-size: 0.75rem;
    }
    
    .info-value {
        font-size: 0.875rem;
    }
}

@media (max-width: 640px) {
    .student-table {
        font-size: 0.75rem;
    }
    
    .student-table th,
    .student-table td {
        padding: 0.375rem;
    }
    
    .copy-link-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* Focus States */
.focus-ring:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Table Row Animations */
.table-row-enter {
    animation: tableRowEnter 0.3s ease-out;
}

@keyframes tableRowEnter {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Button Group */
.button-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
}

@media (max-width: 640px) {
    .button-group {
        flex-direction: column;
        align-items: center;
    }
    
    .button-group button {
        width: 100%;
        max-width: 120px;
    }
}
