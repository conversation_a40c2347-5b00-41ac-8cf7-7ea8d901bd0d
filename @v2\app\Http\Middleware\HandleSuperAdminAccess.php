<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class HandleSuperAdminAccess
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // If user is a super admin (team_id = null), handle tenant access
        if ($user && $user->team_id === null && $user->hasRole('super_admin')) {
            // Check if super admin is trying to access the admin panel without a tenant
            if ($request->is('admin') || $request->is('admin/*')) {
                // Get the first available team for super admin to use
                $firstTeam = \App\Models\Team::where('is_active', true)->first();

                if (!$firstTeam) {
                    // Create a default team if none exists
                    $firstTeam = \App\Models\Team::create([
                        'name' => 'Default Team',
                        'slug' => 'default-team',
                        'description' => 'Default team for super admin access',
                        'is_active' => true,
                    ]);
                }

                // If accessing the root admin URL, redirect to the first team
                if ($request->is('admin') && !$request->has('tenant')) {
                    return redirect("/admin/{$firstTeam->slug}");
                }

                // If accessing admin/* without tenant in URL, redirect with tenant
                if ($request->is('admin/*') && !str_contains($request->path(), $firstTeam->slug)) {
                    $path = str_replace('admin/', "admin/{$firstTeam->slug}/", $request->path());
                    return redirect($path . ($request->getQueryString() ? '?' . $request->getQueryString() : ''));
                }
            }
        }

        return $next($request);
    }
}
