<?php

namespace App\Providers\Filament;

use App\Filament\App\Pages\RegisterTeam;
use App\Filament\Pages\Auth\Login;
use App\Models\Team;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Facades\FilamentView;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AppPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('app')
            ->path('app')
            ->darkMode(false) // Force light theme
            ->viteTheme('resources/css/filament/app/theme.css')
            ->brandName(fn() => auth()->user()?->team?->name ?? 'School Portal')
            ->brandLogo(fn() => view('filament.app.logo'))
            ->brandLogoHeight('1.25rem')
            ->login(Login::class)
            ->registration()
            ->passwordReset()
            ->emailVerification()
            // ->tenant(Team::class, ownershipRelationship: 'team', slugAttribute: 'slug')
            // ->tenantRegistration(RegisterTeam::class)
            ->discoverResources(in: app_path('Filament/App/Resources'), for: 'App\\Filament\\App\\Resources')
            ->discoverPages(in: app_path('Filament/App/Pages'), for: 'App\\Filament\\App\\Pages')
            ->discoverWidgets(in: app_path('Filament/App/Widgets'), for: 'App\\Filament\\App\\Widgets')
            ->navigationGroups($this->getNavigationGroupsForUser())
            ->unsavedChangesAlerts()
            ->databaseNotifications()
            ->sidebarCollapsibleOnDesktop()
            ->collapsedSidebarWidth('4rem')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->tenant(Team::class, ownershipRelationship: 'team', slugAttribute: 'slug')
            ->tenantMiddleware([
                \BezhanSalleh\FilamentShield\Middleware\SyncShieldTenant::class,
            ], isPersistent: true)
            ->plugins([
                FilamentShieldPlugin::make()
            ])
            ->renderHook(
                PanelsRenderHook::TOPBAR_END,
                fn () => view('components.language-switcher', ['class' => 'mr-4'])
            )
            ->renderHook(
                PanelsRenderHook::STYLES_AFTER,
                fn () => '<link rel="stylesheet" href="' . asset('css/question-builder.css') . '">'
            );
            // ->viteTheme('resources/css/sidebar.css')
            // ->renderHook(
            //     'panels::body.end',
            //     fn () => view('filament.hooks.sidebar-script')
            // );
    }

    /**
     * Get navigation groups based on user role
     */
    private function getNavigationGroupsForUser(): array
    {
        $user = auth()->user();

        if (!$user) {
            return ['Dashboard'];
        }

        // School Admin / Team Admin
        if ($user->hasRole('team_admin') || $user->hasRole('school')) {
            return [
                'Dashboard',
                'School Management',
                'Academic Management',
                'User Management',
                'Communication',
                'Reports & Analytics',
                'Settings'
            ];
        }

        // Teacher
        if ($user->hasRole('teacher')) {
            return [
                'Dashboard',
                'My Classes',
                'Students & Grades',
                'Assignments',
                'Communication',
                'Reports'
            ];
        }

        // Parent
        if ($user->hasRole('parent')) {
            return [
                'Dashboard',
                'My Children',
                'Academic Progress',
                'Communication',
                'Payments',
                'Events & Calendar'
            ];
        }

        // Student
        if ($user->hasRole('student')) {
            return [
                'Dashboard',
                'My Courses',
                'Assignments',
                'Grades',
                'Schedule',
                'Resources'
            ];
        }

        // Default
        return ['Dashboard'];
    }
}
