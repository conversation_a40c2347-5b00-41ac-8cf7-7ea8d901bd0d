# Subscription System Setup Guide

## Overview
This guide will help you activate the complete subscription system with payment processing, including:
- Individual Student Plans (14-day free trial)
- Individual Teacher Plans (14-day free trial)
- School Plans (30-day free trial)
- Payment methods: Credit Card, PayPal, Bank Transfer, PromptPay QR Code

## 1. Environment Configuration

Add these variables to your `.env` file:

```env
# PromptPay Configuration
PROMPTPAY_ID=your_promptpay_id_here

# SlipOK API Configuration (for bank transfer slip verification)
SLIPOK_API_KEY=your_slipok_api_key_here

# PayPal Configuration (for credit card payments)
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox # or live for production
```

## 2. Database Setup

Run the subscription plans seeder:

```bash
php artisan db:seed --class=SubscriptionPlansSeeder
```

This will create the following plans:
- **Individual Student** ($29.99/month, 14-day trial)
- **Individual Teacher** ($49.99/month, 14-day trial)
- **School Basic** ($199.99/month, 30-day trial, 100 students)
- **School Premium** ($399.99/month, 30-day trial, 500 students)
- **Additional Students Pack** ($99.99/month, 50 more students)
- **Annual versions** with 2-month discounts

## 3. Required Packages

The following packages are already installed:
- `simplesoftwareio/simple-qrcode` - For PromptPay QR code generation
- `guzzlehttp/guzzle` - For SlipOK API integration

If you need to install them manually:

```bash
composer require simplesoftwareio/simple-qrcode guzzlehttp/guzzle
```

## 4. Features Implemented

### Frontend
- **Pricing Page**: `/pricing` - Beautiful responsive pricing page
- **Subscription Checkout**: Complete checkout flow with payment method selection
- **Payment Instructions**: 
  - Bank transfer instructions with copy-to-clipboard
  - PromptPay QR code generation and display

### Backend (Admin Panel)
- **Subscription Status Widget**: Shows usage limits and subscription status
- **Subscription Management Page**: Cancel/resume subscriptions, view usage
- **Plan Management**: Super admin can manage global subscription plans
- **Subscription Enforcement**: Middleware enforces user limits based on plans

### Payment Methods
1. **PayPal**: Credit card and PayPal account payments
2. **Bank Transfer**: Automatic slip verification using SlipOK API
3. **PromptPay QR Code**: Automatic QR code generation for Thai users

## 5. User Flow

### For Individual Users:
1. Visit `/pricing`
2. Select Individual Student or Teacher plan
3. Register/login if needed
4. Choose payment method
5. Complete payment
6. Get 14-day free trial
7. Access admin panel with subscription limits

### For Schools:
1. Visit `/pricing`
2. Select School Basic or Premium plan
3. Register/login as school admin
4. Choose payment method
5. Complete payment
6. Get 30-day free trial
7. Manage up to 100/500 students + parents

## 6. Subscription Limits

The system automatically enforces:
- **Individual Student**: 1 student + 1 parent account
- **Individual Teacher**: Up to 50 students
- **School Basic**: Up to 100 students + 100 parents
- **School Premium**: Up to 500 students + 500 parents
- **Additional Packs**: 50 more students + parents per pack

## 7. Admin Features

### Super Admin:
- Manage global subscription plans
- View all subscriptions across teams
- No subscription limits

### Team Admin:
- View subscription status widget
- Manage team subscription
- See usage limits and upgrade options

## 8. Testing

### Test the System:
1. Create a super admin: `php artisan make:super-admin`
2. Visit `/pricing` to see the pricing page
3. Register a new user and select a plan
4. Test different payment methods
5. Check admin panel for subscription status
6. Try adding users to test limits

### Test Payment Methods:
- **Bank Transfer**: Will show instructions page
- **PromptPay**: Will generate QR code (requires PROMPTPAY_ID in .env)
- **Stripe/PayPal**: Requires API keys configuration

## 9. Customization

### Modify Plans:
Edit `database/seeders/SubscriptionPlansSeeder.php` and re-run the seeder.

### Customize Payment Methods:
- Edit `app/Services/PaymentDrivers/` for custom payment logic
- Modify `config/filament-payments.php` to add/remove drivers

### Styling:
- Frontend: Edit `resources/views/pricing/` and payment views
- Backend: Customize widget views in `resources/views/filament/widgets/`

## 10. Production Checklist

- [ ] Configure real payment gateway credentials
- [ ] Set up webhook endpoints for payment verification
- [ ] Test all payment methods thoroughly
- [ ] Configure email notifications for subscription events
- [ ] Set up monitoring for failed payments
- [ ] Configure backup payment methods
- [ ] Test subscription renewal process
- [ ] Set up customer support for payment issues

## 11. Security Notes

- All payment processing uses secure drivers
- Sensitive data is encrypted in database
- CSRF protection on all forms
- User authentication required for subscriptions
- Tenant isolation enforced

## 12. Key URLs

- **Pricing Page**: `/pricing` - Beautiful responsive pricing page
- **Sitemap Webpage**: `/sitemap` - Clickable sitemap to check all frontend pages
- **Sitemap XML**: `/sitemap.xml` - XML sitemap for search engines
- **Subscription Management**: Available in admin panel
- **Payment Instructions**: Auto-generated for bank transfer and PromptPay

## 13. New Features Added

### SlipOK Integration
- Automatic bank transfer slip verification
- Upload slip image for instant verification
- Fallback to manual review if verification fails

### Enhanced Payment Methods
- **PayPal**: Handles both credit cards and PayPal accounts
- **Bank Transfer**: Upload slip with SlipOK API verification
- **PromptPay**: QR code generation for Thai users

### Sitemap Webpage
- Interactive sitemap at `/sitemap`
- Browse all available content by category
- Click to visit any page directly
- Shows last modified dates and descriptions

The subscription system is now fully activated and ready for use!
