<?php

namespace App\Filament\Resources\Exam;

use App\Filament\Resources\Exam\ChoiceResource\Pages;
use App\Models\Exam\Choice;
use App\Models\Exam\Question;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChoiceResource extends Resource
{
    protected static ?string $model = Choice::class;

    protected static ?string $navigationIcon = 'heroicon-o-list-bullet';

    protected static ?string $navigationGroup = 'Academic';

    protected static ?int $navigationSort = 9;

    protected static ?string $navigationLabel = 'Answer Choices';

    protected static ?string $modelLabel = 'Choice';

    protected static ?string $pluralModelLabel = 'Choices';

    protected static bool $shouldRegisterNavigation = false;

    public static function isScopedToTenant(): bool
    {
        return true;
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery();

        // Apply team filtering through question relationship
        if (Filament::hasTenancy() && Filament::getTenant()) {
            $query->whereHas('question', function (Builder $query) {
                $query->where('team_id', Filament::getTenant()->id);
            });
        }

        return $query;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Choice Details')
                    ->schema([
                        Forms\Components\Select::make('question_id')
                            ->label('Question')
                            ->relationship('question', 'question_text', function (Builder $query) {
                                if (Filament::hasTenancy() && Filament::getTenant()) {
                                    $query->where('team_id', Filament::getTenant()->id);
                                }
                                return $query->where('is_active', true);
                            })
                            ->searchable()
                            ->preload()
                            ->required()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('choice_text')
                            ->label('Choice Text')
                            ->required()
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Toggle::make('is_correct')
                            ->label('Correct Answer')
                            ->default(false)
                            ->helperText('Mark this choice as the correct answer')
                            ->columnSpan(1),

                        Forms\Components\TextInput::make('sort_order')
                            ->label('Sort Order')
                            ->numeric()
                            ->default(0)
                            ->helperText('Order in which this choice appears')
                            ->columnSpan(1),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('question.question_text')
                    ->label('Question')
                    ->limit(50)
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('choice_text')
                    ->label('Choice Text')
                    ->limit(80)
                    ->searchable()
                    ->sortable()
                    ->wrap(),

                Tables\Columns\IconColumn::make('is_correct')
                    ->label('Correct')
                    ->boolean()
                    ->alignCenter()
                    ->color(fn (bool $state): string => $state ? 'success' : 'gray'),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Order')
                    ->alignCenter()
                    ->sortable(),

                Tables\Columns\TextColumn::make('question.exam.title')
                    ->label('Exam')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('question.subject.name')
                    ->label('Subject')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('question')
                    ->relationship('question', 'question_text')
                    ->searchable()
                    ->preload(),

                Tables\Filters\TernaryFilter::make('is_correct')
                    ->label('Correct Answer'),

                Tables\Filters\Filter::make('exam')
                    ->form([
                        Forms\Components\Select::make('exam_id')
                            ->label('Exam')
                            ->relationship('question.exam', 'title')
                            ->searchable()
                            ->preload(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['exam_id'],
                                fn (Builder $query, $examId): Builder => $query->whereHas('question', function (Builder $query) use ($examId) {
                                    $query->where('exam_id', $examId);
                                }),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChoices::route('/'),
            'create' => Pages\CreateChoice::route('/create'),
            'view' => Pages\ViewChoice::route('/{record}'),
            'edit' => Pages\EditChoice::route('/{record}/edit'),
        ];
    }
}
