<?php

namespace App\Models\Exam;

use App\Models\Team;
use App\Models\User;
use App\Models\Subject;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class Question extends Model
{
    use HasFactory;

    protected $table = 'exam_questions';

    protected $fillable = [
        'team_id',
        'exam_id',
        'question_text',
        'question_type',
        'user_id',
        'subject_id',
        'points',
        'explanation',
        'difficulty_level',
        'tags',
        'sort_order',
        'is_active',
    ];

    protected $casts = [
        'points' => 'float',
        'tags' => 'array',
        'sort_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // Apply global scope for multi-tenancy
        static::addGlobalScope('team', function (Builder $builder) {
            if (auth()->check() && auth()->user()->team_id) {
                $builder->where('team_id', auth()->user()->team_id);
            }
        });
    }

    // Relationships
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function teacher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function subject(): BelongsTo
    {
        return $this->belongsTo(Subject::class);
    }

    public function exam(): BelongsTo
    {
        return $this->belongsTo(Exam::class);
    }

    public function choices(): HasMany
    {
        return $this->hasMany(Choice::class)->orderBy('sort_order');
    }

    public function correctChoices(): HasMany
    {
        return $this->hasMany(Choice::class)->where('is_correct', true);
    }

    public function answers(): HasMany
    {
        return $this->hasMany(Answer::class, 'question_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('question_type', $type);
    }

    public function scopeByDifficulty($query, string $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    // Helper methods
    public function isMultipleChoice(): bool
    {
        return $this->question_type === 'multiple_choice';
    }

    public function isShortAnswer(): bool
    {
        return $this->question_type === 'short_answer';
    }

    public function isEssay(): bool
    {
        return $this->question_type === 'essay';
    }

    public function getCorrectAnswer(): ?string
    {
        if ($this->isMultipleChoice()) {
            $correctChoice = $this->correctChoices()->first();
            return $correctChoice ? $correctChoice->choice_text : null;
        }

        return null; // For short_answer and essay, there's no single correct answer
    }
}
