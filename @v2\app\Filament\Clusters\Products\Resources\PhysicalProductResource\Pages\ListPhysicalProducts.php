<?php

namespace App\Filament\Clusters\Products\Resources\PhysicalProductResource\Pages;

use App\Filament\Clusters\Products\Resources\PhysicalProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPhysicalProducts extends ListRecords
{
    protected static string $resource = PhysicalProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            PhysicalProductResource\Widgets\PhysicalProductStats::class,
            PhysicalProductResource\Widgets\BrandCategoryStats::class,
        ];
    }
}
