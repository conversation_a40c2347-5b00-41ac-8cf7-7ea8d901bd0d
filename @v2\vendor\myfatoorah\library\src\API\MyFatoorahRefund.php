<?php namespace MyFatoorah\Library\API; use MyFatoorah\Library\MyFatoorah; class  MyFatoorahRefund extends MyFatoorah{public function refund($keyId,$amount,$currency=null,$comment=null,$orderId=null,$keyType='PaymentId'){$postFields=['Key'=>$keyId,'KeyType'=>$keyType,'RefundChargeOnCustomer'=>false,'ServiceChargeOnCustomer'=>false,'Amount'=>$amount,'CurrencyIso'=>$currency,'Comment'=>$comment,];return $this->makeRefund($postFields,$orderId);}public function makeRefund($curlData,$logId=null){$url="$this->apiURL/v2/MakeRefund";$json=$this->callAPI($url,$curlData,$logId,'Make Refund');return $json->Data;}}