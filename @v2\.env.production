APP_NAME="EduNest"
APP_NAME1="Edu"
APP_NAME2="Nest"
APP_ENV=production
APP_KEY=base64:7KidC7Wf4SPuVPN0fjJ2gj4rNWuzW6Zy7lfk6O5hBLQ=
APP_DEBUG=true
APP_URL=https://edutest.space
APP_PORT=8000
DEBUGBAR_ENABLED=false

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

#DB_CONNECTION=sqlite
DB_CONNECTION=mysql
DB_DATABASE=uat_edu
DB_USERNAME=uat_edu
DB_PASSWORD="9ol.0p;/"

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

FLARE_KEY=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


# ================================================
# Social Authentication Configuration
# Copy these variables to your .env file and replace with your actual credentials

# Social Authentication Master Switch
SOCIAL_AUTH_ENABLED=true

# Individual Provider Enable/Disable Switches
GOOGLE_LOGIN_ENABLED=true
MICROSOFT_LOGIN_ENABLED=false
APPLE_LOGIN_ENABLED=false
LINE_LOGIN_ENABLED=true
PHONE_LOGIN_ENABLED=false

# Google OAuth Configuration
# OAuth is limited to 100 sensitive scope logins  until the OAuth consent screen is verified. This may require a verification process that can take several days.
GOOGLE_CLIENT_ID=832621029067-7fss81nake4otfl6dag9tdmr84mm9cqs.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI="${APP_URL}/auth/google/callback"

# Microsoft OAuth Configuration
MICROSOFT_CLIENT_ID=your_microsoft_client_id_here
MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret_here
MICROSOFT_REDIRECT_URI="${APP_URL}/auth/microsoft/callback"

# Apple Sign In Configuration
APPLE_CLIENT_ID=your.apple.client.id.here
APPLE_TEAM_ID=YOUR_TEAM_ID
APPLE_KEY_ID=YOUR_KEY_ID
APPLE_PRIVATE_KEY_PATH=storage/app/private/apple_private_key.p8
APPLE_REDIRECT_URI="${APP_URL}/auth/apple/callback"

# LINE Login Configuration
LINE_CLIENT_ID=**********
LINE_CLIENT_SECRET=e3ce689fbcb3aba1daa24d3f8f5ce9bc
LINE_REDIRECT_URI="${APP_URL}/auth/line/callback"

# Phone Authentication (SMS) Configuration
SMS_PROVIDER=twilio

# Twilio Configuration (Recommended)
# https://www.twilio.com/en-us/pricing
TWILIO_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=+**********

# Alternative: AWS SNS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_DEFAULT_REGION=ap-southeast-1
AWS_SNS_REGION=ap-southeast-1

# Alternative: Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id_here
FIREBASE_PRIVATE_KEY_ID=your_firebase_private_key_id_here
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_firebase_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your_firebase_client_id_here
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Session Configuration for Social Auth
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_DRIVER=file

# Security Settings
BCRYPT_ROUNDS=12
HASH_VERIFY=true

# Rate Limiting for Authentication
THROTTLE_LOGIN_ATTEMPTS=5
THROTTLE_LOGIN_DECAY_MINUTES=1
THROTTLE_OTP_ATTEMPTS=3
THROTTLE_OTP_DECAY_MINUTES=5
