<?php

namespace App\Listeners;

use App\Models\MediaManager\Folder;
use App\Models\Shop\Product;
use Spatie\MediaLibrary\MediaCollections\Events\MediaHasBeenAdded;

class MediaLibraryEventListener
{
    /**
     * Handle the MediaHasBeenAdded event.
     */
    public function handle(MediaHasBeenAdded $event): void
    {
        $media = $event->media;
        $model = $media->model;
        $user = auth()->user();

        // Skip if media already has team_id set
        if ($media->team_id) {
            return;
        }

        // Set team_id based on the model that owns the media
        $teamId = null;
        $parentFolderId = null;
        $parentFolderName = null;

        // Handle Product media uploads
        if ($model instanceof Product) {
            $teamId = $model->team_id;
            
            // Find the appropriate folder based on collection
            $folderName = $this->getFolderNameForCollection($media->collection_name);
            $parentFolder = Folder::withoutGlobalScopes(['team_folder', 'user_folder'])
                ->where('team_id', $teamId)
                ->where('name', $folderName)
                ->first();
            
            if ($parentFolder) {
                $parentFolderId = $parentFolder->id;
                $parentFolderName = $parentFolder->name;
            }
        }
        // Handle other models that have team_id
        elseif (method_exists($model, 'team') && $model->team_id) {
            $teamId = $model->team_id;
        }
        // Fallback to user's team
        elseif ($user && $user->team_id) {
            $teamId = $user->team_id;
        }

        // Update media with team information
        if ($teamId) {
            $customProperties = $media->custom_properties ?? [];
            $customProperties['team_id'] = $teamId;
            
            if ($user) {
                $customProperties['created_by_user'] = $user->name;
            }
            
            if ($parentFolderId) {
                $customProperties['parent_folder_id'] = $parentFolderId;
                $customProperties['parent_folder_name'] = $parentFolderName;
            }
            
            // Add team name
            $team = \App\Models\Team::find($teamId);
            if ($team) {
                $customProperties['team_name'] = $team->name;
            }

            // Add model information
            if ($model) {
                $customProperties['model_type'] = get_class($model);
                $customProperties['model_id'] = $model->id;
                
                if ($model instanceof Product) {
                    $customProperties['product_name'] = $model->name;
                    $customProperties['product_sku'] = $model->sku ?? '';
                }
            }

            $media->update([
                'team_id' => $teamId,
                'created_by' => $user?->id,
                'user_id' => $user?->id,
                'custom_properties' => $customProperties,
            ]);
        }
    }

    /**
     * Map collection names to folder names.
     */
    protected function getFolderNameForCollection(string $collectionName): string
    {
        $collectionMap = [
            'product-images' => 'Products',
            'digital-files' => 'Products',
            'avatars' => 'Avatars',
            'profile-images' => 'Avatars',
            'user_avatars' => 'Avatars',
            'documents' => 'Documents',
            'files' => 'Documents',
            'media' => 'Media',
            'images' => 'Media',
        ];

        return $collectionMap[$collectionName] ?? 'Media';
    }
}
