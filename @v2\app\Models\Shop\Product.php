<?php

namespace App\Models\Shop;

use App\Models\Comment;
use App\Traits\BelongsToTeam;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

/**
 * 
 *
 * @property int $id
 * @property int|null $team_id
 * @property int|null $shop_brand_id
 * @property string $name
 * @property string|null $slug
 * @property string|null $sku
 * @property string|null $barcode
 * @property string|null $description
 * @property int $qty
 * @property int $security_stock
 * @property bool $featured
 * @property bool $is_visible
 * @property string|null $old_price
 * @property string|null $price
 * @property string|null $cost
 * @property array<array-key, mixed>|null $wholesale_pricing
 * @property array<array-key, mixed>|null $product_variants
 * @property string|null $option1_label
 * @property string|null $option2_label
 * @property array<array-key, mixed>|null $option1_variants
 * @property array<array-key, mixed>|null $option2_variants
 * @property string $product_type
 * @property array<array-key, mixed>|null $digital_file_types
 * @property int|null $download_limit
 * @property int|null $download_expiry_days
 * @property bool $requires_license_key
 * @property string|null $license_terms
 * @property string|null $file_size_mb
 * @property string|null $digital_format
 * @property string|null $version
 * @property \Illuminate\Support\Carbon|null $last_updated
 * @property bool $backorder
 * @property bool $requires_shipping
 * @property \Illuminate\Support\Carbon|null $published_at
 * @property string|null $seo_title
 * @property string|null $seo_description
 * @property string|null $weight_value
 * @property string $weight_unit
 * @property string|null $height_value
 * @property string $height_unit
 * @property string|null $width_value
 * @property string $width_unit
 * @property string|null $depth_value
 * @property string $depth_unit
 * @property string|null $volume_value
 * @property string $volume_unit
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property array<array-key, mixed>|null $specifications
 * @property-read \App\Models\Shop\Brand|null $brand
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Shop\Category> $categories
 * @property-read int|null $categories_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Comment> $comments
 * @property-read int|null $comments_count
 * @property-read \Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection<int, \App\Models\MediaManager\Media> $media
 * @property-read int|null $media_count
 * @property-read \App\Models\Team|null $team
 * @method static \Database\Factories\Shop\ProductFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product forCurrentTeam()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product forTeam(\App\Models\Team $team)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereBackorder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereBarcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDepthUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDepthValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDigitalFileTypes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDigitalFormat($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDownloadExpiryDays($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDownloadLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereFeatured($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereFileSizeMb($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereHeightUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereHeightValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereIsVisible($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereLastUpdated($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereLicenseTerms($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOldPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOption1Label($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOption1Variants($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOption2Label($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereOption2Variants($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereProductType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereProductVariants($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePublishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereRequiresLicenseKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereRequiresShipping($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSecurityStock($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSeoDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSeoTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereShopBrandId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSpecifications($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereVolumeUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereVolumeValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereWeightUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereWeightValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereWholesalePricing($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereWidthUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereWidthValue($value)
 * @mixin \Eloquent
 */
class Product extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use BelongsToTeam;

    /**
     * @var string
     */
    protected $table = 'shop_products';

    /**
     * @var array<string, string>
     */
    protected $casts = [
        'featured' => 'boolean',
        'is_visible' => 'boolean',
        'backorder' => 'boolean',
        'requires_shipping' => 'boolean',
        'requires_license_key' => 'boolean',
        'published_at' => 'date',
        'last_updated' => 'date',
        'digital_file_types' => 'array',
        'specifications' => 'array',
        'wholesale_pricing' => 'array',
        'product_variants' => 'array',
        'option1_variants' => 'array',
        'option2_variants' => 'array',
    ];

    /**
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'sku',
        'barcode',
        'description',
        'qty',
        'security_stock',
        'featured',
        'is_visible',
        'old_price',
        'price',
        'cost',
        'wholesale_pricing',
        'product_variants',
        'variant_option1_label',
        'variant_option2_label',
        'option1_variants',
        'option2_variants',
        'product_type',
        'digital_file_types',
        'download_limit',
        'download_expiry_days',
        'requires_license_key',
        'license_terms',
        'file_size_mb',
        'digital_format',
        'specifications',
        'version',
        'last_updated',
        'backorder',
        'requires_shipping',
        'published_at',
        'seo_title',
        'seo_description',
        'weight_value',
        'weight_unit',
        'height_value',
        'height_unit',
        'width_value',
        'width_unit',
        'depth_value',
        'depth_unit',
        'volume_value',
        'volume_unit',
        'shop_brand_id',
        'team_id',
    ];

    /**
     * Product type constants
     */
    const TYPE_PHYSICAL = 'physical';
    const TYPE_DIGITAL = 'digital';

    /**
     * Digital file format constants
     */
    const FORMAT_PDF = 'pdf';
    const FORMAT_EPUB = 'epub';
    const FORMAT_DOCX = 'docx';
    const FORMAT_XLSX = 'xlsx';
    const FORMAT_PPTX = 'pptx';
    const FORMAT_MP3 = 'mp3';
    const FORMAT_MP4 = 'mp4';
    const FORMAT_ZIP = 'zip';
    const FORMAT_EXE = 'exe';
    const FORMAT_APK = 'apk';

    /**
     * Check if product is digital
     */
    public function isDigital(): bool
    {
        return $this->product_type === self::TYPE_DIGITAL;
    }

    /**
     * Check if product is physical
     */
    public function isPhysical(): bool
    {
        return $this->product_type === self::TYPE_PHYSICAL;
    }

    /**
     * Get available digital formats
     */
    public static function getDigitalFormats(): array
    {
        return [
            self::FORMAT_PDF => 'PDF Document',
            self::FORMAT_EPUB => 'EPUB eBook',
            self::FORMAT_DOCX => 'Word Document',
            self::FORMAT_XLSX => 'Excel Spreadsheet',
            self::FORMAT_PPTX => 'PowerPoint Presentation',
            self::FORMAT_MP3 => 'Audio File (MP3)',
            self::FORMAT_MP4 => 'Video File (MP4)',
            self::FORMAT_ZIP => 'Archive (ZIP)',
            self::FORMAT_EXE => 'Software (EXE)',
            self::FORMAT_APK => 'Android App (APK)',
        ];
    }

    /**
     * Get icon for digital format
     */
    public function getDigitalFormatIcon(): string
    {
        return match ($this->digital_format) {
            self::FORMAT_PDF => 'heroicon-o-document-text',
            self::FORMAT_EPUB => 'heroicon-o-book-open',
            self::FORMAT_DOCX => 'heroicon-o-document',
            self::FORMAT_XLSX => 'heroicon-o-table-cells',
            self::FORMAT_PPTX => 'heroicon-o-presentation-chart-bar',
            self::FORMAT_MP3 => 'heroicon-o-musical-note',
            self::FORMAT_MP4 => 'heroicon-o-video-camera',
            self::FORMAT_ZIP => 'heroicon-o-archive-box',
            self::FORMAT_EXE => 'heroicon-o-cog-6-tooth',
            self::FORMAT_APK => 'heroicon-o-device-phone-mobile',
            default => 'heroicon-o-document',
        };
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSize(): string
    {
        if (!$this->file_size_mb) {
            return 'Unknown';
        }

        if ($this->file_size_mb < 1) {
            return number_format($this->file_size_mb * 1024, 0) . ' KB';
        } elseif ($this->file_size_mb < 1024) {
            return number_format($this->file_size_mb, 1) . ' MB';
        } else {
            return number_format($this->file_size_mb / 1024, 1) . ' GB';
        }
    }

    /**
     * Check if download is still valid
     */
    public function isDownloadValid(?\DateTime $purchaseDate = null): bool
    {
        if (!$this->isDigital() || !$this->download_expiry_days) {
            return true;
        }

        if (!$purchaseDate) {
            return true; // No purchase date provided, assume valid
        }

        $expiryDate = $purchaseDate->modify("+{$this->download_expiry_days} days");
        return new \DateTime() <= $expiryDate;
    }

    /** @return BelongsTo<Brand,self> */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class, 'shop_brand_id');
    }

    /** @return BelongsToMany<Category> */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class, 'shop_category_product', 'shop_product_id', 'shop_category_id')->withTimestamps();
    }

    /** @return MorphMany<Comment> */
    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    /**
     * Register media collections
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('product-images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);

        $this->addMediaCollection('digital-files')
            ->acceptsMimeTypes([
                'application/pdf',
                'application/epub+zip',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                'audio/mpeg',
                'video/mp4',
                'application/zip',
                'application/x-msdownload',
                'application/vnd.android.package-archive',
            ]);
    }
}
