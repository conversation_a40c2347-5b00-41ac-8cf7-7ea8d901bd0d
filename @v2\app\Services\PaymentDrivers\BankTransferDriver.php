<?php

namespace App\Services\PaymentDrivers;

use TomatoPHP\FilamentPayments\Services\Contracts\PaymentDriverInterface;
use TomatoPHP\FilamentPayments\Models\Payment;

class BankTransferDriver implements PaymentDriverInterface
{
    public function pay(Payment $payment): array
    {
        // Generate bank transfer instructions
        $bankDetails = [
            'bank_name' => 'Bangkok Bank',
            'account_name' => config('app.name'),
            'account_number' => '123-456-7890',
            'swift_code' => 'BKKBTHBK',
            'reference' => $payment->uuid,
            'amount' => $payment->amount,
        ];

        // Update payment status to pending
        $payment->update([
            'status' => 'pending',
            'gateway_response' => json_encode($bankDetails),
        ]);

        return [
            'success' => true,
            'payment_url' => route('payment.bank-transfer.instructions', $payment->uuid),
            'message' => 'Please complete the bank transfer and upload your slip for verification.',
            'bank_details' => $bankDetails,
        ];
    }

    public function verifySlip(Payment $payment, $slipImagePath): array
    {
        // Use SlipOK API to verify the slip
        $slipOkApiKey = config('services.slipok.api_key');

        if (!$slipOkApiKey) {
            return [
                'success' => false,
                'message' => 'SlipOK API key not configured',
            ];
        }

        try {
            $client = new \GuzzleHttp\Client();

            $response = $client->post('https://api.slipok.com/api/line/apikey/' . $slipOkApiKey, [
                'multipart' => [
                    [
                        'name' => 'files',
                        'contents' => fopen($slipImagePath, 'r'),
                        'filename' => basename($slipImagePath),
                    ]
                ]
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if ($result['success']) {
                $slipData = $result['data'];
                $bankDetails = json_decode($payment->gateway_response, true);

                // Verify amount matches
                $expectedAmount = $bankDetails['amount'];
                $slipAmount = $slipData['amount'];

                // Verify account number matches (if available)
                $expectedAccount = str_replace('-', '', $bankDetails['account_number']);
                $slipAccount = str_replace('-', '', $slipData['receiver']['account'] ?? '');

                $isAmountMatch = abs($expectedAmount - $slipAmount) < 0.01;
                $isAccountMatch = empty($slipAccount) || str_contains($slipAccount, $expectedAccount);

                if ($isAmountMatch && $isAccountMatch) {
                    // Payment verified successfully
                    $payment->update([
                        'status' => 'completed',
                        'verified_at' => now(),
                        'slip_data' => json_encode($slipData),
                    ]);

                    return [
                        'success' => true,
                        'message' => 'Payment verified successfully',
                        'slip_data' => $slipData,
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Payment verification failed: Amount or account mismatch',
                        'expected_amount' => $expectedAmount,
                        'slip_amount' => $slipAmount,
                        'expected_account' => $expectedAccount,
                        'slip_account' => $slipAccount,
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to read slip: ' . ($result['message'] ?? 'Unknown error'),
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error verifying slip: ' . $e->getMessage(),
            ];
        }
    }

    public function verify(Payment $payment): array
    {
        // In a real implementation, you would verify the payment with your bank
        // For now, we'll assume manual verification by admin
        
        return [
            'success' => $payment->status === 'completed',
            'message' => $payment->status === 'completed' 
                ? 'Payment verified successfully' 
                : 'Payment verification pending',
        ];
    }

    public function refund(Payment $payment, float $amount = null): array
    {
        $refundAmount = $amount ?? $payment->amount;
        
        // Create refund record
        $payment->update([
            'status' => 'refunded',
            'refunded_amount' => $refundAmount,
            'refunded_at' => now(),
        ]);

        return [
            'success' => true,
            'message' => 'Refund processed. Amount will be transferred back to your account within 3-5 business days.',
            'refund_amount' => $refundAmount,
        ];
    }

    public function webhook(array $data): array
    {
        // Handle bank transfer webhooks if available
        return [
            'success' => true,
            'message' => 'Webhook processed',
        ];
    }

    public function getPaymentMethods(): array
    {
        return [
            'bank_transfer' => [
                'name' => 'Bank Transfer',
                'description' => 'Transfer money directly from your bank account',
                'icon' => 'heroicon-o-building-library',
                'currencies' => ['THB', 'USD'],
            ],
        ];
    }

    public function isAvailable(): bool
    {
        return true;
    }

    public function getName(): string
    {
        return 'Bank Transfer';
    }

    public function getSlug(): string
    {
        return 'bank-transfer';
    }
}
