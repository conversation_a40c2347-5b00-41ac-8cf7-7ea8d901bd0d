<?php

namespace App\Filament\App\Widgets\Teacher;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class TeacherStudentManagementWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.teacher.teacher-student-management';
    
    protected int | string | array $columnSpan = 'full';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('teacher');
    }

    public function getStudents(): array
    {
        // Placeholder data - replace with actual student data
        return [
            [
                'number' => 1,
                'student_id' => '12345',
                'name' => 'เด็กชายสมชาย ใจดี',
                'subjects' => [
                    ['name' => 'คณิตศาสตร์', 'color' => 'blue'],
                    ['name' => 'ภาษาไทย', 'color' => 'green'],
                    ['name' => 'วิทยาศาสตร์', 'color' => 'yellow'],
                ],
                'status' => 'active',
                'status_label' => 'ปกติ',
                'status_color' => 'green'
            ],
            [
                'number' => 2,
                'student_id' => '12346',
                'name' => 'เด็กหญิงสมหญิง รักเรียน',
                'subjects' => [
                    ['name' => 'คณิตศาสตร์', 'color' => 'blue'],
                    ['name' => 'ภาษาไทย', 'color' => 'green'],
                    ['name' => 'ภาษาอังกฤษ', 'color' => 'purple'],
                ],
                'status' => 'active',
                'status_label' => 'ปกติ',
                'status_color' => 'green'
            ],
            [
                'number' => 3,
                'student_id' => '12347',
                'name' => 'เด็กชายวิชัย เรียนดี',
                'subjects' => [
                    ['name' => 'คณิตศาสตร์', 'color' => 'blue'],
                    ['name' => 'วิทยาศาสตร์', 'color' => 'yellow'],
                ],
                'status' => 'sick_leave',
                'status_label' => 'ลาป่วย',
                'status_color' => 'yellow'
            ],
        ];
    }

    public function getClasses(): array
    {
        return [
            ['value' => 'm1-1', 'label' => 'ม.1/1'],
            ['value' => 'm1-2', 'label' => 'ม.1/2'],
            ['value' => 'm2-1', 'label' => 'ม.2/1'],
            ['value' => 'm2-2', 'label' => 'ม.2/2'],
            ['value' => 'm3-1', 'label' => 'ม.3/1'],
            ['value' => 'm3-2', 'label' => 'ม.3/2'],
        ];
    }

    public function getCurrentClass(): string
    {
        return 'ม.1/1'; // This would be dynamic based on selected class
    }

    public function getTotalStudents(): int
    {
        return count($this->getStudents());
    }
}
