@extends('layouts.frontend')

@section('title', $subject->name)

@section('content')
<div class="bg-white">
    <div class="relative bg-indigo-900">
        <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
            <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">{{ $subject->name }}</h1>
            @if($subject->description)
                <p class="mt-6 text-xl text-indigo-100 max-w-3xl">{{ strip_tags($subject->description) }}</p>
            @endif
        </div>
    </div>

    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        @if($subject->courses && $subject->courses->count() > 0)
            <div class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Courses in {{ $subject->name }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($subject->courses as $course)
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $course->title }}</h3>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ strip_tags($course->description) }}</p>
                                <a href="{{ route('frontend.courses.show', $course) }}" class="text-blue-600 hover:text-blue-500 font-medium text-sm">
                                    View Course →
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if($subject->books && $subject->books->count() > 0)
            <div class="mb-12">
                <h2 class="text-2xl font-bold text-gray-900 mb-6">Books in {{ $subject->name }}</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach($subject->books as $book)
                        <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $book->title }}</h3>
                                @if($book->author)
                                    <p class="text-sm text-gray-600 mb-2">by {{ $book->author }}</p>
                                @endif
                                @if($book->description)
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">{{ strip_tags($book->description) }}</p>
                                @endif
                                <a href="{{ route('frontend.books.show', $book) }}" class="text-green-600 hover:text-green-500 font-medium text-sm">
                                    View Book →
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if((!$subject->courses || $subject->courses->count() === 0) && (!$subject->books || $subject->books->count() === 0))
            <div class="text-center py-12">
                <h3 class="mt-2 text-sm font-medium text-gray-900">No content available</h3>
                <p class="mt-1 text-sm text-gray-500">There are no courses or books in this subject yet.</p>
            </div>
        @endif
    </div>
</div>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endsection
