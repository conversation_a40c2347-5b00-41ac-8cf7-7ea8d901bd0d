.card-shadow{box-shadow:0 4px 15px #0000001a;transition:box-shadow .3s ease}.card-shadow:hover{box-shadow:0 8px 25px #00000026}.gradient-bg{background:linear-gradient(135deg,#4F46E5 0%,#7C3AED 100%);transition:all .3s ease}.gradient-bg:hover{background:linear-gradient(135deg,#4338CA 0%,#6D28D9 100%);transform:translateY(-1px)}.activity-card{transition:all .3s ease;border:1px solid #e5e7eb}.activity-card:hover{transform:translateY(-5px);box-shadow:0 10px 30px #00000026}.checkbox-item{position:absolute;opacity:0;width:100%;height:100%;cursor:pointer;z-index:10}.checkbox-item:checked+label{background-color:#eef2ff;border-color:#4f46e5;transform:scale(1.02)}.checkbox-item:checked+label .check-icon{display:flex!important;animation:checkmark .3s ease-in-out}@keyframes checkmark{0%{transform:scale(0);opacity:0}50%{transform:scale(1.2)}to{transform:scale(1);opacity:1}}.progress-bar{height:8px;border-radius:4px;background-color:#e5e7eb;overflow:hidden;position:relative}.progress-fill{height:100%;border-radius:4px;background:linear-gradient(90deg,#4F46E5 0%,#7C3AED 100%);transition:width 1s ease-in-out;position:relative}.progress-fill:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background:linear-gradient(90deg,transparent,rgba(255,255,255,.3),transparent);animation:shimmer 2s infinite}@keyframes shimmer{0%{transform:translate(-100%)}to{transform:translate(100%)}}.form-input{transition:all .2s ease}.form-input:focus{transform:scale(1.02);box-shadow:0 0 0 3px #4f46e51a}.btn-primary{background:linear-gradient(135deg,#4F46E5 0%,#7C3AED 100%);transition:all .3s ease}.btn-primary:hover{background:linear-gradient(135deg,#4338CA 0%,#6D28D9 100%);transform:translateY(-2px);box-shadow:0 8px 20px #4f46e54d}.btn-secondary{transition:all .2s ease}.btn-secondary:hover{transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.status-badge{font-size:.75rem;padding:.25rem .5rem;border-radius:9999px;font-weight:500;text-transform:uppercase;letter-spacing:.025em}.status-active{background-color:#3b82f6;color:#fff;animation:pulse-blue 2s infinite}.status-completed{background-color:#6b7280;color:#fff}@keyframes pulse-blue{0%,to{opacity:1}50%{opacity:.8}}.activity-tag{font-size:.75rem;padding:.25rem .5rem;border-radius:9999px;font-weight:500;transition:all .2s ease}.activity-tag:hover{transform:scale(1.05)}.icon-primary{color:#4f46e5;transition:color .2s ease}.icon-primary:hover{color:#4338ca}.loading{animation:pulse 2s cubic-bezier(.4,0,.6,1) infinite}@keyframes pulse{0%,to{opacity:1}50%{opacity:.5}}.form-slide-in{animation:slideInLeft .5s ease-out}@keyframes slideInLeft{0%{transform:translate(-20px);opacity:0}to{transform:translate(0);opacity:1}}.card-fade-in{animation:fadeInUp .6s ease-out}@keyframes fadeInUp{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}.hover-lift{transition:transform .2s ease}.hover-lift:hover{transform:translateY(-2px)}.hover-scale{transition:transform .2s ease}.hover-scale:hover{transform:scale(1.05)}.modal-overlay{background-color:#00000080;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);transition:opacity .3s ease}.modal-content{animation:modalSlideIn .3s ease-out}@keyframes modalSlideIn{0%{transform:translateY(-20px) scale(.95);opacity:0}to{transform:translateY(0) scale(1);opacity:1}}.notification{animation:slideInRight .3s ease-out}@keyframes slideInRight{0%{transform:translate(100%);opacity:0}to{transform:translate(0);opacity:1}}@media (max-width: 768px){.activity-card{margin-bottom:1rem}.form-slide-in,.card-fade-in{animation:none}.progress-bar{height:6px}.activity-tag,.status-badge{font-size:.6rem;padding:.125rem .375rem}}.focus-ring:focus{outline:2px solid transparent;outline-offset:2px;box-shadow:0 0 0 3px #4f46e51a}.custom-scrollbar::-webkit-scrollbar{width:6px}.custom-scrollbar::-webkit-scrollbar-track{background:#f1f5f9;border-radius:3px}.custom-scrollbar::-webkit-scrollbar-thumb{background:#cbd5e1;border-radius:3px}.custom-scrollbar::-webkit-scrollbar-thumb:hover{background:#94a3b8}.text-shadow{text-shadow:0 1px 2px rgba(0,0,0,.1)}.glass-effect{background:rgba(255,255,255,.9);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border:1px solid rgba(255,255,255,.2)}.error-shake{animation:shake .5s ease-in-out}@keyframes shake{0%,to{transform:translate(0)}25%{transform:translate(-5px)}75%{transform:translate(5px)}}.success-bounce{animation:bounce .6s ease-in-out}@keyframes bounce{0%,20%,50%,80%,to{transform:translateY(0)}40%{transform:translateY(-10px)}60%{transform:translateY(-5px)}}
