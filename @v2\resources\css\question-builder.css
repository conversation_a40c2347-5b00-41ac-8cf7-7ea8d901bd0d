/* Question Builder Styles */

/* Improve drag handle visibility */
.fi-fo-builder-item-actions .fi-btn-icon {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 6px;
    padding: 8px;
    transition: all 0.2s ease;
}

.fi-fo-builder-item-actions .fi-btn-icon:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
    transform: scale(1.05);
}

/* Style the question blocks */
.fi-fo-builder-block {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.fi-fo-builder-block:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Style the repeater items (choices) */
.fi-fo-repeater-item {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background: #f9fafb;
    margin-bottom: 8px;
    transition: all 0.2s ease;
}

.fi-fo-repeater-item:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

/* Improve the add buttons */
.fi-fo-builder-block-picker-ctn .fi-btn,
.fi-fo-repeater-add-action .fi-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 24px;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.fi-fo-builder-block-picker-ctn .fi-btn:hover,
.fi-fo-repeater-add-action .fi-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* Style the question type badges */
.fi-fo-field-wrp-label {
    font-weight: 600;
    color: #374151;
}

/* Improve spacing and layout */
.fi-fo-builder-blocks {
    gap: 16px;
}

.fi-fo-repeater-items {
    gap: 8px;
}

/* Style the correct answer toggle */
.fi-fo-toggle.fi-color-success .fi-fo-toggle-button {
    background-color: #10b981;
}

/* Add visual feedback for dragging */
.sortable-ghost {
    opacity: 0.5;
    background: #f3f4f6;
    border: 2px dashed #9ca3af;
}

.sortable-chosen {
    transform: rotate(2deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Style the language switcher in topbar */
.fi-topbar .language-switcher {
    margin-right: 1rem;
}

.fi-topbar .language-switcher button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #374151;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.fi-topbar .language-switcher button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Dark mode adjustments */
.dark .fi-fo-builder-block {
    background: #1f2937;
    border-color: #374151;
}

.dark .fi-fo-builder-block:hover {
    border-color: #3b82f6;
}

.dark .fi-fo-repeater-item {
    background: #111827;
    border-color: #374151;
}

.dark .fi-fo-repeater-item:hover {
    background: #1e3a8a;
    border-color: #3b82f6;
}
