document.addEventListener("DOMContentLoaded",function(){p(),L(),v(),y();function p(){const s=document.getElementById("registerForm");s.querySelectorAll(".form-input").forEach(o=>{o.addEventListener("blur",l),o.addEventListener("input",c)});function l(o){const t=o.target,e=t.value.trim(),a=t.name;switch(n(t),a){case"role":e?d(t):i(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E1B\u0E23\u0E30\u0E40\u0E20\u0E17\u0E1C\u0E39\u0E49\u0E43\u0E0A\u0E49");break;case"name":e?e.length<2?i(t,"\u0E0A\u0E37\u0E48\u0E2D\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E2D\u0E22\u0E48\u0E32\u0E07\u0E19\u0E49\u0E2D\u0E22 2 \u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23"):d(t):i(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E0A\u0E37\u0E48\u0E2D-\u0E19\u0E32\u0E21\u0E2A\u0E01\u0E38\u0E25");break;case"email":e?u(e)?d(t):i(t,"\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E2D\u0E35\u0E40\u0E21\u0E25\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07"):i(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2D\u0E35\u0E40\u0E21\u0E25");break;case"password":e?e.length<8?i(t,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E2D\u0E22\u0E48\u0E32\u0E07\u0E19\u0E49\u0E2D\u0E22 8 \u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23"):f(e)?d(t):i(t,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E15\u0E49\u0E2D\u0E07\u0E21\u0E35\u0E15\u0E31\u0E27\u0E2D\u0E31\u0E01\u0E29\u0E23\u0E43\u0E2B\u0E0D\u0E48 \u0E40\u0E25\u0E47\u0E01 \u0E41\u0E25\u0E30\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02"):i(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19");break;case"password_confirmation":const m=s.querySelector('input[name="password"]');e?e!==m.value?i(t,"\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19\u0E44\u0E21\u0E48\u0E15\u0E23\u0E07\u0E01\u0E31\u0E19"):d(t):i(t,"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19\u0E23\u0E2B\u0E31\u0E2A\u0E1C\u0E48\u0E32\u0E19");break}}function c(o){const t=o.target;t.classList.contains("error")&&n(t)}function i(o,t){o.classList.add("error"),o.classList.remove("success");const e=document.createElement("div");e.className="error-message",e.innerHTML=`
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                ${t}
            `,o.parentNode.appendChild(e)}function d(o){o.classList.add("success"),o.classList.remove("error")}function n(o){o.classList.remove("error","success");const t=o.parentNode.querySelector(".error-message");t&&t.remove()}function u(o){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o)}function f(o){const t=/[A-Z]/.test(o),e=/[a-z]/.test(o),a=/\d/.test(o);return t&&e&&a}}function L(){document.querySelectorAll('.social-button:not([id="phoneLogin"])').forEach(l=>{const c=l.id.replace("Login","");l.addEventListener("click",()=>r(c,l))});function r(l,c){c.textContent,h(c,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E40\u0E0A\u0E37\u0E48\u0E2D\u0E21\u0E15\u0E48\u0E2D..."),window.location.href=`/auth/${l}`}}function v(){const s=document.getElementById("phoneLogin"),r=document.getElementById("phoneModal"),l=document.getElementById("closePhoneModal"),c=document.getElementById("sendOtp"),i=document.getElementById("verifyOtp"),d=document.getElementById("phoneNumber"),n=document.querySelectorAll(".otp-input");s&&s.addEventListener("click",function(){r.classList.remove("hidden"),d.focus()}),l&&l.addEventListener("click",function(){r.classList.add("hidden"),o()}),r&&r.addEventListener("click",function(e){e.target===this&&(this.classList.add("hidden"),o())}),c&&c.addEventListener("click",u),i&&i.addEventListener("click",f),n.forEach((e,a)=>{e.addEventListener("input",function(m){m.target.value.length===1&&a<n.length-1&&n[a+1].focus()}),e.addEventListener("keydown",function(m){m.key==="Backspace"&&m.target.value===""&&a>0&&n[a-1].focus()})});function u(){const e=d.value.trim(),a=document.getElementById("countryCode").value;if(!e){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C","error");return}if(!t(e)){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\u0E42\u0E17\u0E23\u0E28\u0E31\u0E1E\u0E17\u0E4C\u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error");return}h(c,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E48\u0E07 OTP..."),setTimeout(()=>{document.getElementById("phoneStep").classList.add("hidden"),document.getElementById("otpStep").classList.remove("hidden"),w(c,"\u0E2A\u0E48\u0E07 OTP"),window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E2A\u0E48\u0E07 OTP \u0E44\u0E1B\u0E22\u0E31\u0E07 "+a+e+" \u0E41\u0E25\u0E49\u0E27","success"),n[0].focus()},2e3)}function f(){const e=Array.from(n).map(a=>a.value).join("");if(e.length!==6){window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E01\u0E23\u0E38\u0E13\u0E32\u0E01\u0E23\u0E2D\u0E01 OTP \u0E43\u0E2B\u0E49\u0E04\u0E23\u0E1A 6 \u0E2B\u0E25\u0E31\u0E01","error");return}h(i,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E15\u0E23\u0E27\u0E08\u0E2A\u0E2D\u0E1A..."),setTimeout(()=>{e==="123456"?(window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19\u0E15\u0E31\u0E27\u0E15\u0E19\u0E2A\u0E33\u0E40\u0E23\u0E47\u0E08!","success"),r.classList.add("hidden"),o()):(window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E23\u0E2B\u0E31\u0E2A OTP \u0E44\u0E21\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07","error"),n.forEach(a=>a.value=""),n[0].focus()),w(i,"\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19 OTP")},2e3)}function o(){document.getElementById("phoneStep").classList.remove("hidden"),document.getElementById("otpStep").classList.add("hidden"),d.value="",n.forEach(e=>e.value="")}function t(e){return/^[0-9]{9,10}$/.test(e)}}function y(){const s=document.getElementById("registerForm"),r=document.getElementById("submitBtn");s&&s.addEventListener("submit",function(i){i.preventDefault(),l()&&c()});function l(){const i=s.querySelectorAll(".form-input[required]");let d=!0;return i.forEach(n=>{const u=new Event("blur");n.dispatchEvent(u),(n.classList.contains("error")||!n.value.trim())&&(d=!1)}),d}async function c(){h(r,"\u0E01\u0E33\u0E25\u0E31\u0E07\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E1A\u0E31\u0E0D\u0E0A\u0E35...");try{const i=new FormData(s),n=await(await fetch("/api/register",{method:"POST",headers:{"X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content"),Accept:"application/json"},body:i})).json();n.success?(window.LayoutUtils&&window.LayoutUtils.showNotification(n.message,"success"),setTimeout(()=>{window.location.href=n.redirect},1500)):(window.LayoutUtils&&window.LayoutUtils.showNotification(n.message||"\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14","error"),n.errors&&Object.keys(n.errors).forEach(u=>{const f=s.querySelector(`[name="${u}"]`);f&&showFieldError(f,n.errors[u][0])}))}catch{window.LayoutUtils&&window.LayoutUtils.showNotification("\u0E40\u0E01\u0E34\u0E14\u0E02\u0E49\u0E2D\u0E1C\u0E34\u0E14\u0E1E\u0E25\u0E32\u0E14\u0E43\u0E19\u0E01\u0E32\u0E23\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E1A\u0E31\u0E0D\u0E0A\u0E35","error")}w(r,"\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E1A\u0E31\u0E0D\u0E0A\u0E35")}}function h(s,r){s.disabled=!0,s.innerHTML=`
            <div class="loading">
                <div class="loading-spinner"></div>
                ${r}
            </div>
        `}function w(s,r){s.disabled=!1,s.innerHTML=r}});
