<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Student & Exam Info -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-start justify-between mb-4">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">{{ $this->user->name }}</h2>
                    <p class="text-sm text-gray-500">{{ $this->user->email }}</p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Exam</div>
                    <div class="font-medium text-gray-900">{{ $this->exam->title }}</div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->attempts->count() }}</div>
                    <div class="text-xs text-gray-500">Total Attempts</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->attempts->max('total_score') ?? 0 }}</div>
                    <div class="text-xs text-gray-500">Best Score</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->attempts->first()?->total_score ?? 0 }}</div>
                    <div class="text-xs text-gray-500">Latest Score</div>
                </div>
                <div class="text-center p-3 bg-gray-50 rounded">
                    <div class="text-lg font-semibold text-gray-900">{{ $this->exam->total_score }}</div>
                    <div class="text-xs text-gray-500">Total Possible</div>
                </div>
            </div>
        </div>

        <!-- Attempts Tabs -->
        @if($this->attempts->count() > 0)
            <div class="bg-white rounded-lg border border-gray-200">
                <!-- Tab Headers -->
                <div class="border-b border-gray-200">
                    <nav class="flex space-x-8 px-6" aria-label="Tabs">
                        @foreach($this->attempts as $index => $attempt)
                            <button
                                wire:click="setActiveTab({{ $index }})"
                                class="py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap {{ $this->activeTab === $index ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}"
                            >
                                Attempt #{{ $attempt->attempt_number }}
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($attempt->status === 'graded') bg-green-100 text-green-800
                                    @elseif($attempt->status === 'submitted') bg-blue-100 text-blue-800
                                    @elseif($attempt->status === 'in_progress') bg-yellow-100 text-yellow-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    {{ ucfirst($attempt->status) }}
                                </span>
                                @if($attempt->total_score !== null)
                                    <span class="ml-2 text-xs text-gray-500">
                                        {{ $attempt->total_score }}/{{ $this->exam->total_score }}
                                    </span>
                                @endif
                            </button>
                        @endforeach
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    @if(isset($this->attempts[$this->activeTab]))
                        @php $currentAttempt = $this->attempts[$this->activeTab]; @endphp
                        
                        <!-- Attempt Summary -->
                        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">Started:</span>
                                    <span class="font-medium">{{ $currentAttempt->started_at?->format('M j, Y g:i A') ?? 'N/A' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Submitted:</span>
                                    <span class="font-medium">{{ $currentAttempt->submitted_at?->format('M j, Y g:i A') ?? 'Not submitted' }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">Time Taken:</span>
                                    <span class="font-medium">
                                        @if($currentAttempt->started_at && $currentAttempt->submitted_at)
                                            {{ $currentAttempt->started_at->diffInMinutes($currentAttempt->submitted_at) }} minutes
                                        @else
                                            N/A
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Grading Form -->
                        <form wire:submit="save">
                            {{ $this->form }}
                            
                            <div class="mt-6 flex justify-end space-x-3">
                                <button type="submit" 
                                        class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                    Save Grading
                                </button>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        @else
            <div class="bg-white rounded-lg border border-gray-200 p-6 text-center">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No attempts found</h3>
                    <p class="mt-1 text-sm text-gray-500">This student hasn't attempted this exam yet.</p>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
