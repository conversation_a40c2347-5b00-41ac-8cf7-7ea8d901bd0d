<?php

namespace App\Filament\App\Widgets\Parent;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class PaymentStatusWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.parent.payment-status';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('parent');
    }

    public function getPayments(): array
    {
        // Placeholder data - replace with actual payment data
        return [
            [
                'description' => 'ค่าเทอม - มกราคม 2567',
                'amount' => '15,000 บาท',
                'due_date' => '15 ม.ค. 2567',
                'status' => 'pending',
                'priority' => 'high',
                'color' => 'danger'
            ],
            [
                'description' => 'ค่าอุปกรณ์การเรียน',
                'amount' => '2,500 บาท',
                'due_date' => '20 ม.ค. 2567',
                'status' => 'pending',
                'priority' => 'medium',
                'color' => 'warning'
            ],
            [
                'description' => 'ค่าเทอม - ธันวาคม 2566',
                'amount' => '15,000 บาท',
                'due_date' => '15 ธ.ค. 2566',
                'status' => 'paid',
                'priority' => 'low',
                'color' => 'success'
            ],
        ];
    }

    public function getTotalPending(): string
    {
        return '17,500 บาท'; // Calculate from actual data
    }
}
