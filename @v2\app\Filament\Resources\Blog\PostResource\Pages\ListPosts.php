<?php

namespace App\Filament\Resources\Blog\PostResource\Pages;

use App\Filament\Resources\Blog\PostResource;
use App\Filament\Resources\Blog\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPosts extends ListRecords
{
    protected static string $resource = PostResource::class;

    protected function getActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->url(fn (): string => static::getResource()::getUrl('create', ['type' => request()->get('type')])),
        ];
    }

    public function getTitle(): string
    {
        $type = request()->get('type');
        if ($type) {
            $config = config("post-types.types.{$type}", []);
            return ($config['name'] ?? ucfirst($type)) . ' Posts';
        }

        return 'Posts';
    }

    protected function getHeaderActions(): array
    {
        $type = request()->get('type', config('post-types.default_type', 'blog'));
        $typeConfig = config("post-types.types.{$type}", []);

        return [
            Actions\CreateAction::make()
                ->url(fn (): string => static::getResource()::getUrl('create', ['type' => $type])),

            Actions\Action::make('manageCategories')
                ->label('Manage ' . ($typeConfig['name'] ?? ucfirst($type)) . ' Categories')
                ->icon('heroicon-o-rectangle-stack')
                ->color('gray')
                ->url(fn (): string => CategoryResource::getUrl('index', ['type' => $type]))
                ->openUrlInNewTab(),
        ];
    }
}
