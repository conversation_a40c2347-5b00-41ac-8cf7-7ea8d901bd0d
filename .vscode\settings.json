{"intelephense.stubs": ["apache", "bcmath", "bz2", "calendar", "com_dotnet", "Core", "ctype", "curl", "date", "dba", "dom", "enchant", "exif", "FFI", "fileinfo", "filter", "fpm", "ftp", "gd", "gettext", "gmp", "hash", "iconv", "imap", "intl", "json", "ldap", "libxml", "mbstring", "meta", "mysq<PERSON>", "oci8", "odbc", "openssl", "pcntl", "pcre", "PDO", "pdo_ibm", "pdo_mysql", "pdo_pgsql", "pdo_sqlite", "pgsql", "<PERSON><PERSON>", "posix", "pspell", "readline", "Reflection", "session", "shmop", "SimpleXML", "snmp", "soap", "sockets", "sodium", "SPL", "sqlite3", "standard", "superglobals", "sysvmsg", "sysvsem", "sysvshm", "tidy", "tokenizer", "xml", "xmlreader", "xmlrpc", "xmlwriter", "xsl", "Zend OPcache", "zip", "zlib"], "intelephense.files.associations": ["*.php", "*.phtml"], "intelephense.files.maxSize": 5000000, "intelephense.completion.insertUseDeclaration": true, "intelephense.completion.fullyQualifyGlobalConstantsAndFunctions": false, "intelephense.diagnostics.undefinedMethods": true, "intelephense.diagnostics.undefinedFunctions": true, "intelephense.diagnostics.undefinedConstants": true, "intelephense.diagnostics.undefinedClassConstants": true, "intelephense.diagnostics.undefinedProperties": true, "intelephense.diagnostics.undefinedTypes": true, "intelephense.diagnostics.undefinedVariables": true, "intelephense.environment.includePaths": ["vendor/", "./"], "intelephense.environment.phpVersion": "8.2.0", "php.suggest.basic": false, "php.validate.enable": false}