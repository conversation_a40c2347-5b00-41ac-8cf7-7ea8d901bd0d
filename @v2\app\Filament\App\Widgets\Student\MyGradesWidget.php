<?php

namespace App\Filament\App\Widgets\Student;

use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class MyGradesWidget extends Widget
{
    protected static string $view = 'filament.app.widgets.student.my-grades';

    public static function canView(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole('student');
    }

    public function getSubjectProgress(): array
    {
        // Placeholder data - replace with actual subject progress
        return [
            [
                'name' => 'คณิตศาสตร์',
                'progress' => 75,
                'average_score' => 92,
                'max_score' => 100,
                'exercises_completed' => '75%',
                'color' => '#3b82f6', // blue
                'bg_gradient' => 'from-blue-500 to-blue-600'
            ],
            [
                'name' => 'วิทยาศาสตร์',
                'progress' => 60,
                'average_score' => 85,
                'max_score' => 100,
                'exercises_completed' => '60%',
                'color' => '#22c55e', // green
                'bg_gradient' => 'from-green-500 to-green-600'
            ],
            [
                'name' => 'ภาษาไทย',
                'progress' => 90,
                'average_score' => 99,
                'max_score' => 100,
                'exercises_completed' => '90%',
                'color' => '#a855f7', // purple
                'bg_gradient' => 'from-purple-500 to-purple-600'
            ],
            [
                'name' => 'ภาษาอังกฤษ',
                'progress' => 45,
                'average_score' => 78,
                'max_score' => 100,
                'exercises_completed' => '45%',
                'color' => '#eab308', // yellow
                'bg_gradient' => 'from-yellow-500 to-yellow-600'
            ],
            [
                'name' => 'สังคมศึกษา',
                'progress' => 65,
                'average_score' => 88,
                'max_score' => 100,
                'exercises_completed' => '65%',
                'color' => '#ec4899', // pink
                'bg_gradient' => 'from-pink-500 to-pink-600'
            ],
            [
                'name' => 'ศิลปะ',
                'progress' => 80,
                'average_score' => 95,
                'max_score' => 100,
                'exercises_completed' => '80%',
                'color' => '#f97316', // orange
                'bg_gradient' => 'from-orange-500 to-orange-600'
            ],
        ];
    }

    public function getCircleProgress($progress): array
    {
        $circumference = 2 * pi() * 45; // radius = 45
        $strokeDasharray = $circumference;
        $strokeDashoffset = $circumference - ($progress / 100) * $circumference;

        return [
            'strokeDasharray' => $strokeDasharray,
            'strokeDashoffset' => $strokeDashoffset
        ];
    }
}
