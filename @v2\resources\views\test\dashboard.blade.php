<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Development Testing Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">🧪 Development Testing Dashboard</h1>
                <p class="text-gray-600">Comprehensive testing tools for development and debugging</p>
                
                @if(!config('app.debug'))
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4">
                        <strong>Warning:</strong> Testing tools are only available when APP_DEBUG=true
                    </div>
                @endif
            </div>

            <!-- Testing Tools Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                
                <!-- Resource Permission Tester -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Permission Tester</h3>
                            <p class="text-sm text-gray-500">Test resource permissions across roles</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        Switch between different users and roles to test resource permissions without manual login/logout cycles.
                    </p>
                    <div class="flex space-x-2">
                        <a href="{{ route('test.permissions') }}" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-center text-sm">
                            Open Tester
                        </a>
                    </div>
                </div>

                <!-- SVG Demo -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">SVG Demo</h3>
                            <p class="text-sm text-gray-500">View available SVG animations</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        Browse and test all available SVG animations and graphics with easy enable/disable functionality.
                    </p>
                    <div class="flex space-x-2">
                        <a href="{{ route('test.svg-demo') }}" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-center text-sm">
                            View SVGs
                        </a>
                    </div>
                </div>

                <!-- SafeIcon Demo -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">SafeIcon Demo</h3>
                            <p class="text-sm text-gray-500">Browse available icons</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        Explore the complete SafeIcon library with search and preview functionality.
                    </p>
                    <div class="flex space-x-2">
                        <a href="{{ route('test.safeicon-demo') }}" class="flex-1 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-center text-sm">
                            Browse Icons
                        </a>
                    </div>
                </div>

                <!-- Command Line Tools -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">CLI Commands</h3>
                            <p class="text-sm text-gray-500">Available artisan commands</p>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm mb-4">
                        Command line tools for testing and development.
                    </p>
                    <div class="space-y-2 text-xs font-mono bg-gray-50 p-3 rounded">
                        <div>php artisan test:permissions</div>
                        <div>php artisan test:models</div>
                        <div>php artisan migrate:fresh --seed</div>
                    </div>
                </div>

                <!-- Database Tools -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Database Status</h3>
                            <p class="text-sm text-gray-500">Current database information</p>
                        </div>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Teams:</span>
                            <span class="font-medium">{{ \App\Models\Team::count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Users:</span>
                            <span class="font-medium">{{ \App\Models\User::count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Exams:</span>
                            <span class="font-medium">{{ \App\Models\Exam\Exam::count() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Assignments:</span>
                            <span class="font-medium">{{ \App\Models\Assignment::count() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Environment Info -->
                <div class="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900">Environment</h3>
                            <p class="text-sm text-gray-500">Current environment settings</p>
                        </div>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Environment:</span>
                            <span class="font-medium {{ config('app.env') === 'production' ? 'text-red-600' : 'text-green-600' }}">
                                {{ config('app.env') }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Debug:</span>
                            <span class="font-medium {{ config('app.debug') ? 'text-green-600' : 'text-red-600' }}">
                                {{ config('app.debug') ? 'Enabled' : 'Disabled' }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Laravel:</span>
                            <span class="font-medium">{{ app()->version() }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">PHP:</span>
                            <span class="font-medium">{{ PHP_VERSION }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">🚀 Quick Actions</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <a href="/backend" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-center">
                        Admin Panel
                    </a>
                    <a href="/app" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 text-center">
                        App Panel
                    </a>
                    <a href="/exams" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 text-center">
                        Public Exams
                    </a>
                    <a href="/assignments" class="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 text-center">
                        Assignments
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
