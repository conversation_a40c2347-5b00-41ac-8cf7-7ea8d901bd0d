<?php

namespace App\Filament\Clusters\Products\Resources\PhysicalProductResource\Pages;

use App\Filament\Clusters\Products\Resources\PhysicalProductResource;
use App\Models\Shop\Brand;
use App\Models\Shop\Category;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Str;

class EditPhysicalProduct extends EditRecord
{
    protected static string $resource = PhysicalProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            
            Actions\Action::make('bulkCreateBrands')
                ->label('Quick Create Brands')
                ->icon('heroicon-o-tag')
                ->color('info')
                ->form([
                    \Filament\Forms\Components\Textarea::make('brand_names')
                        ->label('Brand Names')
                        ->placeholder('Enter brand names, one per line:' . "\n" . 'Apple' . "\n" . 'Samsung' . "\n" . 'Nike')
                        ->rows(5)
                        ->required()
                        ->helperText('Enter one brand name per line. Brands will be created automatically.'),
                        
                    \Filament\Forms\Components\Toggle::make('make_visible')
                        ->label('Make all brands visible')
                        ->default(true),
                ])
                ->action(function (array $data) {
                    $names = array_filter(array_map('trim', explode("\n", $data['brand_names'])));
                    $created = 0;
                    
                    foreach ($names as $name) {
                        if (!empty($name)) {
                            Brand::create([
                                'name' => $name,
                                'slug' => Str::slug($name),
                                'description' => null,
                                'position' => 0,
                                'is_visible' => $data['make_visible'] ?? true,
                                'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                            ]);
                            $created++;
                        }
                    }
                    
                    Notification::make()
                        ->title("Created {$created} brands successfully!")
                        ->success()
                        ->send();
                }),

            Actions\Action::make('bulkCreateCategories')
                ->label('Quick Create Categories')
                ->icon('heroicon-o-folder')
                ->color('success')
                ->form([
                    \Filament\Forms\Components\Textarea::make('category_names')
                        ->label('Category Names')
                        ->placeholder('Enter category names, one per line:' . "\n" . 'Electronics' . "\n" . 'Clothing' . "\n" . 'Books')
                        ->rows(5)
                        ->required()
                        ->helperText('Enter one category name per line. Categories will be created automatically.'),
                        
                    \Filament\Forms\Components\Toggle::make('make_visible')
                        ->label('Make all categories visible')
                        ->default(true),
                ])
                ->action(function (array $data) {
                    $names = array_filter(array_map('trim', explode("\n", $data['category_names'])));
                    $created = 0;
                    
                    foreach ($names as $name) {
                        if (!empty($name)) {
                            Category::create([
                                'name' => $name,
                                'slug' => Str::slug($name),
                                'description' => null,
                                'position' => 0,
                                'is_visible' => $data['make_visible'] ?? true,
                                'team_id' => \Filament\Facades\Filament::getTenant()?->id,
                            ]);
                            $created++;
                        }
                    }
                    
                    Notification::make()
                        ->title("Created {$created} categories successfully!")
                        ->success()
                        ->send();
                }),
        ];
    }
}
