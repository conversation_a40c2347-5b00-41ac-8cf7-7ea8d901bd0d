.font-prompt{font-family:Prompt,sans-serif}html{scroll-behavior:smooth}.hover-lift{transition:transform .3s cubic-bezier(.34,1.56,.64,1),box-shadow .3s cubic-bezier(.34,1.56,.64,1)}.hover-lift:hover{transform:translateY(-8px);box-shadow:0 15px 30px #0000001a}.btn-primary{background:linear-gradient(90deg,#06b6d4,#3b82f6);transition:all .3s cubic-bezier(.34,1.56,.64,1);box-shadow:0 4px 15px #3b82f633;position:relative;overflow:hidden}.btn-primary:hover{transform:translateY(-3px);box-shadow:0 8px 25px #3b82f64d}.btn-primary:active{transform:translateY(-1px)}.btn-primary:after{content:"";position:absolute;top:50%;left:50%;width:5px;height:5px;background:rgba(255,255,255,.5);opacity:0;border-radius:100%;transform:scale(1) translate(-50%);transform-origin:50% 50%}.btn-primary:hover:after{animation:ripple 1s ease-out}@keyframes ripple{0%{transform:scale(0);opacity:.5}to{transform:scale(20);opacity:0}}.btn-secondary{transition:all .3s cubic-bezier(.34,1.56,.64,1);position:relative;overflow:hidden}.btn-secondary:hover{transform:translateY(-3px);box-shadow:0 5px 15px #0000000d}.nav-link:hover{color:#06b6d4}.course-card{transition:all .4s cubic-bezier(.34,1.56,.64,1);border:1px solid rgba(226,232,240,.8)}.course-card:hover{transform:translateY(-10px);box-shadow:0 15px 30px #0000001a;border-color:#06b6d44d}.feature-icon{transition:all .5s cubic-bezier(.34,1.56,.64,1)}.feature-card:hover .feature-icon{transform:scale(1.1) rotate(5deg)}.testimonial-card{transition:all .4s cubic-bezier(.34,1.56,.64,1)}.testimonial-card:hover{transform:translateY(-5px) scale(1.02);box-shadow:0 10px 25px #00000014}.float{animation:float 6s ease-in-out infinite}@keyframes float{0%{transform:translateY(0)}50%{transform:translateY(-20px)}to{transform:translateY(0)}}.pulse{animation:pulse 3s infinite}@keyframes pulse{0%{transform:scale(1)}50%{transform:scale(1.05)}to{transform:scale(1)}}.fade-in{animation:fadeIn 1s ease-out forwards;opacity:0}@keyframes fadeIn{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.stagger-1{animation-delay:.1s}.stagger-2{animation-delay:.2s}.stagger-3{animation-delay:.3s}.stagger-4{animation-delay:.4s}.stagger-5{animation-delay:.5s}.stagger-6{animation-delay:.6s}.category-badge{transition:all .3s ease}.category-badge:hover{transform:translateY(-3px);box-shadow:0 5px 15px #0000000d}.counter-value{transition:all .5s ease-out}.instructor-card{transition:all .4s cubic-bezier(.34,1.56,.64,1)}.instructor-card:hover{transform:translateY(-8px);box-shadow:0 15px 30px #0000001a}.social-icon{transition:all .3s ease}.social-icon:hover{transform:translateY(-3px);color:#06b6d4}.faq-item{transition:all .3s ease}.faq-item:hover{background-color:#f1f5f9cc}.faq-answer{max-height:0;overflow:hidden;transition:max-height .5s cubic-bezier(0,1,0,1)}.faq-answer.active{max-height:1000px;transition:max-height 1s ease-in-out}.faq-icon{transition:transform .3s ease}.faq-icon.active{transform:rotate(45deg)}.newsletter-input{transition:all .3s ease}.newsletter-input:focus{transform:translateY(-2px);box-shadow:0 5px 15px #0000000d}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:#f1f5f9}::-webkit-scrollbar-thumb{background:#cbd5e1;border-radius:4px}::-webkit-scrollbar-thumb:hover{background:#94a3b8}.progress-bar{height:6px;background:#e2e8f0;border-radius:3px;overflow:hidden}.progress-value{height:100%;background:linear-gradient(90deg,#06b6d4,#3b82f6);border-radius:3px;transition:width .5s ease}.stars{display:inline-flex;color:#fbbf24}.level-badge{transition:all .3s ease}.level-badge:hover{transform:scale(1.05)}.pricing-toggle{transition:all .3s ease}.toggle-dot{transition:transform .3s cubic-bezier(.34,1.56,.64,1)}.blob{animation:blob-animation 15s infinite alternate;opacity:.1}@keyframes blob-animation{0%{border-radius:60% 40% 30% 70%/60% 30% 70% 40%}50%{border-radius:30% 60% 70% 40%/50% 60% 30%}to{border-radius:60% 40% 30% 70%/60% 30% 70% 40%}}.path-animation{stroke-dasharray:1000;stroke-dashoffset:1000;animation:dash 3s linear forwards}@keyframes dash{to{stroke-dashoffset:0}}.fill-animation{animation:fillIn 2s ease-in-out forwards;opacity:0}@keyframes fillIn{0%{opacity:0}to{opacity:1}}.rotate-animation{transform-origin:center;animation:rotate 20s linear infinite}@keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.morph-animation{animation:morph 8s ease-in-out infinite alternate}@keyframes morph{0%{d:path("M120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z")}25%{d:path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z")}50%{d:path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,100 160,80 180,100 C200,120 160,140 120,120 Z")}75%{d:path("M120,120 C140,100 180,140 160,160 C140,180 100,140 120,120 C120,100 160,80 180,100 C200,120 160,140 120,120 Z")}to{d:path("M120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 C120,120 120,120 120,120 Z")}}.wave-animation{animation:wave 3s ease-in-out infinite alternate}@keyframes wave{0%{d:path("M0,100 C150,100 150,20 300,20 C450,20 450,100 600,100 L600,200 L0,200 Z")}to{d:path("M0,100 C150,20 150,100 300,100 C450,100 450,20 600,20 L600,200 L0,200 Z")}}.bounce-animation{animation:bounce 2s ease-in-out infinite}@keyframes bounce{0%,to{transform:translateY(0)}50%{transform:translateY(-20px)}}.pulse-glow{filter:drop-shadow(0 0 5px rgba(6,182,212,.3));animation:pulseGlow 3s ease-in-out infinite}@keyframes pulseGlow{0%,to{filter:drop-shadow(0 0 5px rgba(6,182,212,.3))}50%{filter:drop-shadow(0 0 15px rgba(6,182,212,.7))}}.draw-animation{stroke-dasharray:1000;stroke-dashoffset:1000;animation:draw 5s ease-in-out forwards}@keyframes draw{to{stroke-dashoffset:0}}.gradient-animation{animation:gradientShift 5s ease infinite}@keyframes gradientShift{0%{stop-color:#06b6d4}50%{stop-color:#3b82f6}to{stop-color:#06b6d4}}.shake-animation{animation:shake .5s ease-in-out infinite;transform-origin:center}@keyframes shake{0%,to{transform:rotate(0)}25%{transform:rotate(5deg)}75%{transform:rotate(-5deg)}}.pop-animation{animation:pop 2s ease-in-out infinite;transform-origin:center}@keyframes pop{0%,to{transform:scale(1)}50%{transform:scale(1.2)}}.orbit-animation{animation:orbit 10s linear infinite;transform-origin:50% 50%}@keyframes orbit{0%{transform:rotate(0) translate(50px) rotate(0)}to{transform:rotate(360deg) translate(50px) rotate(-360deg)}}.dash-animation{stroke-dasharray:10;animation:dash-move 20s linear infinite}@keyframes dash-move{to{stroke-dashoffset:-1000}}.flicker-animation{animation:flicker 3s linear infinite}@keyframes flicker{0%,19.999%,22%,62.999%,64%,64.999%,70%,to{opacity:1}20%,21.999%,63%,63.999%,65%,69.999%{opacity:.5}}.spin-animation{animation:spin 10s linear infinite;transform-origin:center}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.hover-glow:hover{filter:drop-shadow(0 0 10px rgba(6,182,212,.7));transition:filter .3s ease}.hover-scale{transition:transform .3s ease}.hover-scale:hover{transform:scale(1.1)}.hover-color-shift{transition:fill .3s ease,stroke .3s ease}.hover-color-shift:hover{fill:#3b82f6;stroke:#06b6d4}.notification-badge{position:absolute;top:-5px;right:-5px;width:18px;height:18px;background-color:#ff6b6b;color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:10px;font-weight:700}
