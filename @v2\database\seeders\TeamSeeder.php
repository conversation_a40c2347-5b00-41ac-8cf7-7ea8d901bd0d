<?php

namespace Database\Seeders;

use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Seeder;

class TeamSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default teams with specific IDs
        $teams = [
            [
                'id' => 1,
                'name' => 'Default Team',
                'slug' => 'default-team',
                'description' => 'The default team for the application',
                'is_active' => true,
            ],
            [
                'id' => 2,
                'name' => 'Test School',
                'slug' => 'test-school',
                'description' => 'Test team with full access',
                'is_active' => true,
            ],
        ];

        foreach ($teams as $teamData) {
            Team::updateOrCreate(
                ['id' => $teamData['id']],
                $teamData
            );
        }

        // Assign existing users to the default team if they don't have a team
        $defaultTeam = Team::where('slug', 'default-team')->first();
        
        if ($defaultTeam) {
            User::whereNull('team_id')->update(['team_id' => $defaultTeam->id]);
        }
    }
}
