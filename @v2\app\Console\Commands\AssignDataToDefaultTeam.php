<?php

namespace App\Console\Commands;

use App\Models\Team;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AssignDataToDefaultTeam extends Command
{
    protected $signature = 'tenant:assign-default-team';
    protected $description = 'Assign all existing data to the default team';

    public function handle()
    {
        $defaultTeam = Team::where('slug', 'default-team')->first();
        
        if (!$defaultTeam) {
            $this->error('Default team not found. Please run the TeamSeeder first.');
            return 1;
        }

        $this->info("Assigning data to team: {$defaultTeam->name}");

        // List of tables that need team_id assignment
        $tables = [
            'users',
            'blog_authors',
            'blog_categories', 
            'blog_posts',
            'blog_links',
            'shop_customers',
            'shop_brands',
            'shop_categories',
            'shop_products',
            'shop_orders',
            'shop_order_items',
            'shop_payments',
            'shop_order_addresses',
            'addresses',
            'tags',
            'comments',
            'media',
            'imports',
            'exports',
            'failed_import_rows',
            'notifications',
            'settings',
            'app_settings',
            'shop_category_product',
            'taggables',
            'addressables'
        ];

        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table) && DB::getSchemaBuilder()->hasColumn($table, 'team_id')) {
                $updated = DB::table($table)
                    ->whereNull('team_id')
                    ->update(['team_id' => $defaultTeam->id]);
                
                if ($updated > 0) {
                    $this->info("Updated {$updated} records in {$table}");
                }
            }
        }

        $this->info('Data assignment completed!');
        return 0;
    }
}
