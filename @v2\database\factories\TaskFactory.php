<?php

namespace Database\Factories;

use App\Models\Task;
use Illuminate\Database\Eloquent\Factories\Factory;

class TaskFactory extends Factory
{
    protected $model = Task::class;

    public function definition(): array
    {
        $startDateTime = $this->faker->dateTimeBetween('now', '+1 week');
        $endDateTime = $this->faker->dateTimeBetween($startDateTime, '+2 weeks');
        
        return [
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'start_datetime' => $startDateTime,
            'end_datetime' => $endDateTime,
            'priority' => $this->faker->randomElement(['low', 'medium', 'high', 'urgent']),
            'status' => $this->faker->randomElement(['pending', 'in_progress', 'completed', 'cancelled']),
            'color' => $this->faker->hexColor(),
            'has_alert' => $this->faker->boolean(70),
            'alert_datetime' => $this->faker->dateTimeBetween('now', $startDateTime),
            'alert_minutes_before' => $this->faker->randomElement([15, 30, 60, 120]),
            'is_recurring' => $this->faker->boolean(20),
            'recurring_pattern' => $this->faker->boolean(20) ? [
                'frequency' => $this->faker->randomElement(['daily', 'weekly', 'monthly']),
                'interval' => $this->faker->numberBetween(1, 3),
                'days' => $this->faker->randomElements(['monday', 'tuesday', 'wednesday', 'thursday', 'friday'], $this->faker->numberBetween(1, 3)),
            ] : null,
            'recurring_end_date' => $this->faker->boolean(20) ? $this->faker->dateTimeBetween('+1 month', '+6 months') : null,
            'alert_sound' => $this->faker->boolean(50),
            'alert_email' => $this->faker->boolean(60),
            'alert_line' => $this->faker->boolean(30),
            'is_all_day' => $this->faker->boolean(20),
            'location' => $this->faker->boolean(60) ? $this->faker->address() : null,
            'notes' => $this->faker->boolean(70) ? $this->faker->sentence() : null,
        ];
    }
}
