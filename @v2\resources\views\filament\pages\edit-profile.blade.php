<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Profile Information Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Profile Information
                </h3>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Update your account's profile information and email address.
                </p>
            </div>
            <div class="px-6 py-4">
                {{ $this->profileForm }}

                <div class="mt-6 flex justify-end">
                    <x-filament::button wire:click="updateProfile" color="primary">
                        Update Profile
                    </x-filament::button>
                </div>
            </div>
        </div>

        <!-- Password Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Update Password
                </h3>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Ensure your account is using a long, random password to stay secure.
                </p>
            </div>
            <div class="px-6 py-4">
                {{ $this->passwordForm }}

                <div class="mt-6 flex justify-end">
                    <x-filament::button wire:click="updatePassword" color="warning">
                        Update Password
                    </x-filament::button>
                </div>
            </div>
        </div>

        <!-- Two-Factor Authentication Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Two-Factor Authentication
                </h3>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Add additional security to your account using two-factor authentication.
                </p>
            </div>
            <div class="px-6 py-4">
                {{ $this->twoFactorForm }}

                @if(auth()->user()->two_factor_secret)
                <div class="mt-6 flex justify-end space-x-3">
                    <x-filament::button
                        wire:click="regenerateRecoveryCodes"
                        color="danger"
                        outlined>
                        Regenerate Recovery Codes
                    </x-filament::button>
                </div>
                @endif
            </div>
        </div>

        <!-- Subscription Management Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Subscription Management
                </h3>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Manage your subscription plan and billing information.
                </p>
            </div>
            <div class="px-6 py-4">
                @livewire('subscription-management')
            </div>
        </div>

        <!-- Payment Methods Section -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Payment Methods
                </h3>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Manage your payment methods and billing history.
                </p>
            </div>
            <div class="px-6 py-4">
                @livewire('payment-methods')
            </div>
        </div>
    </div>
</x-filament-panels::page>
