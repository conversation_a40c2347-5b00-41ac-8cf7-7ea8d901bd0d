@extends('layouts.frontend')

@section('title', 'Taking: ' . $exam->title)

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Fixed Header with Timer -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-10">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-lg font-semibold text-gray-900">{{ $exam->title }}</h1>
                    <p class="text-sm text-gray-500">Attempt #{{ $attempt->attempt_number }}</p>
                </div>
                
                @if($exam->time_limit)
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div id="timer" class="text-lg font-mono font-semibold text-orange-600"></div>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Exam Content -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <form id="examForm" method="POST" action="{{ route('frontend.exams.submit', [$exam, $attempt]) }}">
            @csrf
            
            <div class="space-y-8">
                @foreach($exam->questions as $index => $question)
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <!-- Question Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
                                        Question {{ $index + 1 }}
                                    </span>
                                    <span class="text-sm text-gray-500">{{ $question->points }} {{ $question->points == 1 ? 'point' : 'points' }}</span>
                                </div>
                                <div class="text-lg text-gray-900 mb-4">{{ $question->question_text }}</div>
                            </div>
                        </div>

                        <!-- Answer Options -->
                        @if($question->question_type === 'multiple_choice')
                            <div class="space-y-3">
                                @foreach($question->choices as $choice)
                                    <label class="flex items-start space-x-3 p-3 rounded-lg border hover:bg-gray-50 cursor-pointer">
                                        <input type="radio" 
                                               name="answers[{{ $question->id }}][choice_id]" 
                                               value="{{ $choice->id }}"
                                               {{ isset($existingAnswers[$question->id]) && $existingAnswers[$question->id]->selected_choice_id == $choice->id ? 'checked' : '' }}
                                               class="mt-1 text-blue-600 focus:ring-blue-500">
                                        <span class="flex-1 text-gray-900">{{ $choice->choice_text }}</span>
                                    </label>
                                @endforeach
                            </div>
                        @elseif($question->question_type === 'short_answer')
                            <div>
                                <textarea name="answers[{{ $question->id }}][text]" 
                                          rows="3" 
                                          placeholder="Enter your answer here..."
                                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ isset($existingAnswers[$question->id]) ? $existingAnswers[$question->id]->answer_text : '' }}</textarea>
                            </div>
                        @elseif($question->question_type === 'essay')
                            <div>
                                <textarea name="answers[{{ $question->id }}][text]" 
                                          rows="6" 
                                          placeholder="Write your essay here..."
                                          class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ isset($existingAnswers[$question->id]) ? $existingAnswers[$question->id]->answer_text : '' }}</textarea>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>

            <!-- Submit Section -->
            <div class="bg-white rounded-lg shadow-sm p-6 mt-8">
                <div class="text-center">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Ready to Submit?</h3>
                    <p class="text-gray-600 mb-6">Make sure you have answered all questions before submitting. You cannot change your answers after submission.</p>
                    
                    <div class="space-x-4">
                        <button type="button" 
                                onclick="saveDraft()" 
                                class="px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            Save Draft
                        </button>
                        <button type="submit" 
                                onclick="return confirm('Are you sure you want to submit your exam? You cannot change your answers after submission.')"
                                class="px-6 py-3 bg-green-600 text-white font-medium rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                            Submit Exam
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@if($exam->time_limit)
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startTime = new Date('{{ $attempt->started_at->toISOString() }}');
    const timeLimit = {{ $exam->time_limit }} * 60 * 1000; // Convert minutes to milliseconds
    const endTime = new Date(startTime.getTime() + timeLimit);
    
    function updateTimer() {
        const now = new Date();
        const remaining = endTime - now;
        
        if (remaining <= 0) {
            // Time's up - auto submit
            document.getElementById('examForm').submit();
            return;
        }
        
        const minutes = Math.floor(remaining / (1000 * 60));
        const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
        
        const timerElement = document.getElementById('timer');
        timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        // Change color when less than 5 minutes remaining
        if (remaining < 5 * 60 * 1000) {
            timerElement.className = 'text-lg font-mono font-semibold text-red-600';
        }
    }
    
    // Update timer immediately and then every second
    updateTimer();
    setInterval(updateTimer, 1000);
});

function saveDraft() {
    // Auto-save functionality could be implemented here
    alert('Draft saved! (This is a placeholder - implement auto-save if needed)');
}

// Auto-save every 30 seconds
setInterval(function() {
    // Implement auto-save logic here if needed
}, 30000);

// Warn user before leaving page
window.addEventListener('beforeunload', function(e) {
    e.preventDefault();
    e.returnValue = 'Are you sure you want to leave? Your progress may be lost.';
});

// Remove warning when form is submitted
document.getElementById('examForm').addEventListener('submit', function() {
    window.removeEventListener('beforeunload', function() {});
});
</script>
@endif
@endsection
